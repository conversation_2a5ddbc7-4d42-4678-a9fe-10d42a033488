#!/bin/bash

# AI Law Firm - One-Click Startup
# This script starts your complete AI Law Firm system

echo "🚀 Starting AI Law Firm System..."
echo "=================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

if ! command -v python &> /dev/null; then
    echo "❌ Python is not installed. Please install Python first."
    echo "   Visit: https://python.org/"
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Start Docker containers
echo ""
echo "🐳 Starting database containers..."
cd docker

# Start databases first to create the network
docker-compose -f docker-compose.databases.yml up -d

# Wait a moment for network creation
sleep 5

# Then start tools
docker-compose -f docker-compose.tools.yml --profile tools up -d

# Wait for containers to be ready
echo "⏳ Waiting for containers to start..."
sleep 15

# Check if containers are running
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ Some containers failed to start. Check logs:"
    echo "   cd docker && docker-compose logs"
    exit 1
fi

echo "✅ Database containers are running!"

# Start backend
echo ""
echo "🔧 Starting backend server..."
cd ../src

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Setting up Python virtual environment..."
    python -m venv venv
    source venv/bin/activate
    pip install -r ../requirements.txt
else
    source venv/bin/activate
fi

echo "🚀 Launching FastAPI server..."
python main.py &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend..."
sleep 10

# Check if backend is responding
if ! curl -s http://localhost:8000/api/v1/health > /dev/null; then
    echo "⚠️  Backend might not be ready yet, but continuing..."
fi

# Start frontend
echo ""
echo "🎨 Starting frontend..."
cd ../frontend

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

echo "🚀 Launching React development server..."
npm start &
FRONTEND_PID=$!

echo ""
echo "🎉 AI Law Firm System Started Successfully!"
echo "=========================================="
echo ""
echo "📍 Access your system:"
echo "   🌐 Frontend:    http://localhost:3000"
echo "   🔧 Backend:     http://localhost:8000"
echo "   📚 API Docs:    http://localhost:8000/docs"
echo "   🐘 PgAdmin:     http://localhost:50500"
echo "   🍃 MongoDB:     http://localhost:8081"
echo "   🔴 Redis:       http://localhost:8082"
echo ""
echo "🎯 Try the document comparison feature:"
echo "   1. Go to http://localhost:3000"
echo "   2. Upload 2 legal documents"
echo "   3. Select both documents"
echo "   4. Click 'Compare' button"
echo ""
echo "🛑 To stop everything:"
echo "   docker-compose -f docker/docker-compose.databases.yml -f docker/docker-compose.tools.yml --profile tools down"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo "📝 Process IDs:"
echo "   Backend:  $BACKEND_PID"
echo "   Frontend: $FRONTEND_PID"
echo ""
echo "🚀 Happy legal AI exploring!"

# Keep script running
trap "echo '🛑 Shutting down...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; cd docker && docker-compose -f docker-compose.databases.yml -f docker-compose.tools.yml --profile tools down; exit" INT

# Wait for user interrupt
wait