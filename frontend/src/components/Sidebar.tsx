import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  MessageSquare,
  Search,
  Upload,
  FileText,
  GitCompare,
  Users,
  BarChart3,
  Settings,
  Scale
} from 'lucide-react'

const Sidebar: React.FC = () => {
  const location = useLocation()

  const menuItems = [
    { path: '/', icon: Home, label: 'Dashboard' },
    { path: '/chat', icon: MessageSquare, label: 'Legal Chat' },
    { path: '/search', icon: Search, label: 'Search Documents' },
    { path: '/upload', icon: Upload, label: 'Upload Documents' },
    { path: '/documents', icon: FileText, label: 'Document Library' },
    { path: '/compare', icon: GitCompare, label: 'Compare Documents' },
    { path: '/agents', icon: Users, label: 'Agent Management' },
    { path: '/monitoring', icon: BarChart3, label: 'Monitoring' },
    { path: '/settings', icon: Settings, label: 'Settings' },
  ]

  return (
    <div className="w-64 bg-white shadow-lg">
      <div className="flex items-center justify-center h-16 px-4 bg-legal-600">
        <Scale className="w-8 h-8 text-white mr-2" />
        <h1 className="text-xl font-bold text-white">AI Law Firm</h1>
      </div>

      <nav className="mt-8">
        <div className="px-4 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon
            const isActive = location.pathname === item.path

            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  isActive
                    ? 'bg-legal-100 text-legal-700 border-r-4 border-legal-500'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <Icon className="w-5 h-5 mr-3" />
                {item.label}
              </Link>
            )
          })}
        </div>
      </nav>

      <div className="absolute bottom-0 w-64 p-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <p className="text-xs text-gray-500 mb-1">System Status</p>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span className="text-sm text-gray-700">All Systems Operational</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Sidebar