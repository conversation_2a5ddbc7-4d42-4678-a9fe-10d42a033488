import React, { useState } from 'react'
import { <PERSON>, EyeOff, X, Co<PERSON>, Check } from 'lucide-react'

interface ApiKeyInputProps {
  label: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  fieldId?: string
  currentlyEditing?: string | null
  onEditingChange?: (fieldId: string | null) => void
}

const ApiKeyInput: React.FC<ApiKeyInputProps> = ({
  label,
  value,
  onChange,
  placeholder = '',
  className = '',
  fieldId,
  currentlyEditing,
  onEditingChange
}) => {
  const [showPassword, setShowPassword] = useState(false)
  const [copied, setCopied] = useState(false)
  const [actualKeyValue, setActualKeyValue] = useState<string>('')

  // Check if this is a stored API key
  const isStoredKey = value === 'STORED_IN_DB'
  const displayValue = isStoredKey && !showPassword
    ? '••••••••••••••••••••••••••••••••'
    : isStoredKey && showPassword
    ? actualKeyValue || 'Loading...'
    : value || ''

  const toggleVisibility = async () => {
    if (isStoredKey) {
      if (!showPassword) {
        // Show the actual key - fetch it from backend if not already loaded
        if (!actualKeyValue) {
          try {
            // Fetch the actual key from backend
            const response = await fetch('/api/v1/config')
            const config = await response.json()
            // Extract the actual key from the config based on fieldId
            let actualKey = ''
            if (fieldId === 'openai' && config.openai?.api_key && config.openai.api_key !== 'STORED_IN_DB') {
              actualKey = config.openai.api_key
            } else if (fieldId === 'anthropic' && config.anthropic?.api_key && config.anthropic.api_key !== 'STORED_IN_DB') {
              actualKey = config.anthropic.api_key
            } else if (fieldId === 'huggingface' && config.huggingface?.api_key && config.huggingface.api_key !== 'STORED_IN_DB') {
              actualKey = config.huggingface.api_key
            } else if (fieldId === 'deepseek' && config.deepseek?.api_key && config.deepseek.api_key !== 'STORED_IN_DB') {
              actualKey = config.deepseek.api_key
            } else if (fieldId === 'openrouter' && config.openrouter?.api_key && config.openrouter.api_key !== 'STORED_IN_DB') {
              actualKey = config.openrouter.api_key
            }
            setActualKeyValue(actualKey)
          } catch (error) {
            console.error('Failed to fetch actual key:', error)
            setActualKeyValue('Error loading key')
          }
        }
      }
      setShowPassword(!showPassword)
    } else {
      setShowPassword(!showPassword)
    }
  }

  const clearInput = () => {
    onChange('')
    setActualKeyValue('')
    setShowPassword(false)
    onEditingChange?.(null)
  }

  const copyToClipboard = async () => {
    const textToCopy = isStoredKey && showPassword ? actualKeyValue : value

    if (textToCopy && textToCopy !== '••••••••••••••••••••••••••••••••' && textToCopy !== 'Loading...' && textToCopy !== 'Error loading key') {
      try {
        await navigator.clipboard.writeText(textToCopy)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy text: ', err)
      }
    } else if (isStoredKey && !showPassword) {
      // For stored keys when hidden, show a message
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <div className="relative">
        <input
          type={showPassword || (!isStoredKey && value) ? 'text' : 'password'}
          value={displayValue}
          onChange={(e) => onChange(e.target.value)}
          className={`w-full px-3 py-2 pr-24 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent ${
            isStoredKey && !showPassword ? 'bg-gray-50 text-gray-500' : ''
          }`}
          placeholder={isStoredKey && !showPassword ? 'Click eye to show key' : placeholder}
          readOnly={isStoredKey && !showPassword}
        />
        <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-2">
          {/* Copy button */}
          <button
            type="button"
            onClick={copyToClipboard}
            disabled={!value && !isStoredKey}
            className={`p-1 rounded-md transition-colors ${
              copied
                ? 'text-green-600 hover:text-green-700'
                : isStoredKey
                ? 'text-blue-500 hover:text-blue-600'
                : 'text-gray-400 hover:text-gray-600'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
            title={copied ? 'Copied!' : isStoredKey ? 'Key stored securely' : 'Copy to clipboard'}
          >
            {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
          </button>

          {/* Clear button */}
          <button
            type="button"
            onClick={clearInput}
            disabled={!value && !isStoredKey}
            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Clear input"
          >
            <X className="w-4 h-4" />
          </button>

          {/* Eye button */}
          <button
            type="button"
            onClick={toggleVisibility}
            className={`p-1 ${isStoredKey ? 'text-blue-400' : 'text-gray-400'} hover:text-gray-600`}
            title={
              isStoredKey
                ? (showPassword ? 'Hide API key' : 'Show API key')
                : (showPassword ? 'Hide password' : 'Show password')
            }
          >
            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ApiKeyInput