import { Document } from '../types';

interface DocumentPanelProps {
  documents: Document[];
  onUpload: (file: File) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  isUploading: boolean;
}

export function DocumentPanel({ documents, onUpload: _onUpload, onDelete: _onDelete, isUploading }: DocumentPanelProps) {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Document Management</h2>
        <p className="text-gray-600 dark:text-gray-400">Upload and manage your legal documents</p>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <p className="text-gray-500 dark:text-gray-400">
          Document management interface will be implemented here.
        </p>
        <p className="text-sm text-gray-400 mt-2">
          Documents: {documents.length} | Uploading: {isUploading ? 'Yes' : 'No'}
        </p>
      </div>
    </div>
  );
}