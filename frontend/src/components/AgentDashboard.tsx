import { Agent, AgentStats } from '../types';

interface AgentDashboardProps {
  agents: Agent[];
  stats: AgentStats;
  onRefresh: () => void;
}

export function AgentDashboard({ agents, stats, onRefresh }: AgentDashboardProps) {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Agent Dashboard</h2>
        <p className="text-gray-600 dark:text-gray-400">Monitor your AI legal specialists</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="text-2xl font-bold text-blue-600">{stats.totalAgents}</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Total Agents</div>
        </div>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="text-2xl font-bold text-green-600">{stats.activeAgents}</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Active</div>
        </div>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="text-2xl font-bold text-gray-600">{stats.idleAgents}</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Idle</div>
        </div>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="text-2xl font-bold text-red-600">{stats.errorAgents}</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Errors</div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Legal Agents</h3>
            <button
              onClick={onRefresh}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Refresh
            </button>
          </div>
        </div>
        <div className="p-4">
          {agents.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">No agents available</p>
          ) : (
            <div className="space-y-3">
              {agents.map((agent) => (
                <div key={agent.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{agent.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{agent.specialization}</p>
                  </div>
                  <div className="text-right">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      agent.status === 'idle' ? 'bg-green-100 text-green-800' :
                      agent.status === 'busy' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {agent.status}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {agent.totalTasks} tasks • {(agent.successRate * 100).toFixed(1)}% success
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}