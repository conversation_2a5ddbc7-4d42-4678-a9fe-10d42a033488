import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Bell, Search, User, X } from 'lucide-react'
import { api } from '../api/client'

const Header: React.FC = () => {
  const navigate = useNavigate()
  const [showNotifications, setShowNotifications] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [isSearching, setIsSearching] = useState(false)

  // Sample notifications - in a real app, these would come from an API
  const notifications = [
    {
      id: 1,
      title: 'Document Analysis Complete',
      message: 'Contract analysis for NDA-2024.pdf has been completed',
      time: '2 minutes ago',
      unread: true
    },
    {
      id: 2,
      title: 'System Health Alert',
      message: 'All systems are running optimally',
      time: '5 minutes ago',
      unread: false
    },
    {
      id: 3,
      title: 'New GPT-5 Models Available',
      message: 'GPT-5-nano, GPT-5-mini, and GPT-5 are now available',
      time: '1 hour ago',
      unread: false
    }
  ]

  const unreadCount = notifications.filter(n => n.unread).length

  // Search handler
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      setShowSearchResults(false)
      return
    }

    setIsSearching(true)
    try {
      const response = await api.searchDocuments({ query, limit: 5 })
      setSearchResults(response.results || [])
      setShowSearchResults(true)
    } catch (error) {
      console.error('Search failed:', error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  // Handle search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)

    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch(query)
    }, 300)

    return () => clearTimeout(timeoutId)
  }

  // Handle search result click
  const handleSearchResultClick = (documentId: string) => {
    setShowSearchResults(false)
    setSearchQuery('')
    // Navigate to document view or analysis page
    navigate(`/documents/${documentId}`)
  }

  // Handle search submit (Enter key)
  const handleSearchSubmit = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && searchQuery.trim()) {
      setShowSearchResults(false)
      // Navigate to search results page with query
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  // Close dropdowns when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showSearchResults && !(event.target as Element).closest('.search-dropdown')) {
        setShowSearchResults(false)
      }
      if (showNotifications && !(event.target as Element).closest('.notifications-dropdown')) {
        setShowNotifications(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showSearchResults, showNotifications])

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex-1 flex items-center">
            <div className="max-w-lg w-full lg:max-w-xs">
              <label htmlFor="search" className="sr-only">
                Search
              </label>
              <div className="relative search-dropdown">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="search"
                  name="search"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  onKeyDown={handleSearchSubmit}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-legal-500 focus:border-legal-500 sm:text-sm"
                  placeholder="Search documents, cases, or legal queries..."
                  type="search"
                />
              </div>

              {/* Search Results Dropdown */}
              {showSearchResults && (searchResults.length > 0 || isSearching) && (
                <div className="absolute top-full mt-1 w-full bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50 max-h-80 overflow-y-auto">
                  {isSearching ? (
                    <div className="px-4 py-3 text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-legal-500 mr-2"></div>
                        Searching...
                      </div>
                    </div>
                  ) : searchResults.length > 0 ? (
                    <div>
                      {searchResults.map((result, index) => (
                        <div
                          key={result.document_id || index}
                          onClick={() => handleSearchResultClick(result.document_id)}
                          className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {result.title || 'Untitled Document'}
                              </p>
                              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                                {result.content?.substring(0, 100) || 'No preview available'}...
                              </p>
                              <div className="flex items-center mt-1 space-x-2">
                                <span className="text-xs text-gray-400">
                                  Score: {result.score?.toFixed(2) || 'N/A'}
                                </span>
                                {result.metadata?.document_type && (
                                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                    {result.metadata.document_type}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                      <div className="px-4 py-2 border-t border-gray-200">
                        <button
                          onClick={() => {
                            setShowSearchResults(false)
                            navigate(`/search?q=${encodeURIComponent(searchQuery)}`)
                          }}
                          className="text-sm text-legal-600 hover:text-legal-800 font-medium"
                        >
                          View all results
                        </button>
                      </div>
                    </div>
                  ) : searchQuery && !isSearching ? (
                    <div className="px-4 py-3 text-sm text-gray-500">
                      No results found for "{searchQuery}"
                    </div>
                  ) : null}
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <div className="relative">
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-legal-500 relative"
              >
                <span className="sr-only">View notifications</span>
                <Bell className="h-6 w-6" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
                    {unreadCount}
                  </span>
                )}
              </button>

              {/* Notifications Dropdown */}
              {showNotifications && (
                <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50 notifications-dropdown">
                  <div className="py-1">
                    <div className="px-4 py-2 border-b border-gray-200">
                      <h3 className="text-sm font-medium text-gray-900">Notifications</h3>
                    </div>
                    <div className="max-h-64 overflow-y-auto">
                      {notifications.length === 0 ? (
                        <div className="px-4 py-3 text-sm text-gray-500">
                          No notifications
                        </div>
                      ) : (
                        notifications.map((notification) => (
                          <div key={notification.id} className="px-4 py-3 border-b border-gray-100 last:border-b-0">
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {notification.title}
                                </p>
                                <p className="text-sm text-gray-600 mt-1">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-gray-400 mt-1">
                                  {notification.time}
                                </p>
                              </div>
                              {notification.unread && (
                                <div className="ml-2 flex-shrink-0">
                                  <div className="w-2 h-2 bg-legal-500 rounded-full"></div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                    <div className="px-4 py-2 border-t border-gray-200">
                      <button className="text-sm text-legal-600 hover:text-legal-800 font-medium">
                        View all notifications
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Profile dropdown */}
            <div className="relative">
              <button className="bg-white flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-legal-500">
                <span className="sr-only">Open user menu</span>
                <div className="h-8 w-8 rounded-full bg-legal-500 flex items-center justify-center">
                  <User className="h-5 w-5 text-white" />
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header