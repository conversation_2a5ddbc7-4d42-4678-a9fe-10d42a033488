import React, { useState, useRef, useEffect } from 'react';
import { PaperAirplaneIcon, DocumentIcon, XMarkIcon, ArrowsRightLeftIcon } from '@heroicons/react/24/outline';
import { Document } from '../types';
import { DocumentComparison } from './DocumentComparison';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  agent?: string;
  confidence?: number;
  sources?: any[];
}

interface ChatInterfaceProps {
  messages: Message[];
  isLoading: boolean;
  onSendMessage: (message: string, documents?: string[]) => void;
  onClearMessages: () => void;
  selectedDocuments: string[];
  onDocumentSelect: (documentIds: string[]) => void;
  availableDocuments: Document[];
}

type ViewMode = 'chat' | 'comparison';

export function ChatInterface({
  messages,
  isLoading,
  onSendMessage,
  onClearMessages,
  selectedDocuments,
  onDocumentSelect,
  availableDocuments
}: ChatInterfaceProps) {
  const [input, setInput] = useState('');
  const [showDocumentSelector, setShowDocumentSelector] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('chat');
  const [comparisonDocuments, setComparisonDocuments] = useState<[Document, Document] | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      onSendMessage(input.trim(), selectedDocuments);
      setInput('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const toggleDocument = (docId: string) => {
    const newSelected = selectedDocuments.includes(docId)
      ? selectedDocuments.filter(id => id !== docId)
      : [...selectedDocuments, docId];
    onDocumentSelect(newSelected);
  };

  const selectedDocs = availableDocuments.filter(doc => selectedDocuments.includes(doc.id));

  const handleCompareDocuments = () => {
    if (selectedDocuments.length === 2) {
      const [leftId, rightId] = selectedDocuments;
      const leftDoc = availableDocuments.find(doc => doc.id === leftId);
      const rightDoc = availableDocuments.find(doc => doc.id === rightId);

      if (leftDoc && rightDoc) {
        setComparisonDocuments([leftDoc, rightDoc]);
        setViewMode('comparison');
      }
    }
  };

  const handleCloseComparison = () => {
    setViewMode('chat');
    setComparisonDocuments(null);
  };

  const handleAskAIAboutComparison = (question: string, context: string) => {
    // Send the question with comparison context to the chat
    onSendMessage(`Context: ${context}\n\nQuestion: ${question}`, selectedDocuments);
    handleCloseComparison();
  };

  if (viewMode === 'comparison' && comparisonDocuments) {
    return (
      <DocumentComparison
        leftDocument={comparisonDocuments[0]}
        rightDocument={comparisonDocuments[1]}
        onClose={handleCloseComparison}
        onAskAI={handleAskAIAboutComparison}
      />
    );
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
            AI Law Firm Assistant
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Ask legal questions and analyze documents with AI
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedDocuments.length > 0 && (
            <div className="flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 rounded-full">
              <DocumentIcon className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-700 dark:text-blue-300">
                {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''} selected
              </span>
            </div>
          )}

          {selectedDocuments.length === 2 && (
            <button
              onClick={handleCompareDocuments}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              title="Compare selected documents"
            >
              <ArrowsRightLeftIcon className="w-4 h-4" />
              <span>Compare</span>
            </button>
          )}

          <button
            onClick={onClearMessages}
            className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            Clear Chat
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-2xl text-white">⚖️</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Welcome to AI Law Firm
              </h3>
              <p className="text-gray-500 dark:text-gray-400 max-w-md">
                Ask questions about legal documents, get analysis, or request summaries.
                Upload documents first for context-aware responses.
              </p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-3xl px-4 py-3 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                }`}
              >
                <div className="whitespace-pre-wrap">{message.content}</div>
                {message.agent && (
                  <div className="mt-2 text-xs opacity-70">
                    Agent: {message.agent}
                    {message.confidence && ` • Confidence: ${(message.confidence * 100).toFixed(1)}%`}
                  </div>
                )}
                <div className="mt-1 text-xs opacity-50">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))
        )}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 dark:bg-gray-700 px-4 py-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Analyzing...</span>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Document Selector */}
      {showDocumentSelector && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-900 dark:text-white">Select Documents for Context</h4>
            <button
              onClick={() => setShowDocumentSelector(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
          {selectedDocuments.length === 2 && (
            <div className="mb-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center space-x-2 text-green-800 dark:text-green-200">
                <ArrowsRightLeftIcon className="w-4 h-4" />
                <span className="text-sm font-medium">Ready to compare!</span>
              </div>
              <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                Select "Compare" in the header to view differences side-by-side
              </p>
            </div>
          )}

          {selectedDocuments.length > 2 && (
            <div className="mb-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-center space-x-2 text-yellow-800 dark:text-yellow-200">
                <DocumentIcon className="w-4 h-4" />
                <span className="text-sm font-medium">Too many documents selected</span>
              </div>
              <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                Select exactly 2 documents to enable comparison mode
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto">
            {availableDocuments.map((doc) => (
              <div
                key={doc.id}
                onClick={() => toggleDocument(doc.id)}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedDocuments.includes(doc.id)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                }`}
              >
                <div className="flex items-start space-x-2">
                  <DocumentIcon className="w-4 h-4 mt-0.5 text-gray-400" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {doc.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {doc.type} • {(doc.size / 1024 / 1024).toFixed(1)}MB
                    </p>
                  </div>
                  {selectedDocuments.includes(doc.id) && (
                    <div className="flex items-center justify-center w-5 h-5 bg-blue-500 rounded-full">
                      <span className="text-xs text-white font-medium">
                        {selectedDocuments.indexOf(doc.id) + 1}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <form onSubmit={handleSubmit} className="flex items-end space-x-3">
          <div className="flex-1">
            <div className="relative">
              <textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask a legal question or request document analysis..."
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                rows={1}
                style={{ minHeight: '48px', maxHeight: '120px' }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = 'auto';
                  target.style.height = Math.min(target.scrollHeight, 120) + 'px';
                }}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setShowDocumentSelector(!showDocumentSelector)}
              className={`p-2 rounded-lg transition-colors ${
                selectedDocuments.length > 0
                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              }`}
              title="Select documents for context"
            >
              <DocumentIcon className="w-5 h-5" />
            </button>

            <button
              type="submit"
              disabled={!input.trim() || isLoading}
              className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <PaperAirplaneIcon className="w-5 h-5" />
            </button>
          </div>
        </form>

        {selectedDocs.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            {selectedDocs.map((doc) => (
              <div
                key={doc.id}
                className="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
              >
                <DocumentIcon className="w-3 h-3 mr-1" />
                {doc.title}
                <button
                  onClick={() => toggleDocument(doc.id)}
                  className="ml-1 hover:text-blue-600"
                >
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}