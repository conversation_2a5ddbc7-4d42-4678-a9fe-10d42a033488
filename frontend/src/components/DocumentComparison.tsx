import { useState, useEffect, useRef } from 'react';
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { Document } from '../types';

interface DiffLine {
  lineNumber: number;
  type: 'added' | 'removed' | 'modified' | 'unchanged';
  content: string;
  originalLine?: number;
  newLine?: number;
}

interface ComparisonResult {
  differences: DiffLine[];
  summary: {
    totalLines: number;
    addedLines: number;
    removedLines: number;
    modifiedLines: number;
    similarity: number;
  };
  aiAnalysis?: string;
}

interface DocumentComparisonProps {
  leftDocument: Document;
  rightDocument: Document;
  onClose: () => void;
  onAskAI: (question: string, context: string) => void;
}

export function DocumentComparison({
  leftDocument,
  rightDocument,
  onClose,
  onAskAI
}: DocumentComparisonProps) {
  const [comparison, setComparison] = useState<ComparisonResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentDiffIndex, setCurrentDiffIndex] = useState(0);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [viewMode, setViewMode] = useState<'split' | 'unified'>('split');
  const [aiQuestion, setAiQuestion] = useState('');
  const leftRef = useRef<HTMLDivElement>(null);
  const rightRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadComparison();
  }, [leftDocument, rightDocument]);

  const loadComparison = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/compare/detailed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document_ids: [leftDocument.id, rightDocument.id]
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      const comparisonResult: ComparisonResult = {
        differences: data.differences || [],
        summary: data.summary || {
          totalLines: 0,
          addedLines: 0,
          removedLines: 0,
          modifiedLines: 0,
          similarity: 0,
        },
        aiAnalysis: data.aiAnalysis || 'AI analysis not available.',
      };

      setComparison(comparisonResult);
    } catch (error) {
      console.error('Error loading comparison:', error);

      // Fallback to mock data if API fails
      const mockComparison: ComparisonResult = {
        differences: [
          {
            lineNumber: 1,
            type: 'unchanged',
            content: 'This Agreement is entered into on [DATE] by and between:',
          },
          {
            lineNumber: 2,
            type: 'modified',
            content: 'Party A: [ORIGINAL PARTY NAME]',
            originalLine: 2,
            newLine: 2,
          },
          {
            lineNumber: 3,
            type: 'added',
            content: 'Party B: [NEW PARTY NAME ADDED]',
          },
          {
            lineNumber: 4,
            type: 'removed',
            content: 'Party C: [REMOVED PARTY]',
          },
          {
            lineNumber: 5,
            type: 'unchanged',
            content: 'WHEREAS, the parties desire to enter into this agreement;',
          },
        ],
        summary: {
          totalLines: 100,
          addedLines: 12,
          removedLines: 8,
          modifiedLines: 15,
          similarity: 0.75,
        },
        aiAnalysis: `This document comparison shows significant changes to the party structure. The addition of Party B suggests an expansion of the agreement scope, while the removal of Party C indicates a consolidation. The modified party name on line 2 may reflect a corporate name change or correction. Overall similarity of 75% indicates substantial but not complete overlap between versions.`,
      };

      setComparison(mockComparison);
    } finally {
      setLoading(false);
    }
  };

  const navigateToDiff = (direction: 'prev' | 'next') => {
    if (!comparison) return;

    const diffs = comparison.differences.filter(d => d.type !== 'unchanged');
    if (diffs.length === 0) return;

    let newIndex = currentDiffIndex;
    if (direction === 'next') {
      newIndex = (currentDiffIndex + 1) % diffs.length;
    } else {
      newIndex = currentDiffIndex === 0 ? diffs.length - 1 : currentDiffIndex - 1;
    }

    setCurrentDiffIndex(newIndex);

    // Scroll to the diff
    const targetLine = diffs[newIndex].lineNumber;
    const element = document.querySelector(`[data-line="${targetLine}"]`);
    element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };

  const handleAskAI = () => {
    if (!aiQuestion.trim() || !comparison) return;

    const context = `Comparing documents: "${leftDocument.title}" vs "${rightDocument.title}". Key differences: ${comparison.differences.filter(d => d.type !== 'unchanged').length} changes found. ${comparison.aiAnalysis}`;

    onAskAI(aiQuestion, context);
    setAiQuestion('');
  };

  const renderDiffLine = (line: DiffLine, isLeft: boolean) => {
    const isCurrentDiff = comparison?.differences.filter(d => d.type !== 'unchanged')[currentDiffIndex]?.lineNumber === line.lineNumber;

    return (
      <div
        key={`${isLeft ? 'left' : 'right'}-${line.lineNumber}`}
        data-line={line.lineNumber}
        className={`flex items-center px-2 py-1 text-sm font-mono border-l-2 ${
          line.type === 'added' ? 'bg-green-50 border-green-400 text-green-800' :
          line.type === 'removed' ? 'bg-red-50 border-red-400 text-red-800' :
          line.type === 'modified' ? 'bg-yellow-50 border-yellow-400 text-yellow-800' :
          'border-gray-200'
        } ${isCurrentDiff ? 'ring-2 ring-blue-400' : ''}`}
      >
        {showLineNumbers && (
          <span className="w-8 text-right text-gray-400 mr-2 select-none">
            {line.lineNumber}
          </span>
        )}
        <span className="flex-1 whitespace-pre-wrap">
          {line.type === 'added' && !isLeft && '+ '}
          {line.type === 'removed' && isLeft && '- '}
          {line.content}
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <ArrowPathIcon className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Comparing documents...</p>
        </div>
      </div>
    );
  }

  if (!comparison) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-gray-600">Failed to load comparison</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <ArrowLeftIcon className="w-5 h-5" />
          </button>
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Document Comparison
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {leftDocument.title} ↔ {rightDocument.title}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Similarity: {(comparison.summary.similarity * 100).toFixed(1)}%
          </div>
          <div className="flex items-center space-x-1 text-xs">
            <span className="flex items-center text-green-600">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
              +{comparison.summary.addedLines}
            </span>
            <span className="flex items-center text-red-600">
              <span className="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
              -{comparison.summary.removedLines}
            </span>
            <span className="flex items-center text-yellow-600">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></span>
              ~{comparison.summary.modifiedLines}
            </span>
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigateToDiff('prev')}
            className="p-1 rounded text-gray-600 hover:text-gray-900 hover:bg-gray-200"
            title="Previous difference"
          >
            <ArrowLeftIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => navigateToDiff('next')}
            className="p-1 rounded text-gray-600 hover:text-gray-900 hover:bg-gray-200"
            title="Next difference"
          >
            <ArrowRightIcon className="w-4 h-4" />
          </button>
          <span className="text-sm text-gray-600">
            {currentDiffIndex + 1} of {comparison.differences.filter(d => d.type !== 'unchanged').length} differences
          </span>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode(viewMode === 'split' ? 'unified' : 'split')}
            className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50"
          >
            {viewMode === 'split' ? 'Unified' : 'Split'} View
          </button>
          <button
            onClick={() => setShowLineNumbers(!showLineNumbers)}
            className={`p-1 rounded ${showLineNumbers ? 'text-blue-600 bg-blue-50' : 'text-gray-600 hover:text-gray-900'}`}
            title="Toggle line numbers"
          >
            <MagnifyingGlassIcon className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Comparison View */}
      <div className="flex-1 flex overflow-hidden">
        {viewMode === 'split' ? (
          <>
            {/* Left Document */}
            <div className="flex-1 border-r border-gray-200 dark:border-gray-700 overflow-auto">
              <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="flex items-center space-x-2">
                  <DocumentTextIcon className="w-4 h-4 text-gray-600" />
                  <span className="font-medium text-gray-900 dark:text-white">{leftDocument.title}</span>
                </div>
              </div>
              <div ref={leftRef} className="p-2">
                {comparison.differences.map(line => renderDiffLine(line, true))}
              </div>
            </div>

            {/* Right Document */}
            <div className="flex-1 overflow-auto">
              <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="flex items-center space-x-2">
                  <DocumentTextIcon className="w-4 h-4 text-gray-600" />
                  <span className="font-medium text-gray-900 dark:text-white">{rightDocument.title}</span>
                </div>
              </div>
              <div ref={rightRef} className="p-2">
                {comparison.differences.map(line => renderDiffLine(line, false))}
              </div>
            </div>
          </>
        ) : (
          /* Unified View */
          <div className="flex-1 overflow-auto">
            <div className="p-2">
              {comparison.differences.map(line => (
                <div
                  key={`unified-${line.lineNumber}`}
                  data-line={line.lineNumber}
                  className={`flex items-center px-2 py-1 text-sm font-mono border-l-2 ${
                    line.type === 'added' ? 'bg-green-50 border-green-400 text-green-800' :
                    line.type === 'removed' ? 'bg-red-50 border-red-400 text-red-800' :
                    line.type === 'modified' ? 'bg-yellow-50 border-yellow-400 text-yellow-800' :
                    'border-gray-200'
                  }`}
                >
                  {showLineNumbers && (
                    <span className="w-8 text-right text-gray-400 mr-2 select-none">
                      {line.lineNumber}
                    </span>
                  )}
                  <span className="w-8 text-center mr-2 select-none">
                    {line.type === 'added' ? '+' :
                     line.type === 'removed' ? '-' :
                     line.type === 'modified' ? '~' : ' '}
                  </span>
                  <span className="flex-1 whitespace-pre-wrap">{line.content}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* AI Analysis Panel */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800">
        <div className="mb-3">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">AI Analysis</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {comparison.aiAnalysis}
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex-1">
            <input
              type="text"
              value={aiQuestion}
              onChange={(e) => setAiQuestion(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleAskAI()}
              placeholder="Ask AI about these document differences..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <button
            onClick={handleAskAI}
            disabled={!aiQuestion.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
          >
            <ChatBubbleLeftRightIcon className="w-4 h-4" />
            <span>Ask AI</span>
          </button>
        </div>
      </div>
    </div>
  );
}