import { Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'sonner'

// Components
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import LegalChat from './pages/LegalChat'
import DocumentUpload from './pages/DocumentUpload'
import DocumentLibrary from './pages/DocumentLibrary'
import DocumentComparison from './pages/DocumentComparison'
import AgentManagement from './pages/AgentManagement'
import Monitoring from './pages/Monitoring'
import Settings from './pages/Settings'
import Search from './pages/Search'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="chat" element={<LegalChat />} />
            <Route path="search" element={<Search />} />
            <Route path="upload" element={<DocumentUpload />} />
            <Route path="documents" element={<DocumentLibrary />} />
            <Route path="compare" element={<DocumentComparison />} />
            <Route path="agents" element={<AgentManagement />} />
            <Route path="monitoring" element={<Monitoring />} />
            <Route path="settings" element={<Settings />} />
          </Route>
        </Routes>
        <Toaster position="top-right" />
      </div>
    </QueryClientProvider>
  )
}

export default App