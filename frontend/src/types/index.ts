export interface Document {
  id: string;
  title: string;
  type: string;
  size: number;
  uploadDate: string;
  status: 'processing' | 'completed' | 'error';
  chunks?: number;
  metadata?: Record<string, any>;
}

export interface Agent {
  id: string;
  name: string;
  specialization: string;
  status: 'idle' | 'busy' | 'error';
  domain: string;
  expertiseLevel: string;
  totalTasks: number;
  successRate: number;
  averageResponseTime: number;
}

export interface AgentStats {
  totalAgents: number;
  activeAgents: number;
  idleAgents: number;
  errorAgents: number;
  averageResponseTime: number;
  totalTasksProcessed: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  agent?: string;
  confidence?: number;
  sources?: any[];
}

export interface ChatState {
  messages: Message[];
  isLoading: boolean;
  selectedDocuments: string[];
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface QueryRequest {
  query: string;
  documentIds?: string[];
  context?: Record<string, any>;
}

export interface QueryResponse {
  response: string;
  confidence: number;
  sources: any[];
  processingTime: number;
  modelUsed: string;
  agentUsed?: string;
}

export interface DocumentUploadResponse {
  documentId: string;
  status: string;
  chunksCreated: number;
  message: string;
}

export interface HealthStatus {
  overall_health: string;
  services: Record<string, {
    status: string;
    responseTime?: number;
    lastCheck: string;
    errorMessage?: string;
  }>;
  active_requests?: number;
  metrics_summary?: any;
  timestamp?: string;
}