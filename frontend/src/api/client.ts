import axios, { AxiosInstance } from 'axios'
import { CONFIG } from '../config/constants'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: CONFIG.API.BASE_URL,
  timeout: CONFIG.API.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for auth tokens
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// TypeScript interfaces for API responses
export interface SystemInfo {
  version: string
  environment: string
  ai_provider: string
  vector_database: string
  debug_mode: boolean
  features: {
    agents: boolean
    documents: boolean
    knowledge_base: boolean
    search: boolean
    classification: boolean
    comparison: boolean
  }
}

export interface HealthStatus {
  overall_health: string
  services: Record<string, {
    status: string
    response_time: number | null
    last_check: string
    error_message: string | null
  }>
  active_requests: number
  metrics_summary: {
    api_requests: { count: number; average: number; min: number; max: number; latest: number }
    ai_operations: { count: number; average: number; min: number; max: number }
    db_operations: { count: number; average: number; min: number; max: number }
  }
}

export interface AgentInfo {
  agents: {
    total_agents: number
    active_routes: number
    routing_strategy: string
    load_distribution: Record<string, number>
    domain_statistics: Record<string, number>
    average_load_score: number
    total_queued_tasks: number
    last_updated: string
  }
}

export interface DocumentInfo {
  document_id: string
  title: string
  content: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface QueryRequest {
  query: string
  document_ids?: string[]
  context?: Record<string, any>
}

export interface QueryResponse {
  response: string
  confidence: number
  sources: string[]
  processing_time: number
  model_used: string
  cached: boolean
}

export interface DocumentUploadRequest {
  title: string
  document_type: string
  tags?: string[]
  metadata?: Record<string, any>
}

export interface DocumentUploadResponse {
  document_id: string
  status: string
  chunks_created: number
  message: string
}

export interface SearchRequest {
  query: string
  filters?: Record<string, any>
  limit?: number
}

export interface SearchResult {
  document_id: string
  title: string
  content: string
  metadata: Record<string, any>
  score: number
}

export interface SearchResponse {
  query: string
  results: SearchResult[]
  total_found: number
}

export interface ComparisonRequest {
  document_ids: string[]
}

export interface ComparisonResponse {
  differences: Array<{
    lineNumber: number
    type: 'added' | 'removed' | 'modified' | 'unchanged'
    content: string
    originalLine?: number
    newLine?: number
  }>
  summary: {
    totalLines: number
    addedLines: number
    removedLines: number
    modifiedLines: number
    similarity: number
  }
  aiAnalysis: string
  documents: {
    left: { id: string; title: string; type: string }
    right: { id: string; title: string; type: string }
  }
}

// API Client class
class APIClient {
  private client: AxiosInstance

  constructor() {
    this.client = apiClient
  }

  // System endpoints
  async getSystemInfo(): Promise<SystemInfo> {
    const response = await this.client.get('/api/v1/system/info')
    return response.data
  }

  async getSystemConfig(): Promise<any> {
    const response = await this.client.get('/api/v1/system/config')
    return response.data
  }

  // Configuration endpoints
  async getConfig(): Promise<any> {
    console.log('🌐 [DEBUG] API Client: Calling GET /api/v1/config')
    const response = await this.client.get('/api/v1/config')
    console.log('🌐 [DEBUG] API Client: GET /api/v1/config response:', response.data)
    return response.data
  }

  async updateConfig(configData: any): Promise<any> {
    console.log('🌐 [DEBUG] API Client: Calling POST /api/v1/config with data:', JSON.stringify(configData, null, 2))
    const response = await this.client.post('/api/v1/config', configData)
    console.log('🌐 [DEBUG] API Client: POST /api/v1/config response:', response.data)
    return response.data
  }

  // Health endpoints
  async getHealthStatus(): Promise<HealthStatus> {
    const response = await this.client.get('/api/v1/health')
    return response.data
  }

  async runHealthCheck(): Promise<any> {
    const response = await this.client.post('/api/v1/monitoring/health-check')
    return response.data
  }

  // Agent endpoints
  async getAgents(): Promise<AgentInfo> {
    const response = await this.client.get('/api/v1/agents')
    return response.data
  }

  async getAgentInfo(agentName: string): Promise<any> {
    const response = await this.client.get(`/api/v1/agents/${agentName}`)
    return response.data
  }

  // Document endpoints
  async uploadDocument(file: File, metadata: DocumentUploadRequest): Promise<DocumentUploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('title', metadata.title)
    formData.append('document_type', metadata.document_type)
    if (metadata.tags) {
      formData.append('tags', JSON.stringify(metadata.tags))
    }
    if (metadata.metadata) {
      formData.append('metadata', JSON.stringify(metadata.metadata))
    }

    const response = await this.client.post('/api/v1/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  }

  async getDocument(documentId: string): Promise<DocumentInfo> {
    const response = await this.client.get(`/api/v1/documents/${documentId}`)
    return response.data
  }

  async listDocuments(skip: number = 0, limit: number = 50): Promise<{ documents: DocumentInfo[]; skip: number; limit: number }> {
    const response = await this.client.get('/api/v1/documents', {
      params: { skip, limit }
    })
    return response.data
  }

  async batchUploadDocuments(files: FileList): Promise<any> {
    const formData = new FormData()
    Array.from(files).forEach((file) => {
      formData.append('files', file)
    })

    const response = await this.client.post('/api/v1/documents/batch-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  }

  async uploadZipArchive(file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.client.post('/api/v1/documents/zip-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  }

  // Analysis endpoints
  async analyzeQuery(request: QueryRequest): Promise<QueryResponse> {
    const response = await this.client.post('/api/v1/analysis/query', request)
    return response.data
  }

  async analyzeDocument(documentId: string, analysisType: string, options?: Record<string, any>): Promise<any> {
    const response = await this.client.post('/api/v1/analysis/document', {
      document_id: documentId,
      analysis_type: analysisType,
      options: options || {}
    })
    return response.data
  }

  // Search endpoints
  async searchDocuments(request: SearchRequest): Promise<SearchResponse> {
    const response = await this.client.post('/api/v1/search', request)
    return response.data
  }

  // Comparison endpoints
  async compareDocuments(documentIds: string[]): Promise<ComparisonResponse> {
    const response = await this.client.post('/api/v1/compare', { document_ids: documentIds })
    return response.data
  }

  async compareDocumentsDetailed(documentIds: string[]): Promise<ComparisonResponse> {
    const response = await this.client.post('/api/v1/compare/detailed', { document_ids: documentIds })
    return response.data
  }

  // Monitoring endpoints
  async getMetrics(name?: string, tags?: Record<string, any>, hours: number = 1): Promise<any> {
    const params: any = { hours }
    if (name) params.name = name
    if (tags) params.tags = JSON.stringify(tags)

    const response = await this.client.get('/api/v1/monitoring/metrics', { params })
    return response.data
  }

  async getPerformanceMetrics(): Promise<any> {
    const response = await this.client.get('/api/v1/monitoring/performance')
    return response.data
  }

  async getRequestMetrics(): Promise<any> {
    const response = await this.client.get('/api/v1/monitoring/requests')
    return response.data
  }

  // Performance endpoints
  async getPerformanceReport(): Promise<any> {
    const response = await this.client.get('/api/v1/performance/report')
    return response.data
  }

  async getCacheStats(): Promise<any> {
    const response = await this.client.get('/api/v1/cache/stats')
    return response.data
  }

  async clearCache(pattern?: string): Promise<any> {
    const response = await this.client.post('/api/v1/cache/clear', { pattern })
    return response.data
  }

  async triggerPerformanceOptimization(optimizationType: string = 'auto'): Promise<any> {
    const response = await this.client.post('/api/v1/performance/optimize', { optimization_type: optimizationType })
    return response.data
  }
}

// Export singleton instance
export const api = new APIClient()
export default api