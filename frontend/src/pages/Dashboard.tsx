import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { FileText, Users, MessageSquare, BarChart3, RefreshCw } from 'lucide-react'

interface DashboardStats {
  totalDocuments: number
  activeAgents: number
  queriesToday: number
  systemHealth: string
}

interface Activity {
  id: string
  action: string
  resource_type: string
  resource_id: string | null
  timestamp: string
  details: Record<string, any>
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const [stats, setStats] = useState<DashboardStats>({
    totalDocuments: 0,
    activeAgents: 30,
    queriesToday: 0,
    systemHealth: '100%'
  })
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch dashboard statistics
  const fetchStats = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Fetch dashboard statistics from dedicated endpoint
      const statsResponse = await fetch('/api/v1/dashboard/stats')
      const statsData = await statsResponse.json()

      setStats({
        totalDocuments: statsData.total_documents || 0,
        activeAgents: statsData.active_agents || 30,
        queriesToday: statsData.queries_today || 0,
        systemHealth: statsData.system_health || '100%'
      })

      // Fetch recent activities
      const activitiesResponse = await fetch('/api/v1/activities/recent?limit=10')
      const activitiesData = await activitiesResponse.json()

      setActivities(activitiesData.activities || [])
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err)
      setError('Failed to load dashboard data')
      // Set fallback values
      setStats({
        totalDocuments: 0,
        activeAgents: 30,
        queriesToday: 0,
        systemHealth: '100%'
      })
      setActivities([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  const statItems = [
    {
      name: 'Total Documents',
      value: isLoading ? '...' : stats.totalDocuments.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      name: 'Active Agents',
      value: stats.activeAgents.toString(),
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      name: 'Queries Today',
      value: stats.queriesToday.toString(),
      icon: MessageSquare,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      name: 'System Health',
      value: stats.systemHealth,
      icon: BarChart3,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-2 text-gray-600">
            Welcome to your AI Law Firm management system
          </p>
        </div>
        <button
          onClick={fetchStats}
          disabled={isLoading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>{isLoading ? 'Refreshing...' : 'Refresh'}</span>
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error Loading Statistics
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statItems.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`p-3 rounded-md ${stat.bgColor}`}>
                      <Icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Recent Activity
          </h3>
          <div className="space-y-4">
            {activities.length > 0 ? (
              activities.map((activity) => {
                const getActivityIcon = (action: string, resourceType: string) => {
                  if (action === 'document_upload') return FileText
                  if (action === 'legal_query') return MessageSquare
                  return RefreshCw
                }

                const getActivityColor = (action: string) => {
                  if (action === 'document_upload') return 'text-blue-600'
                  if (action === 'legal_query') return 'text-green-600'
                  return 'text-gray-600'
                }

                const getActivityBg = (action: string) => {
                  if (action === 'document_upload') return 'bg-blue-100'
                  if (action === 'legal_query') return 'bg-green-100'
                  return 'bg-gray-100'
                }

                const formatActivityMessage = (activity: Activity) => {
                  if (activity.action === 'document_upload') {
                    return `Document uploaded: ${activity.details.filename || 'Unknown file'}`
                  } else if (activity.action === 'legal_query') {
                    return `Legal query processed: ${activity.details.query?.substring(0, 50) || 'Query'}...`
                  } else {
                    return `${activity.action.replace('_', ' ').toUpperCase()}`
                  }
                }

                const formatActivityDetails = (activity: Activity) => {
                  if (activity.action === 'document_upload') {
                    return `${activity.details.file_size ? Math.round(activity.details.file_size / 1024) + ' KB' : ''} • ${activity.details.chunks_created || 0} chunks created`
                  } else if (activity.action === 'legal_query') {
                    return `Model: ${activity.details.model_used || 'Unknown'} • Response: ${activity.details.response_length || 0} chars`
                  } else {
                    return activity.resource_type
                  }
                }

                const formatTimestamp = (timestamp: string) => {
                  const date = new Date(timestamp)
                  const now = new Date()
                  const diffMs = now.getTime() - date.getTime()
                  const diffMins = Math.floor(diffMs / (1000 * 60))
                  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
                  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

                  if (diffMins < 1) return 'Just now'
                  if (diffMins < 60) return `${diffMins}m ago`
                  if (diffHours < 24) return `${diffHours}h ago`
                  if (diffDays < 7) return `${diffDays}d ago`
                  return date.toLocaleDateString()
                }

                const IconComponent = getActivityIcon(activity.action, activity.resource_type)

                return (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 ${getActivityBg(activity.action)} rounded-full flex items-center justify-center`}>
                        <IconComponent className={`w-4 h-4 ${getActivityColor(activity.action)}`} />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        {formatActivityMessage(activity)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatActivityDetails(activity)}
                      </p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatTimestamp(activity.timestamp)}
                    </div>
                  </div>
                )
              })
            ) : (
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Activity will appear here as you upload documents and make queries.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <button
              onClick={() => navigate('/chat')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-legal-600 hover:bg-legal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-legal-500"
            >
              <MessageSquare className="w-5 h-5 mr-2" />
              Start Legal Chat
            </button>
            <button
              onClick={() => navigate('/upload')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-legal-500"
            >
              <FileText className="w-5 h-5 mr-2" />
              Upload Document
            </button>
            <button
              onClick={() => navigate('/agents')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-legal-500"
            >
              <Users className="w-5 h-5 mr-2" />
              View Agents
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard