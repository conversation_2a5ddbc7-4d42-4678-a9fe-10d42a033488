import { useState, useEffect } from 'react'
import { AgentDashboard } from '../components/AgentDashboard'
import { Agent, AgentStats } from '../types'

const AgentManagement = () => {
  const [agents, setAgents] = useState<Agent[]>([])
  const [agentStats, setAgentStats] = useState<AgentStats>({
    totalAgents: 0,
    activeAgents: 0,
    idleAgents: 0,
    errorAgents: 0,
    averageResponseTime: 0,
    totalTasksProcessed: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAgents()
  }, [])

  const loadAgents = async () => {
    try {
      setLoading(true)
      // Fetch agents from API
      const response = await fetch('/api/v1/agents')
      if (response.ok) {
        const data = await response.json()
        // Transform API response to match our Agent type
        const transformedAgents: Agent[] = data.agents?.map((agent: any) => ({
          id: agent.id || `agent_${Date.now()}`,
          name: agent.name || 'Unknown Agent',
          specialization: agent.specialization || 'general',
          status: agent.status || 'idle',
          domain: agent.domain || 'General Law',
          expertiseLevel: agent.expertise_level || 'intermediate',
          totalTasks: agent.total_tasks || 0,
          successRate: agent.success_rate || 0.8,
          averageResponseTime: agent.average_response_time || 2.0,
        })) || []

        setAgents(transformedAgents)

        // Calculate stats
        const stats: AgentStats = {
          totalAgents: transformedAgents.length,
          activeAgents: transformedAgents.filter(a => a.status === 'busy').length,
          idleAgents: transformedAgents.filter(a => a.status === 'idle').length,
          errorAgents: transformedAgents.filter(a => a.status === 'error').length,
          averageResponseTime: transformedAgents.length > 0
            ? transformedAgents.reduce((sum, a) => sum + a.averageResponseTime, 0) / transformedAgents.length
            : 0,
          totalTasksProcessed: transformedAgents.reduce((sum, a) => sum + a.totalTasks, 0),
        }

        setAgentStats(stats)
      } else {
        // Fallback to mock data if API fails
        loadMockAgents()
      }
    } catch (error) {
      console.error('Failed to load agents:', error)
      loadMockAgents()
    } finally {
      setLoading(false)
    }
  }

  const loadMockAgents = () => {
    const mockAgents: Agent[] = [
      {
        id: 'agent_001',
        name: 'Contract Law Expert',
        specialization: 'contract_analysis',
        status: 'idle',
        domain: 'Contract Law',
        expertiseLevel: 'expert',
        totalTasks: 150,
        successRate: 0.95,
        averageResponseTime: 2.3,
      },
      {
        id: 'agent_002',
        name: 'Employment Law Specialist',
        specialization: 'employment_law',
        status: 'busy',
        domain: 'Employment Law',
        expertiseLevel: 'senior',
        totalTasks: 89,
        successRate: 0.92,
        averageResponseTime: 3.1,
      },
      {
        id: 'agent_003',
        name: 'Corporate Governance Advisor',
        specialization: 'corporate_law',
        status: 'idle',
        domain: 'Corporate Law',
        expertiseLevel: 'principal',
        totalTasks: 203,
        successRate: 0.97,
        averageResponseTime: 1.8,
      },
      {
        id: 'agent_004',
        name: 'Intellectual Property Analyst',
        specialization: 'intellectual_property',
        status: 'idle',
        domain: 'Intellectual Property',
        expertiseLevel: 'expert',
        totalTasks: 67,
        successRate: 0.94,
        averageResponseTime: 2.7,
      },
      {
        id: 'agent_005',
        name: 'Tax Law Consultant',
        specialization: 'tax_law',
        status: 'error',
        domain: 'Tax Law',
        expertiseLevel: 'senior',
        totalTasks: 45,
        successRate: 0.88,
        averageResponseTime: 4.2,
      },
    ]

    setAgents(mockAgents)

    const stats: AgentStats = {
      totalAgents: mockAgents.length,
      activeAgents: mockAgents.filter(a => a.status === 'busy').length,
      idleAgents: mockAgents.filter(a => a.status === 'idle').length,
      errorAgents: mockAgents.filter(a => a.status === 'error').length,
      averageResponseTime: mockAgents.reduce((sum, a) => sum + a.averageResponseTime, 0) / mockAgents.length,
      totalTasksProcessed: mockAgents.reduce((sum, a) => sum + a.totalTasks, 0),
    }

    setAgentStats(stats)
  }

  const handleRefresh = () => {
    loadAgents()
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Agent Management</h1>
          <p className="mt-2 text-gray-600">
            Manage and monitor your AI legal agents
          </p>
        </div>
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading agents...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Agent Management</h1>
        <p className="mt-2 text-gray-600">
          Manage and monitor your AI legal agents
        </p>
      </div>

      {/* Agent Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">A</span>
              </div>
            </div>
            <div className="ml-4">
              <dt className="text-sm font-medium text-gray-500 truncate">Total Agents</dt>
              <dd className="text-lg font-medium text-gray-900">{agentStats.totalAgents}</dd>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">●</span>
              </div>
            </div>
            <div className="ml-4">
              <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
              <dd className="text-lg font-medium text-gray-900">{agentStats.activeAgents}</dd>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">○</span>
              </div>
            </div>
            <div className="ml-4">
              <dt className="text-sm font-medium text-gray-500 truncate">Idle</dt>
              <dd className="text-lg font-medium text-gray-900">{agentStats.idleAgents}</dd>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">!</span>
              </div>
            </div>
            <div className="ml-4">
              <dt className="text-sm font-medium text-gray-500 truncate">Errors</dt>
              <dd className="text-lg font-medium text-gray-900">{agentStats.errorAgents}</dd>
            </div>
          </div>
        </div>
      </div>

      {/* Agent Dashboard */}
      <AgentDashboard
        agents={agents}
        stats={agentStats}
        onRefresh={handleRefresh}
      />

      {/* Additional Management Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Average Response Time</span>
              <span className="text-sm font-medium">{agentStats.averageResponseTime.toFixed(1)}s</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Total Tasks Processed</span>
              <span className="text-sm font-medium">{agentStats.totalTasksProcessed}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Average Success Rate</span>
              <span className="text-sm font-medium">
                {agents.length > 0
                  ? (agents.reduce((sum, a) => sum + a.successRate, 0) / agents.length * 100).toFixed(1)
                  : 0}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Agent Actions</h3>
          <div className="space-y-3">
            <button
              onClick={handleRefresh}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Refresh Agent Status
            </button>
            <button
              className="w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
              disabled
            >
              Configure Agent Pool (Coming Soon)
            </button>
            <button
              className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              disabled
            >
              Add New Agent (Coming Soon)
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AgentManagement