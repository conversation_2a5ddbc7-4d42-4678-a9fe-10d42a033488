import React, { useState, useEffect } from 'react'
import { Save, Key, Database, Cpu, Shield, RefreshCw } from 'lucide-react'
import ApiKeyInput from '../components/ApiKeyInput'
import { api } from '../api/client'

interface SettingsData {
  // AI Provider API Keys
  openaiApiKey: string
  anthropicApiKey: string
  huggingfaceApiKey: string
  deepseekApiKey: string
  openrouterApiKey: string

  // Vector Database Settings
  qdrantUrl: string
  qdrantApiKey: string

  // Case Configuration
  caseId: string

  // AI Model Configuration
  primaryAnalysisModel: string
  secondaryReviewModel: string
  polishFinalizeModel: string

  // Legal Team Configuration
  numberOfLegalProfessionals: string

  // Local AI Settings
  ollamaBaseUrl: string
  ollamaModel: string

  // Database Settings
  postgresHost: string
  postgresPort: string
  postgresDb: string
  postgresUser: string
  postgresPassword: string

  mongodbHost: string
  mongodbPort: string
  mongodbDb: string
  mongodbUser: string
  mongodbPassword: string

  redisHost: string
  redisPort: string
  redisPassword: string

  // Application Settings
  debugMode: boolean
  logLevel: string
  maxDocumentSize: string
}

const Settings: React.FC = () => {
  const [currentlyEditing, setCurrentlyEditing] = useState<string | null>(null)

  const [settings, setSettings] = useState<SettingsData>({
    // AI Provider API Keys - Initialize as empty, will be loaded from backend
    openaiApiKey: '',
    anthropicApiKey: '',
    huggingfaceApiKey: '',
    deepseekApiKey: '',
    openrouterApiKey: '',

    // Vector Database Settings
    qdrantUrl: import.meta.env.VITE_QDRANT_URL || 'http://localhost:6333',
    qdrantApiKey: '',

    // Case Configuration
    caseId: 'DR-25-403973',

    // AI Model Configuration
    primaryAnalysisModel: 'gpt-5-mini',
    secondaryReviewModel: 'gpt-5-mini',
    polishFinalizeModel: 'gpt-5',

    // Legal Team Configuration
    numberOfLegalProfessionals: '3',

    // Local AI Settings
    ollamaBaseUrl: import.meta.env.VITE_OLLAMA_BASE_URL || 'http://localhost:11434',
    ollamaModel: 'llama3.2:3b',

    // Database Settings
    postgresHost: 'localhost',
    postgresPort: '5432',
    postgresDb: 'ai_law_firm',
    postgresUser: 'ai_law_user',
    postgresPassword: '',
    mongodbHost: 'localhost',
    mongodbPort: '27017',
    mongodbDb: 'ai_law_firm',
    mongodbUser: 'ai_law_admin',
    mongodbPassword: '',
    redisHost: 'localhost',
    redisPort: '6379',
    redisPassword: '',

    // Application Settings
    debugMode: false,
    logLevel: 'INFO',
    maxDocumentSize: '50'
  })

  console.log('🏗️ [DEBUG] Settings component initial state:', {
    openaiApiKey: settings.openaiApiKey,
    anthropicApiKey: settings.anthropicApiKey,
    huggingfaceApiKey: settings.huggingfaceApiKey,
    deepseekApiKey: settings.deepseekApiKey,
    openrouterApiKey: settings.openrouterApiKey
  })

  const [isLoading, setIsLoading] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [settingsLoaded, setSettingsLoaded] = useState(false)

  // Load settings from backend
  useEffect(() => {
    console.log('🔍 [DEBUG] Settings component mounted, loading settings...')
    if (!settingsLoaded) {
      loadSettings()
    }
  }, [settingsLoaded])

  // Track state changes for debugging
  useEffect(() => {
    console.log('🔄 [DEBUG] Settings state changed:', {
      openaiApiKey: settings.openaiApiKey,
      anthropicApiKey: settings.anthropicApiKey,
      huggingfaceApiKey: settings.huggingfaceApiKey,
      deepseekApiKey: settings.deepseekApiKey,
      openrouterApiKey: settings.openrouterApiKey
    })
  }, [settings])

  const loadSettings = async () => {
    try {
      console.log('🔍 [DEBUG] Calling api.getConfig()...')
      const config = await api.getConfig()
      console.log('🔍 [DEBUG] Raw config received from backend:', JSON.stringify(config, null, 2))

      // Debug API keys status
      console.log('🔍 [DEBUG] api_keys_status:', config.api_keys_status)
      console.log('🔍 [DEBUG] openai key exists:', config.api_keys_status?.openai?.api_key?.exists)
      console.log('🔍 [DEBUG] anthropic key exists:', config.api_keys_status?.anthropic?.api_key?.exists)
      console.log('🔍 [DEBUG] huggingface key exists:', config.api_keys_status?.huggingface?.api_key?.exists)
      console.log('🔍 [DEBUG] deepseek key exists:', config.api_keys_status?.deepseek?.api_key?.exists)
      console.log('🔍 [DEBUG] openrouter key exists:', config.api_keys_status?.openrouter?.api_key?.exists)

      // Map backend config to frontend format
      const newSettings = {
        ...settings,
        // Load stored API keys from database - FIXED: Use correct path to check existence
        openaiApiKey: config.api_keys_status?.openai?.api_key?.exists ? 'STORED_IN_DB' : (config.openai?.api_key || ''),
        anthropicApiKey: config.api_keys_status?.anthropic?.api_key?.exists ? 'STORED_IN_DB' : (config.anthropic?.api_key || ''),
        huggingfaceApiKey: config.api_keys_status?.huggingface?.api_key?.exists ? 'STORED_IN_DB' : (config.huggingface?.api_key || ''),
        deepseekApiKey: config.api_keys_status?.deepseek?.api_key?.exists ? 'STORED_IN_DB' : (config.deepseek?.api_key || ''),
        openrouterApiKey: config.api_keys_status?.openrouter?.api_key?.exists ? 'STORED_IN_DB' : (config.openrouter?.api_key || ''),
        // Other config values
        qdrantUrl: config.qdrant_cloud?.url || settings.qdrantUrl,
        qdrantApiKey: config.qdrant_cloud?.api_key || '',
        debugMode: config.debug || false,
        logLevel: config.logging?.level || 'INFO',
        primaryAnalysisModel: config.models?.primary_analysis || settings.primaryAnalysisModel,
        secondaryReviewModel: config.models?.secondary_review || settings.secondaryReviewModel,
        polishFinalizeModel: config.models?.polish_finalize || settings.polishFinalizeModel
      }

      console.log('🔍 [DEBUG] Checking api_keys_status structure:')
      console.log('🔍 [DEBUG] config.api_keys_status:', config.api_keys_status)
      console.log('🔍 [DEBUG] openai exists check:', config.api_keys_status?.openai?.api_key?.exists)
      console.log('🔍 [DEBUG] anthropic exists check:', config.api_keys_status?.anthropic?.api_key?.exists)
      console.log('🔍 [DEBUG] huggingface exists check:', config.api_keys_status?.huggingface?.api_key?.exists)
      console.log('🔍 [DEBUG] deepseek exists check:', config.api_keys_status?.deepseek?.api_key?.exists)
      console.log('🔍 [DEBUG] openrouter exists check:', config.api_keys_status?.openrouter?.api_key?.exists)

      console.log('🔍 [DEBUG] Final settings to be set:', {
        openaiApiKey: newSettings.openaiApiKey,
        anthropicApiKey: newSettings.anthropicApiKey,
        huggingfaceApiKey: newSettings.huggingfaceApiKey,
        deepseekApiKey: newSettings.deepseekApiKey,
        openrouterApiKey: newSettings.openrouterApiKey
      })

      setSettings(newSettings)
      setSettingsLoaded(true)
      console.log('✅ [DEBUG] Settings loaded successfully')

      // Force a re-render to ensure state is updated
      setTimeout(() => {
        console.log('🔄 [DEBUG] Force re-render check - current settings state:', {
          openaiApiKey: settings.openaiApiKey,
          anthropicApiKey: settings.anthropicApiKey,
          huggingfaceApiKey: settings.huggingfaceApiKey,
          deepseekApiKey: settings.deepseekApiKey,
          openrouterApiKey: settings.openrouterApiKey
        })
      }, 100)
    } catch (error) {
      console.error('❌ [DEBUG] Failed to load settings:', error)
    }
  }

  const handleInputChange = (field: keyof SettingsData, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateApiKey = (key: string, provider: string): boolean => {
    if (!key || key === 'STORED_IN_DB') return true // Allow stored keys
    if (key === 'Password123' || key === 'password123') return false // Reject invalid defaults

    // Basic validation for different providers
    const validations: Record<string, boolean> = {
      openai: key.startsWith('sk-') && key.length > 20,
      anthropic: key.startsWith('sk-ant-') && key.length > 30,
      huggingface: key.startsWith('hf_') && key.length > 10,
      deepseek: key.startsWith('sk-') && key.length > 20,
      openrouter: key.startsWith('sk-or-v1-') && key.length > 20
    }

    return validations[provider] || key.length > 10 // Fallback validation
  }

  const handleSave = async () => {
    console.log('💾 [DEBUG] Starting save process...')
    setIsLoading(true)
    setSaveStatus('saving')

    try {
      // Validate API keys before saving
      const invalidKeys = []
      if (!validateApiKey(settings.openaiApiKey, 'openai')) invalidKeys.push('OpenAI')
      if (!validateApiKey(settings.anthropicApiKey, 'anthropic')) invalidKeys.push('Anthropic')
      if (!validateApiKey(settings.huggingfaceApiKey, 'huggingface')) invalidKeys.push('HuggingFace')
      if (!validateApiKey(settings.deepseekApiKey, 'deepseek')) invalidKeys.push('DeepSeek')
      if (!validateApiKey(settings.openrouterApiKey, 'openrouter')) invalidKeys.push('OpenRouter')

      if (invalidKeys.length > 0) {
        alert(`❌ Invalid API key format for: ${invalidKeys.join(', ')}\n\nPlease check your API keys and try again.`)
        setSaveStatus('error')
        setIsLoading(false)
        return
      }

      // Debug current settings state
      console.log('💾 [DEBUG] Current settings state:', {
        openaiApiKey: settings.openaiApiKey,
        anthropicApiKey: settings.anthropicApiKey,
        huggingfaceApiKey: settings.huggingfaceApiKey,
        deepseekApiKey: settings.deepseekApiKey,
        openrouterApiKey: settings.openrouterApiKey
      })

      // Prepare config for backend
      const configPayload = {
        // AI Provider Configurations
        openai: {
          api_key: settings.openaiApiKey,
          model: settings.primaryAnalysisModel,
          temperature: 0.1,
          max_tokens: 4000,
          timeout: 30
        },
        anthropic: {
          api_key: settings.anthropicApiKey,
          model: 'claude-3-sonnet-20240229',
          temperature: 0.1,
          max_tokens: 4000,
          timeout: 30
        },
        huggingface: {
          api_key: settings.huggingfaceApiKey
        },
        deepseek: {
          api_key: settings.deepseekApiKey
        },
        openrouter: {
          api_key: settings.openrouterApiKey
        },

        // Vector Database
        qdrant_cloud: {
          url: settings.qdrantUrl,
          api_key: settings.qdrantApiKey
        },

        // Case Configuration
        case: {
          id: settings.caseId
        },

        // AI Model Configuration
        models: {
          primary_analysis: settings.primaryAnalysisModel,
          secondary_review: settings.secondaryReviewModel,
          polish_finalize: settings.polishFinalizeModel
        },

        // Legal Team Configuration
        legal_team: {
          professionals_count: parseInt(settings.numberOfLegalProfessionals.toString())
        },

        // Local AI
        ollama: {
          base_url: settings.ollamaBaseUrl,
          model: settings.ollamaModel
        },

        // Application Settings
        debug: settings.debugMode,
        logging: {
          level: settings.logLevel
        },
        document: {
          max_size_mb: parseInt(settings.maxDocumentSize)
        }
      }

      console.log('💾 [DEBUG] Config payload being sent to backend:', JSON.stringify(configPayload, null, 2))
      console.log('💾 [DEBUG] API key values in payload:', {
        openai: configPayload.openai.api_key,
        anthropic: configPayload.anthropic.api_key,
        huggingface: configPayload.huggingface.api_key,
        deepseek: configPayload.deepseek.api_key,
        openrouter: configPayload.openrouter.api_key
      })

      const result = await api.updateConfig(configPayload)
      console.log('💾 [DEBUG] Backend response:', result)

      if (result.message) {
        console.log('✅ [DEBUG] Settings saved successfully:', result.message)
        setSaveStatus('saved')
        setTimeout(() => setSaveStatus('idle'), 3000)
      } else {
        console.log('❌ [DEBUG] Backend returned no message, throwing error')
        throw new Error('Failed to save settings')
      }
    } catch (error) {
      console.error('❌ [DEBUG] Failed to save settings:', error)
      setSaveStatus('error')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestConnection = async (service: string) => {
    try {
      let endpoint = ''
      switch (service) {
        case 'openai':
          endpoint = '/api/v1/health/openai'
          break
        case 'qdrant':
          endpoint = '/api/v1/health/qdrant'
          break
        case 'database':
          endpoint = '/api/v1/health/database'
          break
        default:
          return
      }

      const response = await fetch(endpoint)
      const result = await response.json()

      if (result.healthy) {
        alert(`${service.toUpperCase()} connection successful!`)
      } else {
        alert(`${service.toUpperCase()} connection failed: ${result.error || 'Unknown error'}`)
      }
    } catch (error) {
      alert(`Failed to test ${service} connection: ${error}`)
    }
  }

  const handleClearAllApiKeys = async () => {
    if (!confirm('⚠️ Are you sure you want to clear ALL API keys?\n\nThis will remove all stored API keys from the database and cannot be undone.')) {
      return
    }

    try {
      setIsLoading(true)

      // Clear all API keys from state
      setSettings(prev => ({
        ...prev,
        openaiApiKey: '',
        anthropicApiKey: '',
        huggingfaceApiKey: '',
        deepseekApiKey: '',
        openrouterApiKey: '',
        qdrantApiKey: ''
      }))

      // Call backend to clear stored keys
      const response = await fetch('/api/v1/config/clear-api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        alert('✅ All API keys have been cleared successfully!')
        // Reload settings to get fresh state
        loadSettings()
      } else {
        throw new Error('Failed to clear API keys')
      }
    } catch (error) {
      console.error('Failed to clear API keys:', error)
      alert('❌ Failed to clear API keys. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="mt-2 text-gray-600">
            Configure your AI Law Firm preferences and API connections
          </p>
        </div>
        <button
          onClick={handleSave}
          disabled={isLoading}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium ${
            saveStatus === 'saved'
              ? 'bg-green-600 text-white'
              : saveStatus === 'error'
              ? 'bg-red-600 text-white'
              : 'bg-legal-500 text-white hover:bg-legal-600'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : saveStatus === 'saved' ? (
            <Save className="w-4 h-4" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          <span>
            {isLoading ? 'Saving...' :
             saveStatus === 'saved' ? 'Saved!' :
             saveStatus === 'error' ? 'Error!' : 'Save Settings'}
          </span>
        </button>
      </div>

      {/* API Configuration & Model Selection */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Key className="w-5 h-5 text-legal-500" />
            <h2 className="text-lg font-semibold text-gray-900">🔑 API Configuration & Model Selection</h2>
          </div>
        </div>
        <div className="p-6 space-y-6">
          {/* AI Provider API Keys */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">🔑 AI Provider API Keys</h3>

            {/* OpenAI */}
            <ApiKeyInput
              label="OpenAI API Key"
              value={settings.openaiApiKey}
              onChange={(value) => handleInputChange('openaiApiKey', value)}
              placeholder="sk-..."
              fieldId="openai"
            />

            {/* Anthropic */}
            <ApiKeyInput
              label="Anthropic API Key"
              value={settings.anthropicApiKey}
              onChange={(value) => handleInputChange('anthropicApiKey', value)}
              placeholder="sk-ant-..."
              fieldId="anthropic"
            />

            {/* Hugging Face */}
            <ApiKeyInput
              label="Hugging Face API Key"
              value={settings.huggingfaceApiKey}
              onChange={(value) => handleInputChange('huggingfaceApiKey', value)}
              placeholder="hf_..."
              fieldId="huggingface"
            />

            {/* DeepSeek */}
            <ApiKeyInput
              label="DeepSeek API Key"
              value={settings.deepseekApiKey}
              onChange={(value) => handleInputChange('deepseekApiKey', value)}
              placeholder="sk-..."
              fieldId="deepseek"
            />

            {/* OpenRouter */}
            <ApiKeyInput
              label="OpenRouter API Key"
              value={settings.openrouterApiKey}
              onChange={(value) => handleInputChange('openrouterApiKey', value)}
              placeholder="sk-or-v1-..."
              fieldId="openrouter"
            />
          </div>

          {/* Vector Database Configuration */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">🔍 Vector Database Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="qdrantUrl" className="block text-sm font-medium text-gray-700">Qdrant URL</label>
                <input
                  id="qdrantUrl"
                  type="text"
                  value={settings.qdrantUrl}
                  onChange={(e) => handleInputChange('qdrantUrl', e.target.value)}
                  placeholder="http://localhost:6333"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div className="space-y-2">
                <ApiKeyInput
                  label="Qdrant API Key"
                  value={settings.qdrantApiKey}
                  onChange={(value) => handleInputChange('qdrantApiKey', value)}
                  fieldId="qdrant"
                />
              </div>
            </div>
          </div>

          {/* Case Configuration */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">📋 Case Configuration</h3>
            <div className="space-y-2">
              <label htmlFor="caseId" className="block text-sm font-medium text-gray-700">Case ID</label>
              <input
                id="caseId"
                type="text"
                value={settings.caseId}
                onChange={(e) => handleInputChange('caseId', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                placeholder="DR-25-403973"
              />
            </div>
          </div>

          {/* AI Model Configuration */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">🤖 AI Model Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label htmlFor="primaryAnalysisModel" className="block text-sm font-medium text-gray-700">Primary Analysis Model</label>
                <select
                  id="primaryAnalysisModel"
                  value={settings.primaryAnalysisModel}
                  onChange={(e) => handleInputChange('primaryAnalysisModel', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                >
                  <option value="gpt-4o-mini">gpt-4o-mini</option>
                  <option value="gpt-5-nano">gpt-5-nano</option>
                  <option value="gpt-5-mini">gpt-5-mini</option>
                  <option value="gpt-5">gpt-5</option>
                  <option value="gpt-4o">gpt-4o</option>
                  <option value="gpt-4">gpt-4</option>
                  <option value="claude-3-sonnet">claude-3-sonnet</option>
                  <option value="claude-3-opus">claude-3-opus</option>
                </select>
              </div>
              <div className="space-y-2">
                <label htmlFor="secondaryReviewModel" className="block text-sm font-medium text-gray-700">Secondary Review Model</label>
                <select
                  id="secondaryReviewModel"
                  name="secondaryReviewModel"
                  value={settings.secondaryReviewModel}
                  onChange={(e) => handleInputChange('secondaryReviewModel', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                >
                  <option value="gpt-4o-mini">gpt-4o-mini</option>
                  <option value="gpt-5-nano">gpt-5-nano</option>
                  <option value="gpt-5-mini">gpt-5-mini</option>
                  <option value="gpt-5">gpt-5</option>
                  <option value="gpt-4o">gpt-4o</option>
                  <option value="gpt-4">gpt-4</option>
                  <option value="claude-3-haiku">claude-3-haiku</option>
                  <option value="claude-3-sonnet">claude-3-sonnet</option>
                </select>
              </div>
              <div className="space-y-2">
                <label htmlFor="polishFinalizeModel" className="block text-sm font-medium text-gray-700">Polish & Finalize Model</label>
                <select
                  id="polishFinalizeModel"
                  title="Polish & Finalize Model"
                  value={settings.polishFinalizeModel}
                  onChange={(e) => handleInputChange('polishFinalizeModel', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                >
                  <option value="gpt-5-nano">gpt-5-nano</option>
                  <option value="gpt-5-mini">gpt-5-mini</option>
                  <option value="gpt-5">gpt-5</option>
                  <option value="gpt-4o">gpt-4o</option>
                  <option value="gpt-4">gpt-4</option>
                  <option value="claude-3-opus">claude-3-opus</option>
                  <option value="claude-3-sonnet">claude-3-sonnet</option>
                </select>
              </div>
            </div>
          </div>

          {/* Legal Team Configuration */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">👥 Legal Team Configuration</h3>
            <div className="space-y-2">
              <label htmlFor="numberOfLegalProfessionals" className="block text-sm font-medium text-gray-700">Number of Legal Professionals</label>
              <input
                id="numberOfLegalProfessionals"
                type="number"
                min="1"
                max="45"
                value={settings.numberOfLegalProfessionals}
                onChange={(e) => handleInputChange('numberOfLegalProfessionals', (parseInt(e.target.value) || 3).toString())}
                placeholder="3"
                title="Number of Legal Professionals"
                aria-describedby="legalProfessionalsRange"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
              />
              <p id="legalProfessionalsRange" className="text-xs text-gray-500">Range: 1-45 professionals</p>
            </div>
          </div>
        </div>
      </div>

      {/* Local AI Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Cpu className="w-5 h-5 text-legal-500" />
            <h2 className="text-lg font-semibold text-gray-900">Local AI Settings</h2>
          </div>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="ollamaBaseUrl" className="block text-sm font-medium text-gray-700 mb-1">
                Ollama Base URL
              </label>
              <input
                id="ollamaBaseUrl"
                type="text"
                value={settings.ollamaBaseUrl}
                onChange={(e) => handleInputChange('ollamaBaseUrl', e.target.value)}
                placeholder="http://localhost:11434"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
              />
            </div>
            <div>
              <label htmlFor="ollamaModel" className="block text-sm font-medium text-gray-700 mb-1">
                Ollama Model
              </label>
              <input
                id="ollamaModel"
                type="text"
                value={typeof settings.ollamaModel === 'string' ? settings.ollamaModel : ''}
                onChange={(e) => handleInputChange('ollamaModel', e.target.value)}
                placeholder="llama3.2:3b"
                title="Ollama Model"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={() => handleTestConnection('ollama')}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
              >
                Test Connection
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Vector Database Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Database className="w-5 h-5 text-legal-500" />
            <h2 className="text-lg font-semibold text-gray-900">Vector Database Settings</h2>
          </div>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="qdrantUrl2" className="block text-sm font-medium text-gray-700 mb-1">
                Qdrant URL
              </label>
              <input
                id="qdrantUrl2"
                type="text"
                value={settings.qdrantUrl}
                onChange={(e) => handleInputChange('qdrantUrl', e.target.value)}
                placeholder="http://localhost:6333"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
              />
            </div>
            <div>
              <ApiKeyInput
                label="API Key"
                value={settings.qdrantApiKey}
                onChange={(value) => handleInputChange('qdrantApiKey', value)}
                fieldId="qdrant2"
                currentlyEditing={currentlyEditing}
                onEditingChange={setCurrentlyEditing}
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={() => handleTestConnection('qdrant')}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
              >
                Test Connection
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Application Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-legal-500" />
            <h2 className="text-lg font-semibold text-gray-900">Application Settings</h2>
          </div>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="logLevel" className="block text-sm font-medium text-gray-700 mb-1">
                Log Level
              </label>
              <select
                id="logLevel"
                value={settings.logLevel}
                onChange={(e) => handleInputChange('logLevel', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
              >
                <option value="DEBUG">Debug</option>
                <option value="INFO">Info</option>
                <option value="WARNING">Warning</option>
                <option value="ERROR">Error</option>
              </select>
            </div>
            <div>
              <label htmlFor="maxDocumentSize" className="block text-sm font-medium text-gray-700 mb-1">
                Max Document Size (MB)
              </label>
              <input
                id="maxDocumentSize"
                type="number"
                value={settings.maxDocumentSize}
                onChange={(e) => handleInputChange('maxDocumentSize', e.target.value)}
                placeholder="50"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.debugMode}
                  onChange={(e) => handleInputChange('debugMode', e.target.checked)}
                  className="rounded border-gray-300 text-legal-500 focus:ring-legal-500"
                />
                <span className="text-sm font-medium text-gray-700">Debug Mode</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Database Settings (Advanced) */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Database className="w-5 h-5 text-legal-500" />
            <h2 className="text-lg font-semibold text-gray-900">Database Settings (Advanced)</h2>
          </div>
        </div>
        <div className="p-6 space-y-6">
          {/* PostgreSQL */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">PostgreSQL</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label htmlFor="postgresHost" className="block text-sm font-medium text-gray-700 mb-1">Host</label>
                <input
                  id="postgresHost"
                  type="text"
                  value={settings.postgresHost}
                  onChange={(e) => handleInputChange('postgresHost', e.target.value)}
                  placeholder="localhost"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="postgresPort" className="block text-sm font-medium text-gray-700 mb-1">Port</label>
                <input
                  id="postgresPort"
                  type="text"
                  value={settings.postgresPort}
                  onChange={(e) => handleInputChange('postgresPort', e.target.value)}
                  placeholder="5432"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="postgresDb" className="block text-sm font-medium text-gray-700 mb-1">Database</label>
                <input
                  id="postgresDb"
                  type="text"
                  value={settings.postgresDb}
                  onChange={(e) => handleInputChange('postgresDb', e.target.value)}
                  placeholder="ai_law_firm"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="postgresUser" className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <input
                  id="postgresUser"
                  type="text"
                  value={settings.postgresUser}
                  onChange={(e) => handleInputChange('postgresUser', e.target.value)}
                  placeholder="ai_law_user"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
            </div>
            <div>
              <ApiKeyInput
                label="Password"
                value={settings.postgresPassword}
                onChange={(value) => handleInputChange('postgresPassword', value)}
                fieldId="postgres"
                currentlyEditing={currentlyEditing}
                onEditingChange={setCurrentlyEditing}
              />
            </div>
          </div>

          {/* MongoDB */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">MongoDB</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label htmlFor="mongodbHost" className="block text-sm font-medium text-gray-700 mb-1">Host</label>
                <input
                  id="mongodbHost"
                  type="text"
                  value={settings.mongodbHost}
                  onChange={(e) => handleInputChange('mongodbHost', e.target.value)}
                  placeholder="localhost"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="mongodbPort" className="block text-sm font-medium text-gray-700 mb-1">Port</label>
                <input
                  id="mongodbPort"
                  type="text"
                  value={settings.mongodbPort}
                  onChange={(e) => handleInputChange('mongodbPort', e.target.value)}
                  placeholder="27017"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="mongodbDb" className="block text-sm font-medium text-gray-700 mb-1">Database</label>
                <input
                  id="mongodbDb"
                  type="text"
                  value={settings.mongodbDb}
                  onChange={(e) => handleInputChange('mongodbDb', e.target.value)}
                  placeholder="ai_law_firm"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="mongodbUser" className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <input
                  id="mongodbUser"
                  type="text"
                  value={settings.mongodbUser}
                  onChange={(e) => handleInputChange('mongodbUser', e.target.value)}
                  placeholder="ai_law_admin"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
            </div>
            <div>
              <ApiKeyInput
                label="Password"
                value={settings.mongodbPassword}
                onChange={(value) => handleInputChange('mongodbPassword', value)}
                fieldId="mongodb"
                currentlyEditing={currentlyEditing}
                onEditingChange={setCurrentlyEditing}
              />
            </div>
          </div>

          {/* Redis */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">Redis (Cache)</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="redisHost" className="block text-sm font-medium text-gray-700 mb-1">Host</label>
                <input
                  id="redisHost"
                  type="text"
                  value={settings.redisHost}
                  onChange={(e) => handleInputChange('redisHost', e.target.value)}
                  placeholder="localhost"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="redisPort" className="block text-sm font-medium text-gray-700 mb-1">Port</label>
                <input
                  id="redisPort"
                  type="text"
                  value={settings.redisPort}
                  onChange={(e) => handleInputChange('redisPort', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent"
                  placeholder="6379"
                />
              </div>
              <div>
                <ApiKeyInput
                  label="Password"
                  value={settings.redisPassword}
                  onChange={(value) => handleInputChange('redisPassword', value)}
                  fieldId="redis"
                  currentlyEditing={currentlyEditing}
                  onEditingChange={setCurrentlyEditing}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">⚡ Quick Actions</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => fetch('/api/v1/monitoring/health-check', { method: 'POST' })}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Run Health Check
          </button>
          <button
            onClick={() => fetch('/api/v1/cache/clear', { method: 'POST' })}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
          >
            Clear Cache
          </button>
          <button
            onClick={() => fetch('/api/v1/performance/optimize', { method: 'POST' })}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            Optimize Performance
          </button>
          <button
            onClick={handleClearAllApiKeys}
            disabled={isLoading}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            🗑️ Clear All API Keys
          </button>
        </div>
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800">
            <strong>⚠️ Security Notice:</strong> If you see "Password123" or other unexpected values in API key fields,
            use the "Clear All API Keys" button above to reset them. This will remove all stored keys from the database.
          </p>
        </div>
      </div>
    </div>
  )
}

export default Settings
