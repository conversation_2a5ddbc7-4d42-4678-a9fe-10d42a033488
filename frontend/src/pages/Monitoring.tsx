import { useState, useEffect, Component, ReactNode } from 'react'
import { HealthStatus } from '../types'

// Error Boundary Component
class MonitoringErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Monitoring component error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Monitoring</h1>
            <p className="mt-2 text-gray-600">
              System performance and health monitoring
            </p>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Monitoring Error
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>There was an error loading the monitoring data. Please try refreshing the page.</p>
                  {this.state.error && (
                    <details className="mt-2">
                      <summary className="cursor-pointer">Error details</summary>
                      <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto">
                        {this.state.error.message}
                      </pre>
                    </details>
                  )}
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
                  >
                    Refresh Page
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

interface SystemMetrics {
  cpu_percent: { current: number; average: number; min: number; max: number; latest: number }
  memory_percent: { current: number; average: number; min: number; max: number; latest: number }
  disk_percent: { current: number; average: number; min: number; max: number; latest: number }
}

interface PerformanceMetrics {
  response_time: { average: number; max: number; count: number }
  cache_performance: { hit_rate: number; average_hit_rate: number }
  database_performance: { average_query_time: number; max_query_time: number }
  ai_performance: { average_request_time: number; max_request_time: number }
}

interface CacheStats {
  hits: number
  misses: number
  hit_rate: number
  total_requests: number
  size: number
}

const Monitoring = () => {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null)
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null)
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null)
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  useEffect(() => {
    loadMonitoringData()

    // Set up periodic updates every 30 seconds
    const interval = setInterval(loadMonitoringData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadMonitoringData = async () => {
    try {
      setLoading(true)

      // Load health status
      const healthResponse = await fetch('/api/v1/health')
      if (healthResponse.ok) {
        const healthData = await healthResponse.json()
        setHealthStatus(healthData)
      }

      // Load performance metrics
      const perfResponse = await fetch('/api/v1/monitoring/performance')
      if (perfResponse.ok) {
        const perfData = await perfResponse.json()
        setSystemMetrics(perfData.system_metrics)
        setPerformanceMetrics(perfData.application_metrics)
      }

      // Load cache stats
      const cacheResponse = await fetch('/api/v1/cache/stats')
      if (cacheResponse.ok) {
        const cacheData = await cacheResponse.json()
        setCacheStats(cacheData.cache_statistics)
      }

      setLastUpdate(new Date())
    } catch (error) {
      console.error('Failed to load monitoring data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string | undefined | null) => {
    if (!status || typeof status !== 'string') {
      return 'text-gray-600 bg-gray-100'
    }

    switch (status.toLowerCase()) {
      case 'healthy':
        return 'text-green-600 bg-green-100'
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100'
      case 'unhealthy':
      case 'not_available':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatMetric = (value: number | undefined | null, unit: string = '') => {
    if (value == null || isNaN(value)) {
      return `N/A${unit}`
    }
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M${unit}`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K${unit}`
    }
    return `${value}${unit}`
  }

  const formatTime = (seconds: number | undefined | null) => {
    if (seconds == null || isNaN(seconds)) {
      return 'N/A'
    }
    if (seconds < 1) {
      return `${(seconds * 1000).toFixed(0)}ms`
    }
    return `${seconds.toFixed(2)}s`
  }

  if (loading && !healthStatus) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Monitoring</h1>
          <p className="mt-2 text-gray-600">
            System performance and health monitoring
          </p>
        </div>
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading monitoring data...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Monitoring</h1>
          <p className="mt-2 text-gray-600">
            System performance and health monitoring
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </span>
          <button
            onClick={loadMonitoringData}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Overall Health Status */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">System Health</h2>
        {healthStatus && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(healthStatus.overall_health)}`}>
                {(healthStatus.overall_health || 'unknown').toUpperCase()}
              </div>
              <p className="mt-2 text-sm text-gray-600">Overall Status</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {Object.keys(healthStatus.services).length}
              </div>
              <p className="text-sm text-gray-600">Total Services</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Object.values(healthStatus.services).filter(s => s.status === 'healthy').length}
              </div>
              <p className="text-sm text-gray-600">Healthy Services</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {Object.keys(healthStatus.services).length}
              </div>
              <p className="text-sm text-gray-600">Monitored Services</p>
            </div>
          </div>
        )}
      </div>

      {/* Service Status Details */}
      {healthStatus && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Service Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(healthStatus.services).map(([serviceName, service]) => (
              <div key={serviceName} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 capitalize">
                    {serviceName.replace('_', ' ')}
                  </h3>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                    {service.status || 'unknown'}
                  </span>
                </div>
                {service.responseTime && (
                  <p className="text-sm text-gray-600">
                    Response Time: {formatTime(service.responseTime)}
                  </p>
                )}
                {service.errorMessage && (
                  <p className="text-sm text-red-600 mt-1">
                    Error: {service.errorMessage}
                  </p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Last Check: {new Date(service.lastCheck).toLocaleTimeString()}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* System Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Resources */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">System Resources</h2>
          {systemMetrics ? (
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>CPU Usage</span>
                  <span>{systemMetrics.cpu_percent?.current?.toFixed(1) || 'N/A'}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${Math.min(systemMetrics.cpu_percent?.current || 0, 100)}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Memory Usage</span>
                  <span>{systemMetrics.memory_percent?.current?.toFixed(1) || 'N/A'}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${Math.min(systemMetrics.memory_percent?.current || 0, 100)}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Disk Usage</span>
                  <span>{systemMetrics.disk_percent?.current?.toFixed(1) || 'N/A'}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-600 h-2 rounded-full"
                    style={{ width: `${Math.min(systemMetrics.disk_percent?.current || 0, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">System metrics not available</p>
          )}
        </div>

        {/* Application Performance */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Application Performance</h2>
          {performanceMetrics ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatMetric(performanceMetrics.response_time.count)}
                  </div>
                  <p className="text-sm text-gray-600">API Requests</p>
                  <p className="text-xs text-gray-500">
                    Avg: {formatTime(performanceMetrics.response_time.average)}
                  </p>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatMetric(performanceMetrics.ai_performance.average_request_time ? 1 : 0)}
                  </div>
                  <p className="text-sm text-gray-600">AI Operations</p>
                  <p className="text-xs text-gray-500">
                    Avg: {formatTime(performanceMetrics.ai_performance.average_request_time)}
                  </p>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {formatMetric(performanceMetrics.database_performance.average_query_time ? 1 : 0)}
                  </div>
                  <p className="text-sm text-gray-600">DB Operations</p>
                  <p className="text-xs text-gray-500">
                    Avg: {formatTime(performanceMetrics.database_performance.average_query_time)}
                  </p>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {cacheStats?.hit_rate ? `${(cacheStats.hit_rate * 100).toFixed(1)}%` : 'N/A'}
                  </div>
                  <p className="text-sm text-gray-600">Cache Hit Rate</p>
                  <p className="text-xs text-gray-500">
                    {cacheStats ? `${cacheStats.hits || 0}/${cacheStats.total_requests || 0}` : ''}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">Performance metrics not available</p>
          )}
        </div>
      </div>

      {/* Cache Statistics */}
      {cacheStats && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Cache Performance</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{cacheStats.hits}</div>
              <p className="text-sm text-gray-600">Cache Hits</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{cacheStats.misses}</div>
              <p className="text-sm text-gray-600">Cache Misses</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{cacheStats.hit_rate ? (cacheStats.hit_rate * 100).toFixed(1) : 'N/A'}%</div>
              <p className="text-sm text-gray-600">Hit Rate</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{cacheStats.size}</div>
              <p className="text-sm text-gray-600">Cache Size</p>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => fetch('/api/v1/monitoring/health-check', { method: 'GET' })}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Run Health Check
          </button>
          <button
            onClick={() => fetch('/api/v1/cache/clear', { method: 'POST' })}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
          >
            Clear Cache
          </button>
          <button
            onClick={() => fetch('/api/v1/performance/optimize', { method: 'POST' })}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            Optimize Performance
          </button>
        </div>
      </div>
    </div>
  )
}

// Wrap the component with error boundary
const MonitoringWithErrorBoundary = () => (
  <MonitoringErrorBoundary>
    <Monitoring />
  </MonitoringErrorBoundary>
)

export default MonitoringWithErrorBoundary