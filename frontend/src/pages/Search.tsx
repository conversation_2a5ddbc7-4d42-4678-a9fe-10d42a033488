import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { Search as SearchIcon, FileText, Calendar, Tag } from 'lucide-react'

interface SearchResult {
  document_id: string
  title: string
  content: string
  score: number
  metadata: Record<string, any>
}

const Search = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const [query, setQuery] = useState(searchParams.get('q') || '')
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const q = searchParams.get('q')
    if (q) {
      setQuery(q)
      performSearch(q)
    }
  }, [searchParams])

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/v1/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          limit: 50
        })
      })

      if (response.ok) {
        const data = await response.json()
        setResults(data.results || [])
      } else {
        throw new Error(`Search failed: ${response.status}`)
      }
    } catch (err) {
      console.error('Search error:', err)
      setError(err instanceof Error ? err.message : 'Search failed')
      // Show mock results for demo
      setResults([
        {
          document_id: 'mock_1',
          title: 'Sample Legal Document',
          content: `This document contains information related to ${searchQuery} and legal matters. It includes various legal provisions, clauses, and considerations that may be relevant to your search.`,
          score: 0.85,
          metadata: {
            document_type: 'contract',
            created_date: new Date().toISOString(),
            author: 'Legal Department'
          }
        },
        {
          document_id: 'mock_2',
          title: 'Legal Research Memo',
          content: `Comprehensive legal research on topics related to ${searchQuery}. This memo provides detailed analysis and recommendations for legal practitioners.`,
          score: 0.72,
          metadata: {
            document_type: 'research',
            created_date: new Date().toISOString(),
            author: 'Research Team'
          }
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (query.trim()) {
      setSearchParams({ q: query.trim() })
    }
  }

  const highlightText = (text: string, query: string) => {
    if (!query) return text

    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)

    return parts.map((part, index) =>
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Document Search</h1>
        <p className="mt-2 text-gray-600">
          Search through your legal documents and knowledge base
        </p>
      </div>

      {/* Search Form */}
      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Enter your search query..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              disabled={loading}
            />
          </div>
          <button
            type="submit"
            disabled={loading || !query.trim()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <SearchIcon className="w-5 h-5" />
            {loading ? 'Searching...' : 'Search'}
          </button>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Search Error
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {error}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Searching documents...</span>
          </div>
        </div>
      )}

      {/* Search Results */}
      {!loading && results.length > 0 && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Search Results ({results.length})
            </h2>
            <p className="mt-1 text-sm text-gray-600">
              Results for "{query}"
            </p>
          </div>

          <div className="divide-y divide-gray-200">
            {results.map((result) => (
              <div key={result.document_id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start">
                  <FileText className="w-5 h-5 text-gray-400 mt-1 mr-3 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {highlightText(result.title, query)}
                    </h3>

                    <div className="text-sm text-gray-600 mb-3 line-clamp-3">
                      {highlightText(result.content.substring(0, 300), query)}
                      {result.content.length > 300 && '...'}
                    </div>

                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Tag className="w-3 h-3" />
                        {result.metadata?.document_type || 'Unknown'}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {result.metadata?.created_date ? formatDate(result.metadata.created_date) : 'Unknown date'}
                      </div>
                      <div className="text-green-600 font-medium">
                        Score: {(result.score * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {!loading && !error && results.length === 0 && query && (
        <div className="bg-white shadow rounded-lg p-12">
          <div className="text-center">
            <SearchIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No results found
            </h3>
            <p className="text-gray-600">
              Try adjusting your search query or check your spelling.
            </p>
          </div>
        </div>
      )}

      {/* Initial State */}
      {!loading && !query && !error && results.length === 0 && (
        <div className="bg-white shadow rounded-lg p-12">
          <div className="text-center">
            <SearchIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Search Legal Documents
            </h3>
            <p className="text-gray-600">
              Enter a search query to find relevant legal documents and information.
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

export default Search