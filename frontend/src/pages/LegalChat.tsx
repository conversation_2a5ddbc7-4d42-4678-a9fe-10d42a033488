import React, { useState, useRef, useEffect } from 'react'
import { Send, User, Bot, FileText, Search, Settings, Plus, ChevronDown } from 'lucide-react'
import { api, QueryRequest, QueryResponse } from '../api/client'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  metadata?: {
    confidence?: number
    sources?: string[]
    processing_time?: number
    model_used?: string
    cached?: boolean
  }
}

const LegalChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Hello! I\'m your AI Legal Assistant. I can help you with legal queries, document analysis, and research across 24 different legal domains. How can I assist you today?',
      timestamp: new Date(),
    }
  ])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([])
  const [selectedModel, setSelectedModel] = useState('gpt-4o-mini')
  const [showModelDropdown, setShowModelDropdown] = useState(false)
  const [availableDocuments, setAvailableDocuments] = useState<any[]>([])
  const [loadingDocuments, setLoadingDocuments] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const availableModels = [   
    { id: 'auto', name: 'Auto Select', description: 'Automatically choose best model' },
    { id: 'gpt-5-nano', name: 'GPT-5 Nano', description: 'Ultra-efficient, lightning-fast' },
    { id: 'gpt-5-mini', name: 'GPT-5 Mini', description: 'Next-gen efficiency, high performance' },
    { id: 'gpt-5', name: 'GPT-5', description: 'Flagship model, maximum capability' },
    { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Next-gen AI, ultra-fast, cost-effective' },
    { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable model' },
    { id: 'gpt-4', name: 'GPT-4', description: 'High quality legacy model' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and efficient' },
    { id: 'claude-3-opus', name: 'Claude 3 Opus', description: 'Excellent reasoning' },
    { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', description: 'Balanced performance' },
    { id: 'ollama-llama2', name: 'Llama 2 (Local)', description: 'Privacy-focused' },
    { id: 'ollama-mistral', name: 'Mistral (Local)', description: 'Fast local model' },
  ]

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    loadAvailableDocuments()
  }, [])

  const loadAvailableDocuments = async () => {
    try {
      setLoadingDocuments(true)
      const response = await api.listDocuments()
      setAvailableDocuments(response.documents || [])
    } catch (error) {
      console.error('Failed to load documents:', error)
      // Keep empty array on error
      setAvailableDocuments([])
    } finally {
      setLoadingDocuments(false)
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showModelDropdown && !(event.target as Element).closest('.model-dropdown')) {
        setShowModelDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showModelDropdown])

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {
      const request: QueryRequest = {
        query: userMessage.content,
        document_ids: selectedDocuments.length > 0 ? selectedDocuments : undefined,
        context: {
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          model: selectedModel,
        }
      }

      const response: QueryResponse = await api.analyzeQuery(request)

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.response,
        timestamp: new Date(),
        metadata: {
          confidence: response.confidence,
          sources: response.sources,
          processing_time: response.processing_time,
          model_used: response.model_used,
          cached: response.cached,
        }
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error while processing your request. Please try again or contact support if the issue persists.',
        timestamp: new Date(),
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className="flex h-full">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-legal-500 rounded-full flex items-center justify-center">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">AI Legal Assistant</h1>
                <p className="text-sm text-gray-500">Powered by 30 specialized legal agents</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {/* Model Selector */}
              <div className="relative">
                <button
                  onClick={() => setShowModelDropdown(!showModelDropdown)}
                  className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <span className="font-medium">
                    {availableModels.find(m => m.id === selectedModel)?.name || 'Select Model'}
                  </span>
                  <ChevronDown className="w-4 h-4" />
                </button>

                {showModelDropdown && (
                  <div className="model-dropdown absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="py-1">
                      {availableModels.map((model) => (
                        <button
                          key={model.id}
                          onClick={() => {
                            setSelectedModel(model.id)
                            setShowModelDropdown(false)
                          }}
                          className={`w-full px-4 py-2 text-left hover:bg-gray-50 transition-colors ${
                            selectedModel === model.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                          }`}
                        >
                          <div className="font-medium">{model.name}</div>
                          <div className="text-xs text-gray-500">{model.description}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100" title="Settings">
                <Settings className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100" title="New Chat">
                <Plus className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Selected Documents */}
          {selectedDocuments.length > 0 && (
            <div className="mt-4">
              <div className="flex items-center space-x-2 mb-2">
                <FileText className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {selectedDocuments.length} document{selectedDocuments.length > 1 ? 's' : ''} selected for context
                </span>
                <button
                  onClick={() => setSelectedDocuments([])}
                  className="text-xs text-red-600 hover:text-red-800"
                >
                  Clear
                </button>
              </div>
              <div className="space-y-1">
                {selectedDocuments.map((docId) => {
                  const doc = availableDocuments.find(d => d.document_id === docId)
                  return (
                    <div key={docId} className="flex items-center justify-between p-2 bg-legal-50 rounded border">
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4 text-legal-600" />
                        <span className="text-sm text-gray-900 truncate">
                          {doc?.title || doc?.metadata?.filename || `Document ${docId.slice(0, 8)}`}
                        </span>
                      </div>
                      <button
                        onClick={() => setSelectedDocuments(prev => prev.filter(id => id !== docId))}
                        className="text-gray-400 hover:text-red-600"
                      >
                        ×
                      </button>
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-3xl rounded-lg px-4 py-3 ${
                  message.role === 'user'
                    ? 'bg-legal-500 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.role === 'user' ? 'bg-white bg-opacity-20' : 'bg-gray-200'
                  }`}>
                    {message.role === 'user' ? (
                      <User className="w-4 h-4" />
                    ) : (
                      <Bot className="w-4 h-4" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                      {message.content}
                    </p>

                    {/* Message Metadata */}
                    {message.metadata && (
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                        {message.metadata.confidence && (
                          <span>Confidence: {(message.metadata.confidence * 100).toFixed(1)}%</span>
                        )}
                        {message.metadata.processing_time && (
                          <span>Time: {message.metadata.processing_time.toFixed(2)}s</span>
                        )}
                        {message.metadata.model_used && (
                          <span>Model: {message.metadata.model_used}</span>
                        )}
                        {message.metadata.cached && (
                          <span className="text-green-600">Cached</span>
                        )}
                      </div>
                    )}

                    <div className="mt-1 text-xs opacity-70">
                      {formatTimestamp(message.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Loading indicator */}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 rounded-lg px-4 py-3 max-w-3xl">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
                    <Bot className="w-4 h-4" />
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce loading-dot-1"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce loading-dot-2"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce loading-dot-3"></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="bg-white border-t border-gray-200 px-6 py-4">
          <div className="flex items-end space-x-4">
            <div className="flex-1">
              <div className="relative">
                <textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask a legal question or describe your legal issue..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-legal-500 focus:border-transparent resize-none"
                  rows={3}
                  disabled={isLoading}
                />
                <div className="absolute bottom-2 right-2 flex items-center space-x-2">
                  <button
                    onClick={() => {
                      // Toggle document browser visibility by scrolling to it
                      const sidebar = document.querySelector('.document-sidebar');
                      if (sidebar) {
                        sidebar.scrollIntoView({ behavior: 'smooth' });
                      }
                    }}
                    className="p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="Attach documents"
                  >
                    <FileText className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => {
                      // TODO: Implement document search
                      alert('Document search coming soon!');
                    }}
                    className="p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="Search documents"
                  >
                    <Search className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
            <button
              type="button"
              onClick={handleSendMessage}
              disabled={!input.trim() || isLoading}
              className="bg-legal-500 text-white p-3 rounded-lg hover:bg-legal-600 focus:outline-none focus:ring-2 focus:ring-legal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Send message"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>

          <div className="mt-2 text-xs text-gray-500">
            Press Enter to send, Shift+Enter for new line
          </div>
        </div>
      </div>

      {/* Sidebar for document selection and context */}
      <div className="document-sidebar w-80 bg-gray-50 border-l border-gray-200">
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Context Documents</h3>

          {selectedDocuments.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-sm text-gray-500 mb-3">
                No documents selected for context
              </p>
              <button
                onClick={() => {/* TODO: Open document browser */}}
                className="text-sm text-legal-600 hover:text-legal-800 underline"
              >
                Browse documents
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              {selectedDocuments.map((docId) => (
                <div key={docId} className="flex items-center justify-between p-2 bg-white rounded border">
                  <div className="flex items-center space-x-2">
                    <FileText className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-900 truncate">
                      Document {docId.slice(0, 8)}...
                    </span>
                  </div>
                  <button
                    onClick={() => setSelectedDocuments(prev => prev.filter(id => id !== docId))}
                    className="text-gray-400 hover:text-red-600"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Document Browser */}
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Available Documents</h4>
            {loadingDocuments ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-legal-500"></div>
                <span className="ml-2 text-sm text-gray-600">Loading documents...</span>
              </div>
            ) : availableDocuments.length === 0 ? (
              <div className="text-center py-4">
                <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No documents available</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {availableDocuments.map((doc) => (
                  <div
                    key={doc.document_id}
                    className={`p-2 border rounded cursor-pointer transition-colors ${
                      selectedDocuments.includes(doc.document_id)
                        ? 'bg-legal-100 border-legal-300'
                        : 'bg-white border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      if (selectedDocuments.includes(doc.document_id)) {
                        setSelectedDocuments(prev => prev.filter(id => id !== doc.document_id))
                      } else {
                        setSelectedDocuments(prev => [...prev, doc.document_id])
                      }
                    }}
                  >
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-gray-400" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {doc.title || doc.metadata?.filename || `Document ${doc.document_id.slice(0, 8)}`}
                        </p>
                        <p className="text-xs text-gray-500">
                          {doc.document_type?.toUpperCase() || 'UNKNOWN'} • {doc.word_count || 0} words
                        </p>
                        <p className="text-xs text-gray-400 truncate">
                          {new Date(doc.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      {selectedDocuments.includes(doc.document_id) && (
                        <div className="w-4 h-4 bg-legal-500 rounded-full flex items-center justify-center">
                          <span className="text-xs text-white">✓</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default LegalChat