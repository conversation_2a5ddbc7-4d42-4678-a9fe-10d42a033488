import { useState, useEffect } from 'react'
import { Document } from '../types'

const DocumentLibrary = () => {
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([])
  const documentsPerPage = 12

  useEffect(() => {
    loadDocuments()
  }, [])

  useEffect(() => {
    filterDocuments()
  }, [documents, searchTerm, selectedType, selectedStatus])

  const loadDocuments = async () => {
    try {
      setLoading(true)
      // Fetch documents from API
      const response = await fetch('/api/v1/documents')
      if (response.ok) {
        const data = await response.json()
        // Transform API response to match our Document type
        const transformedDocs: Document[] = data.documents?.map((doc: any) => ({
          id: doc.document_id || doc.id,
          title: doc.title || 'Untitled Document',
          type: doc.document_type || doc.type || 'unknown',
          size: doc.size || 0,
          uploadDate: doc.created_at || doc.upload_date || new Date().toISOString(),
          status: doc.status || 'completed',
          chunks: doc.chunks_created || doc.chunks || 0,
          metadata: doc.metadata || {}
        })) || []

        setDocuments(transformedDocs)
      } else {
        // Fallback to mock data if API fails
        loadMockDocuments()
      }
    } catch (error) {
      console.error('Failed to load documents:', error)
      loadMockDocuments()
    } finally {
      setLoading(false)
    }
  }

  const loadMockDocuments = () => {
    const mockDocs: Document[] = [
      {
        id: 'doc_001',
        title: 'Service Agreement Template',
        type: 'pdf',
        size: 2457600, // 2.4MB
        uploadDate: '2024-08-25T10:30:00Z',
        status: 'completed',
        chunks: 45,
        metadata: { category: 'contracts', client: 'ABC Corp' }
      },
      {
        id: 'doc_002',
        title: 'Employment Contract - John Doe',
        type: 'docx',
        size: 512000, // 512KB
        uploadDate: '2024-08-24T14:15:00Z',
        status: 'completed',
        chunks: 23,
        metadata: { category: 'employment', employee: 'John Doe' }
      },
      {
        id: 'doc_003',
        title: 'Privacy Policy Review',
        type: 'pdf',
        size: 1843200, // 1.8MB
        uploadDate: '2024-08-23T09:45:00Z',
        status: 'processing',
        chunks: 0,
        metadata: { category: 'compliance', priority: 'high' }
      },
      {
        id: 'doc_004',
        title: 'Legal Research Memo - IP Rights',
        type: 'txt',
        size: 128000, // 128KB
        uploadDate: '2024-08-22T16:20:00Z',
        status: 'completed',
        chunks: 12,
        metadata: { category: 'research', topic: 'intellectual property' }
      },
      {
        id: 'doc_005',
        title: 'Court Filing Documents',
        type: 'zip',
        size: 5242880, // 5MB
        uploadDate: '2024-08-21T11:00:00Z',
        status: 'error',
        chunks: 0,
        metadata: { category: 'litigation', case: 'Smith vs Johnson' }
      },
      {
        id: 'doc_006',
        title: 'NDA Template 2024',
        type: 'docx',
        size: 768000, // 768KB
        uploadDate: '2024-08-20T13:30:00Z',
        status: 'completed',
        chunks: 18,
        metadata: { category: 'contracts', template: true }
      }
    ]

    setDocuments(mockDocs)
  }

  const filterDocuments = () => {
    let filtered = documents

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(doc =>
        doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.metadata?.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.metadata?.client?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Type filter
    if (selectedType !== 'all') {
      filtered = filtered.filter(doc => doc.type === selectedType)
    }

    // Status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(doc => doc.status === selectedStatus)
    }

    setFilteredDocuments(filtered)
    setCurrentPage(1) // Reset to first page when filtering
  }

  const getFileIcon = (type: string) => {
    const iconClass = "w-8 h-8 mr-3 flex-shrink-0"
    switch (type.toLowerCase()) {
      case 'pdf':
        return <div className={`${iconClass} bg-red-100 text-red-600 rounded flex items-center justify-center font-bold`}>PDF</div>
      case 'docx':
      case 'doc':
        return <div className={`${iconClass} bg-blue-100 text-blue-600 rounded flex items-center justify-center font-bold`}>DOC</div>
      case 'txt':
        return <div className={`${iconClass} bg-gray-100 text-gray-600 rounded flex items-center justify-center font-bold`}>TXT</div>
      case 'zip':
        return <div className={`${iconClass} bg-yellow-100 text-yellow-600 rounded flex items-center justify-center font-bold`}>ZIP</div>
      default:
        return <div className={`${iconClass} bg-gray-100 text-gray-600 rounded flex items-center justify-center font-bold`}>FILE</div>
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100'
      case 'processing':
        return 'text-yellow-600 bg-yellow-100'
      case 'error':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleDocumentSelect = (docId: string) => {
    setSelectedDocuments(prev =>
      prev.includes(docId)
        ? prev.filter(id => id !== docId)
        : [...prev, docId]
    )
  }

  const handleSelectAll = () => {
    if (selectedDocuments.length === currentDocuments.length) {
      setSelectedDocuments([])
    } else {
      setSelectedDocuments(currentDocuments.map(doc => doc.id))
    }
  }

  const handleDeleteSelected = async () => {
    if (selectedDocuments.length === 0) return

    if (confirm(`Delete ${selectedDocuments.length} selected document(s)?`)) {
      try {
        // Delete selected documents
        for (const docId of selectedDocuments) {
          await fetch(`/api/v1/documents/${docId}`, { method: 'DELETE' })
        }
        // Refresh document list
        loadDocuments()
        setSelectedDocuments([])
      } catch (error) {
        console.error('Failed to delete documents:', error)
        alert('Failed to delete some documents')
      }
    }
  }

  // Pagination
  const totalPages = Math.ceil(filteredDocuments.length / documentsPerPage)
  const startIndex = (currentPage - 1) * documentsPerPage
  const endIndex = startIndex + documentsPerPage
  const currentDocuments = filteredDocuments.slice(startIndex, endIndex)

  const uniqueTypes = Array.from(new Set(documents.map(doc => doc.type)))
  const uniqueStatuses = Array.from(new Set(documents.map(doc => doc.status)))

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Document Library</h1>
          <p className="mt-2 text-gray-600">
            Browse and manage your legal documents
          </p>
        </div>
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading documents...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Document Library</h1>
        <p className="mt-2 text-gray-600">
          Browse and manage your legal documents
        </p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white shadow rounded-lg p-4">
          <div className="text-2xl font-bold text-blue-600">{documents.length}</div>
          <p className="text-sm text-gray-600">Total Documents</p>
        </div>
        <div className="bg-white shadow rounded-lg p-4">
          <div className="text-2xl font-bold text-green-600">
            {documents.filter(d => d.status === 'completed').length}
          </div>
          <p className="text-sm text-gray-600">Processed</p>
        </div>
        <div className="bg-white shadow rounded-lg p-4">
          <div className="text-2xl font-bold text-yellow-600">
            {documents.filter(d => d.status === 'processing').length}
          </div>
          <p className="text-sm text-gray-600">Processing</p>
        </div>
        <div className="bg-white shadow rounded-lg p-4">
          <div className="text-2xl font-bold text-purple-600">
            {documents.reduce((sum, d) => sum + d.size, 0) / (1024 * 1024 * 1024)}
          </div>
          <p className="text-sm text-gray-600">Total Size (GB)</p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              title="Filter by document type"
            >
              <option value="all">All Types</option>
              {uniqueTypes.map(type => (
                <option key={type} value={type}>{type.toUpperCase()}</option>
              ))}
            </select>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              title="Filter by document status"
            >
              <option value="all">All Status</option>
              {uniqueStatuses.map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedDocuments.length > 0 && (
          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-md mb-4">
            <span className="text-sm text-blue-700">
              {selectedDocuments.length} document(s) selected
            </span>
            <div className="flex gap-2">
              <button
                onClick={handleDeleteSelected}
                className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
              >
                Delete Selected
              </button>
            </div>
          </div>
        )}

        {/* Document Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {currentDocuments.map((document) => (
            <div key={document.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center flex-1">
                  <input
                    type="checkbox"
                    checked={selectedDocuments.includes(document.id)}
                    onChange={() => handleDocumentSelect(document.id)}
                    className="mr-3"
                    title={`Select ${document.title}`}
                  />
                  {getFileIcon(document.type)}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">{document.title}</h3>
                    <p className="text-sm text-gray-500">{formatFileSize(document.size)}</p>
                  </div>
                </div>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                  {document.status}
                </span>
              </div>

              <div className="space-y-2 text-sm text-gray-600">
                <p><span className="font-medium">Type:</span> {document.type.toUpperCase()}</p>
                <p><span className="font-medium">Uploaded:</span> {formatDate(document.uploadDate)}</p>
                {document.chunks && document.chunks > 0 && (
                  <p><span className="font-medium">Chunks:</span> {document.chunks}</p>
                )}
                {document.metadata?.category && (
                  <p><span className="font-medium">Category:</span> {document.metadata.category}</p>
                )}
              </div>

              <div className="flex gap-2 mt-4">
                <button className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                  Download
                </button>
                <button className="flex-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm">
                  Preview
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-gray-700">
              Showing {startIndex + 1} to {Math.min(endIndex, filteredDocuments.length)} of {filteredDocuments.length} documents
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-2 text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {filteredDocuments.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No documents found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default DocumentLibrary