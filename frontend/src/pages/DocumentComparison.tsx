import React, { useState } from 'react'
import { Document } from '../types'
import { DocumentComparison } from '../components/DocumentComparison'

const DocumentComparisonPage: React.FC = () => {
  // Mock documents for demonstration
  const [leftDocument] = useState<Document>({
    id: 'doc1',
    title: 'Sample Contract A',
    type: 'contract',
    size: 1024,
    uploadDate: new Date().toISOString(),
    status: 'completed',
    chunks: 5,
    metadata: { author: 'Sample Author A' }
  })

  const [rightDocument] = useState<Document>({
    id: 'doc2',
    title: 'Sample Contract B',
    type: 'contract',
    size: 1024,
    uploadDate: new Date().toISOString(),
    status: 'completed',
    chunks: 5,
    metadata: { author: 'Sample Author B' }
  })

  const handleClose = () => {
    // Handle close action
    console.log('Document comparison closed')
  }

  const handleAskAI = (question: string, context: string) => {
    // Handle AI question
    console.log('AI Question:', question, 'Context:', context)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Document Comparison</h1>
        <p className="mt-2 text-gray-600">
          Compare legal documents with detailed diff analysis
        </p>
      </div>

      <DocumentComparison
        leftDocument={leftDocument}
        rightDocument={rightDocument}
        onClose={handleClose}
        onAskAI={handleAskAI}
      />
    </div>
  )
}

export default DocumentComparisonPage