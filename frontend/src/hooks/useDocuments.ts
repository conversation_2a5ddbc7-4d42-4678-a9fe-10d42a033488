import React, { useState, useCallback } from 'react';
import axios from 'axios';
import { Document, DocumentUploadResponse } from '../types';

export function useDocuments() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const fetchDocuments = useCallback(async () => {
    try {
      const response = await axios.get('/api/v1/documents');
      setDocuments(response.data.documents || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  }, []);

  const uploadDocument = useCallback(async (file: File, title?: string, documentType?: string) => {
    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('title', title || file.name);
      formData.append('document_type', documentType || 'general');

      const response = await axios.post<DocumentUploadResponse>(
        '/api/v1/documents/upload',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      // Refresh documents list
      await fetchDocuments();

      return response.data;
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  }, [fetchDocuments]);

  const deleteDocument = useCallback(async (documentId: string) => {
    try {
      await axios.delete(`/api/v1/documents/${documentId}`);
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }, []);

  // Fetch documents on mount
  React.useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  return {
    documents,
    uploadDocument,
    deleteDocument,
    isUploading,
    refreshDocuments: fetchDocuments,
  };
}