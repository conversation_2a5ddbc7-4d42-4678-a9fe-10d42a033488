import { useState, useCallback } from 'react';
import axios from 'axios';
import { Message, QueryRequest, QueryResponse } from '../types';

export function useChat() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);

  const sendMessage = useCallback(async (content: string, documentIds?: string[]) => {
    if (!content.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      role: 'user',
      content,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Prepare request
      const request: QueryRequest = {
        query: content,
        documentIds: documentIds || selectedDocuments,
        context: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        }
      };

      // Send to API
      const response = await axios.post<QueryResponse>(
        '/api/v1/analysis/query',
        request,
        {
          headers: {
            'Content-Type': 'application/json',
          }
        }
      );

      // Add assistant message
      const assistantMessage: Message = {
        id: `assistant_${Date.now()}`,
        role: 'assistant',
        content: response.data.response,
        timestamp: new Date(),
        agent: response.data.agentUsed,
        confidence: response.data.confidence,
        sources: response.data.sources,
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      const errorMessage: Message = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: `I apologize, but I encountered an error while processing your request: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [selectedDocuments]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setSelectedDocuments([]);
  }, []);

  return {
    messages,
    isLoading,
    sendMessage,
    clearMessages,
    selectedDocuments,
    setSelectedDocuments,
  };
}