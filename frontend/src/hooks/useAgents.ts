oimport { useState, useCallback, useEffect } from 'react';
import { Agent, AgentStats } from '../types';

export function useAgents() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [agentStats, setAgentStats] = useState<AgentStats>({
    totalAgents: 0,
    activeAgents: 0,
    idleAgents: 0,
    errorAgents: 0,
    averageResponseTime: 0,
    totalTasksProcessed: 0,
  });

  const fetchAgents = useCallback(async () => {
    try {
      // Mock data for now - replace with actual API call
      const mockAgents: Agent[] = [
        {
          id: 'agent_001',
          name: 'Contract Law Expert',
          specialization: 'contract_analysis',
          status: 'idle',
          domain: 'Contract Law',
          expertiseLevel: 'expert',
          totalTasks: 150,
          successRate: 0.95,
          averageResponseTime: 2.3,
        },
        {
          id: 'agent_002',
          name: 'Employment Law Specialist',
          specialization: 'employment_law',
          status: 'busy',
          domain: 'Employment Law',
          expertiseLevel: 'senior',
          totalTasks: 89,
          successRate: 0.92,
          averageResponseTime: 3.1,
        },
        {
          id: 'agent_003',
          name: 'Corporate Governance Advisor',
          specialization: 'corporate_law',
          status: 'idle',
          domain: 'Corporate Law',
          expertiseLevel: 'principal',
          totalTasks: 203,
          successRate: 0.97,
          averageResponseTime: 1.8,
        },
      ];

      setAgents(mockAgents);
      setAgentStats({
        totalAgents: mockAgents.length,
        activeAgents: mockAgents.filter(a => a.status === 'busy').length,
        idleAgents: mockAgents.filter(a => a.status === 'idle').length,
        errorAgents: mockAgents.filter(a => a.status === 'error').length,
        averageResponseTime: mockAgents.reduce((sum, a) => sum + a.averageResponseTime, 0) / mockAgents.length,
        totalTasksProcessed: mockAgents.reduce((sum, a) => sum + a.totalTasks, 0),
      });
    } catch (error) {
      console.error('Error fetching agents:', error);
    }
  }, []);

  const refreshAgents = useCallback(() => {
    fetchAgents();
  }, [fetchAgents]);

  // Initialize with mock data
  useEffect(() => {
    fetchAgents();
  }, [fetchAgents]);

  return {
    agents,
    agentStats,
    refreshAgents,
  };
}