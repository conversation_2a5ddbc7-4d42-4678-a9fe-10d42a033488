// Centralized configuration constants
// This is the single source of truth for all configuration values

export const CONFIG = {
  // API Configuration
  API: {
    BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
    TIMEOUT: 30000,
  },

  // Application settings
  APP: {
    NAME: 'AI Law Firm',
    VERSION: '1.0.0',
  },

  // Feature flags
  FEATURES: {
    ENABLE_DEBUG: import.meta.env.DEV,
    ENABLE_ANALYTICS: true,
  },
} as const

// Type-safe configuration access
export type Config = typeof CONFIG