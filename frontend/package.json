{"name": "ai-law-firm-frontend", "version": "1.0.0", "description": "AI Law Firm Frontend - React Application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.17.15", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/uuid": "^9.0.8", "axios": "^1.6.8", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "react-syntax-highlighter": "^15.5.0", "sonner": "^1.4.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.2.0"}}