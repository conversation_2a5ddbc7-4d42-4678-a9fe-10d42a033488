# Development and Testing Dependencies for AI Law Firm System
# These packages are needed for development, testing, and code quality

# Testing Framework
pytest==7.4.0
pytest-cov==4.1.0
pytest-mock==3.11.1
pytest-xdist==3.3.1
pytest-html==3.2.0
pytest-asyncio==0.21.1
pytest-benchmark==4.0.0

# Mocking and Fixtures
responses==0.23.1
freezegun==1.2.2
faker==18.10.1
factory-boy==3.2.1

# Code Quality and Linting
black==23.7.0
isort==5.12.0
flake8==6.0.0
mypy==1.5.1
pylint==2.17.0
bandit==1.7.5
pre-commit==3.4.0

# Type Checking and Documentation
typing-extensions>=4.7.0
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Development Tools
jupyter>=1.0.0
ipykernel>=6.0.0
notebook>=7.0.0

# Performance Testing
memory-profiler==0.61.0
psutil==5.9.4
line-profiler==4.1.0

# UI Testing
playwright==1.37.0
pytest-playwright==0.3.3
selenium>=4.0.0

# API Testing
requests-mock==1.11.0
httmock==1.4.0

# Database Testing
pytest-postgresql>=4.0.0

# Coverage and Reporting
coverage==7.3.0
coveralls==3.3.1

# Environment and Configuration
python-dotenv==1.0.0

# Utilities
click==8.1.3
tqdm>=4.65.0
colorama>=0.4.6

# Documentation
mkdocs==1.5.2
mkdocs-material==9.1.18
mkdocs-mermaid2-plugin>=1.0.0

# Version Control and CI/CD
gitpython>=3.1.0
PyGithub>=1.59.0

# Security Testing
safety==2.3.5
pip-audit==2.5.0

# All main requirements (for completeness in dev environment)
-r requirements.txt