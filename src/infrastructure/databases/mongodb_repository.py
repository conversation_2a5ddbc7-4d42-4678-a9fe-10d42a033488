"""
MongoDB Repository for AI Law Firm.

This module provides a repository pattern implementation for MongoDB,
handling unstructured data including document content, analysis results,
and complex metadata that doesn't fit well in relational tables.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
import pymongo.errors

from .data_models import DocumentContent, AnalysisResultContent
from utils.logging import get_logger
from utils.exceptions import DatabaseError


class MongoDBRepository:
    """
    MongoDB repository for unstructured data operations.

    Handles:
    - Document content storage and retrieval
    - Analysis result content storage
    - Complex metadata and search indexes
    - Full-text search capabilities
    """

    def __init__(self, connection_string: str, database_name: str = "ai_law_firm"):
        self.connection_string = connection_string
        self.database_name = database_name
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None

        # Collections
        self.documents_collection: Optional[AsyncIOMotorCollection] = None
        self.analysis_results_collection: Optional[AsyncIOMotorCollection] = None

    async def initialize(self) -> bool:
        """Initialize the MongoDB connection."""
        try:
            self.client = AsyncIOMotorClient(self.connection_string)
            self.database = self.client[self.database_name]

            # Initialize collections
            self.documents_collection = self.database.documents
            self.analysis_results_collection = self.database.analysis_results

            # Create indexes
            await self._create_indexes()

            # Test connection
            await self.client.admin.command('ping')

            self.logger.info("MongoDB connection initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize MongoDB: {str(e)}")
            return False

    async def shutdown(self) -> bool:
        """Shutdown the MongoDB connection."""
        try:
            if self.client:
                self.client.close()
                self.logger.info("MongoDB connection closed")
            return True
        except Exception as e:
            self.logger.error(f"Error closing MongoDB connection: {str(e)}")
            return False

    async def _create_indexes(self):
        """Create database indexes for performance."""
        try:
            # Document indexes
            await self.documents_collection.create_index("document_id", unique=True)
            await self.documents_collection.create_index("created_at")
            await self.documents_collection.create_index("updated_at")
            await self.documents_collection.create_index([
                ("content", "text"),
                ("metadata.document_type", 1),
                ("metadata.filename", 1)
            ])

            # Analysis results indexes
            await self.analysis_results_collection.create_index("result_id", unique=True)
            await self.analysis_results_collection.create_index("session_id")
            await self.analysis_results_collection.create_index("agent_name")
            await self.analysis_results_collection.create_index("created_at")
            await self.analysis_results_collection.create_index([
                ("response", "text"),
                ("query", "text")
            ])

            self.logger.info("MongoDB indexes created successfully")
        except Exception as e:
            self.logger.error(f"Error creating MongoDB indexes: {str(e)}")

    # Document Content Operations
    async def store_document_content(self, document: DocumentContent) -> str:
        """Store document content in MongoDB."""
        try:
            # Prepare document for storage
            doc_dict = document.to_dict()
            doc_dict["_id"] = document.document_id  # Use document_id as MongoDB _id

            # Insert or update
            result = await self.documents_collection.replace_one(
                {"_id": document.document_id},
                doc_dict,
                upsert=True
            )

            if result.upserted_id or result.modified_count > 0:
                self.logger.info(f"Document content stored: {document.document_id}")
                return document.document_id
            else:
                raise DatabaseError("Failed to store document content")

        except pymongo.errors.DuplicateKeyError:
            raise DatabaseError("Document with this ID already exists")
        except Exception as e:
            self.logger.error(f"Error storing document content: {str(e)}")
            raise DatabaseError(f"Failed to store document content: {str(e)}")

    async def get_document_content(self, document_id: str) -> Optional[DocumentContent]:
        """Retrieve document content from MongoDB."""
        try:
            doc = await self.documents_collection.find_one({"_id": document_id})
            if doc:
                return DocumentContent.from_dict(doc)
            return None
        except Exception as e:
            self.logger.error(f"Error retrieving document content: {str(e)}")
            return None

    async def update_document_content(
        self,
        document_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update document content."""
        try:
            updates["updated_at"] = datetime.utcnow()

            result = await self.documents_collection.update_one(
                {"_id": document_id},
                {"$set": updates}
            )

            success = result.modified_count > 0
            if success:
                self.logger.info(f"Document content updated: {document_id}")
            return success
        except Exception as e:
            self.logger.error(f"Error updating document content: {str(e)}")
            return False

    async def delete_document_content(self, document_id: str) -> bool:
        """Delete document content."""
        try:
            result = await self.documents_collection.delete_one({"_id": document_id})
            success = result.deleted_count > 0
            if success:
                self.logger.info(f"Document content deleted: {document_id}")
            return success
        except Exception as e:
            self.logger.error(f"Error deleting document content: {str(e)}")
            return False

    async def search_documents(
        self,
        query: str,
        document_type: Optional[str] = None,
        limit: int = 20,
        skip: int = 0
    ) -> List[DocumentContent]:
        """Search documents using text search."""
        try:
            search_query = {"$text": {"$search": query}}

            if document_type:
                search_query["metadata.document_type"] = document_type

            cursor = self.documents_collection.find(search_query).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)

            return [DocumentContent.from_dict(doc) for doc in documents]
        except Exception as e:
            self.logger.error(f"Error searching documents: {str(e)}")
            return []

    async def get_documents_by_user(self, user_id: str, limit: int = 50) -> List[DocumentContent]:
        """Get documents for a specific user."""
        try:
            # Note: This assumes we store user_id in the document metadata
            cursor = self.documents_collection.find(
                {"metadata.user_id": user_id}
            ).sort("created_at", -1).limit(limit)

            documents = await cursor.to_list(length=limit)
            return [DocumentContent.from_dict(doc) for doc in documents]
        except Exception as e:
            self.logger.error(f"Error getting user documents: {str(e)}")
            return []

    # Analysis Results Operations
    async def store_analysis_result(self, result: AnalysisResultContent) -> str:
        """Store analysis result in MongoDB."""
        try:
            # Prepare result for storage
            result_dict = result.to_dict()
            result_dict["_id"] = result.result_id  # Use result_id as MongoDB _id

            # Insert or update
            mongo_result = await self.analysis_results_collection.replace_one(
                {"_id": result.result_id},
                result_dict,
                upsert=True
            )

            if mongo_result.upserted_id or mongo_result.modified_count > 0:
                self.logger.info(f"Analysis result stored: {result.result_id}")
                return result.result_id
            else:
                raise DatabaseError("Failed to store analysis result")

        except pymongo.errors.DuplicateKeyError:
            raise DatabaseError("Analysis result with this ID already exists")
        except Exception as e:
            self.logger.error(f"Error storing analysis result: {str(e)}")
            raise DatabaseError(f"Failed to store analysis result: {str(e)}")

    async def get_analysis_result(self, result_id: str) -> Optional[AnalysisResultContent]:
        """Retrieve analysis result from MongoDB."""
        try:
            doc = await self.analysis_results_collection.find_one({"_id": result_id})
            if doc:
                return AnalysisResultContent.from_dict(doc)
            return None
        except Exception as e:
            self.logger.error(f"Error retrieving analysis result: {str(e)}")
            return None

    async def get_session_results(self, session_id: str) -> List[AnalysisResultContent]:
        """Get all analysis results for a session."""
        try:
            cursor = self.analysis_results_collection.find({"session_id": session_id})
            results = await cursor.to_list(length=None)

            return [AnalysisResultContent.from_dict(result) for result in results]
        except Exception as e:
            self.logger.error(f"Error getting session results: {str(e)}")
            return []

    async def search_analysis_results(
        self,
        query: str,
        agent_name: Optional[str] = None,
        limit: int = 20,
        skip: int = 0
    ) -> List[AnalysisResultContent]:
        """Search analysis results using text search."""
        try:
            search_query = {"$text": {"$search": query}}

            if agent_name:
                search_query["agent_name"] = agent_name

            cursor = self.analysis_results_collection.find(search_query).skip(skip).limit(limit)
            results = await cursor.to_list(length=limit)

            return [AnalysisResultContent.from_dict(result) for result in results]
        except Exception as e:
            self.logger.error(f"Error searching analysis results: {str(e)}")
            return []

    async def get_user_analysis_history(
        self,
        user_id: str,
        limit: int = 50
    ) -> List[AnalysisResultContent]:
        """Get analysis history for a user."""
        try:
            # This would require joining with PostgreSQL data
            # For now, we'll search by a user_id field if it exists
            cursor = self.analysis_results_collection.find(
                {"metadata.user_id": user_id}
            ).sort("created_at", -1).limit(limit)

            results = await cursor.to_list(length=limit)
            return [AnalysisResultContent.from_dict(result) for result in results]
        except Exception as e:
            self.logger.error(f"Error getting user analysis history: {str(e)}")
            return []

    # Bulk Operations
    async def bulk_store_documents(self, documents: List[DocumentContent]) -> List[str]:
        """Bulk store multiple documents."""
        try:
            operations = []
            for doc in documents:
                doc_dict = doc.to_dict()
                doc_dict["_id"] = doc.document_id

                operations.append(
                    pymongo.ReplaceOne(
                        {"_id": doc.document_id},
                        doc_dict,
                        upsert=True
                    )
                )

            if operations:
                result = await self.documents_collection.bulk_write(operations)
                stored_ids = [doc.document_id for doc in documents]
                self.logger.info(f"Bulk stored {len(stored_ids)} documents")
                return stored_ids
            return []
        except Exception as e:
            self.logger.error(f"Error in bulk document storage: {str(e)}")
            return []

    async def bulk_store_results(self, results: List[AnalysisResultContent]) -> List[str]:
        """Bulk store multiple analysis results."""
        try:
            operations = []
            for result in results:
                result_dict = result.to_dict()
                result_dict["_id"] = result.result_id

                operations.append(
                    pymongo.ReplaceOne(
                        {"_id": result.result_id},
                        result_dict,
                        upsert=True
                    )
                )

            if operations:
                mongo_result = await self.analysis_results_collection.bulk_write(operations)
                stored_ids = [result.result_id for result in results]
                self.logger.info(f"Bulk stored {len(stored_ids)} analysis results")
                return stored_ids
            return []
        except Exception as e:
            self.logger.error(f"Error in bulk result storage: {str(e)}")
            return []

    # Analytics and Reporting
    async def get_document_stats(self) -> Dict[str, Any]:
        """Get document collection statistics."""
        try:
            stats = await self.database.command("collStats", "documents")

            # Get document type breakdown
            pipeline = [
                {"$group": {"_id": "$metadata.document_type", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}}
            ]

            type_breakdown = await self.documents_collection.aggregate(pipeline).to_list(length=None)

            return {
                "total_documents": stats.get("count", 0),
                "total_size_bytes": stats.get("size", 0),
                "average_size_bytes": stats.get("avgObjSize", 0),
                "type_breakdown": {item["_id"]: item["count"] for item in type_breakdown if item["_id"]},
                "last_updated": datetime.utcnow()
            }
        except Exception as e:
            self.logger.error(f"Error getting document stats: {str(e)}")
            return {}

    async def get_analysis_stats(self) -> Dict[str, Any]:
        """Get analysis results collection statistics."""
        try:
            stats = await self.database.command("collStats", "analysis_results")

            # Get agent breakdown
            pipeline = [
                {"$group": {"_id": "$agent_name", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}}
            ]

            agent_breakdown = await self.analysis_results_collection.aggregate(pipeline).to_list(length=None)

            return {
                "total_results": stats.get("count", 0),
                "total_size_bytes": stats.get("size", 0),
                "average_size_bytes": stats.get("avgObjSize", 0),
                "agent_breakdown": {item["_id"]: item["count"] for item in agent_breakdown if item["_id"]},
                "last_updated": datetime.utcnow()
            }
        except Exception as e:
            self.logger.error(f"Error getting analysis stats: {str(e)}")
            return {}

    # Maintenance Operations
    async def cleanup_old_data(self, days_old: int = 90) -> Dict[str, int]:
        """Clean up old data beyond retention period."""
        try:
            cutoff_date = datetime.utcnow()
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days_old)

            # Remove old documents (soft delete by marking as archived)
            doc_result = await self.documents_collection.update_many(
                {"created_at": {"$lt": cutoff_date}},
                {"$set": {"metadata.archived": True, "updated_at": datetime.utcnow()}}
            )

            # Remove old analysis results
            result_result = await self.analysis_results_collection.delete_many(
                {"created_at": {"$lt": cutoff_date}}
            )

            cleanup_stats = {
                "archived_documents": doc_result.modified_count,
                "deleted_results": result_result.deleted_count
            }

            self.logger.info(f"Data cleanup completed: {cleanup_stats}")
            return cleanup_stats
        except Exception as e:
            self.logger.error(f"Error during data cleanup: {str(e)}")
            return {"error": str(e)}

    # Health Check
    async def health_check(self) -> Dict[str, Any]:
        """Perform MongoDB health check."""
        try:
            # Test connection
            await self.client.admin.command('ping')

            # Get database stats
            db_stats = await self.database.command("dbStats")

            return {
                "healthy": True,
                "database": self.database_name,
                "collections": len(await self.database.list_collection_names()),
                "data_size_bytes": db_stats.get("dataSize", 0),
                "storage_size_bytes": db_stats.get("storageSize", 0),
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }