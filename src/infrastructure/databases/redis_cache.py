"""
Redis Cache for AI Law Firm.

This module provides Redis-based caching and session management
for the AI Law Firm system, including temporary data storage,
session handling, and performance optimization.
"""

import asyncio
import json
import pickle
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import redis.asyncio as redis

from .data_models import CacheEntry, SessionData
from utils.logging import get_logger
from utils.exceptions import DatabaseError


class RedisCache:
    """
    Redis-based caching and session management.

    Features:
    - Session storage and management
    - Temporary data caching
    - Rate limiting data
    - Performance metrics caching
    - Distributed locking
    """

    def __init__(self, connection_string: str, default_ttl: int = 3600):
        self.connection_string = connection_string
        self.default_ttl = default_ttl  # Default TTL in seconds
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        self.redis: Optional[redis.Redis] = None
        self.is_connected = False

    async def initialize(self) -> bool:
        """Initialize Redis connection."""
        try:
            self.redis = redis.from_url(self.connection_string)

            # Test connection
            await self.redis.ping()
            self.is_connected = True

            self.logger.info("Redis connection initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize Redis: {str(e)}")
            return False

    async def shutdown(self) -> bool:
        """Shutdown Redis connection."""
        try:
            if self.redis:
                await self.redis.close()
                self.is_connected = False
                self.logger.info("Redis connection closed")
            return True
        except Exception as e:
            self.logger.error(f"Error closing Redis connection: {str(e)}")
            return False

    # Session Management
    async def store_session(self, session_data: SessionData) -> bool:
        """Store session data in Redis."""
        try:
            if not self.redis:
                raise DatabaseError("Redis not connected")

            key = f"session:{session_data.session_id}"
            data = session_data.to_dict()

            # Store session data
            await self.redis.setex(
                key,
                self.default_ttl,
                json.dumps(data, default=str)
            )

            # Also store user -> session mapping for cleanup
            user_sessions_key = f"user_sessions:{session_data.user_id}"
            await self.redis.sadd(user_sessions_key, session_data.session_id)
            await self.redis.expire(user_sessions_key, self.default_ttl)

            self.logger.debug(f"Session stored: {session_data.session_id}")
            return True
        except Exception as e:
            self.logger.error(f"Error storing session: {str(e)}")
            return False

    async def get_session(self, session_id: str) -> Optional[SessionData]:
        """Retrieve session data from Redis."""
        try:
            if not self.redis:
                return None

            key = f"session:{session_id}"
            data_str = await self.redis.get(key)

            if data_str:
                data = json.loads(data_str)
                # Update last accessed time
                data["last_accessed"] = datetime.utcnow()
                await self.redis.setex(key, self.default_ttl, json.dumps(data, default=str))

                return SessionData.from_dict(data)
            return None
        except Exception as e:
            self.logger.error(f"Error retrieving session: {str(e)}")
            return None

    async def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update session data."""
        try:
            if not self.redis:
                return False

            key = f"session:{session_id}"
            data_str = await self.redis.get(key)

            if data_str:
                data = json.loads(data_str)
                data.update(updates)
                data["last_accessed"] = datetime.utcnow()

                await self.redis.setex(key, self.default_ttl, json.dumps(data, default=str))
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error updating session: {str(e)}")
            return False

    async def delete_session(self, session_id: str) -> bool:
        """Delete session data."""
        try:
            if not self.redis:
                return False

            key = f"session:{session_id}"
            result = await self.redis.delete(key)

            # Also remove from user sessions set
            session_data = await self.get_session(session_id)
            if session_data:
                user_sessions_key = f"user_sessions:{session_data.user_id}"
                await self.redis.srem(user_sessions_key, session_id)

            success = result > 0
            if success:
                self.logger.debug(f"Session deleted: {session_id}")
            return success
        except Exception as e:
            self.logger.error(f"Error deleting session: {str(e)}")
            return False

    async def get_user_sessions(self, user_id: str) -> List[str]:
        """Get all active session IDs for a user."""
        try:
            if not self.redis:
                return []

            user_sessions_key = f"user_sessions:{user_id}"
            sessions = await self.redis.smembers(user_sessions_key)
            return [s.decode('utf-8') for s in sessions]
        except Exception as e:
            self.logger.error(f"Error getting user sessions: {str(e)}")
            return []

    async def invalidate_user_sessions(self, user_id: str) -> int:
        """Invalidate all sessions for a user."""
        try:
            if not self.redis:
                return 0

            session_ids = await self.get_user_sessions(user_id)
            if not session_ids:
                return 0

            # Delete all session keys
            session_keys = [f"session:{sid}" for sid in session_ids]
            result = await self.redis.delete(*session_keys)

            # Clean up user sessions set
            user_sessions_key = f"user_sessions:{user_id}"
            await self.redis.delete(user_sessions_key)

            self.logger.info(f"Invalidated {result} sessions for user {user_id}")
            return result
        except Exception as e:
            self.logger.error(f"Error invalidating user sessions: {str(e)}")
            return 0

    # Generic Caching
    async def set_cache(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None,
        use_pickle: bool = False
    ) -> bool:
        """Store data in cache."""
        try:
            if not self.redis:
                return False

            cache_key = f"cache:{key}"
            ttl = ttl_seconds or self.default_ttl

            if use_pickle:
                # Use pickle for complex objects
                data = pickle.dumps(value)
                await self.redis.setex(cache_key, ttl, data)
            else:
                # Use JSON for simple data
                data = json.dumps(value, default=str)
                await self.redis.setex(cache_key, ttl, data)

            return True
        except Exception as e:
            self.logger.error(f"Error setting cache: {str(e)}")
            return False

    async def get_cache(
        self,
        key: str,
        use_pickle: bool = False
    ) -> Optional[Any]:
        """Retrieve data from cache."""
        try:
            if not self.redis:
                return None

            cache_key = f"cache:{key}"
            data = await self.redis.get(cache_key)

            if data:
                if use_pickle:
                    return pickle.loads(data)
                else:
                    return json.loads(data)
            return None
        except Exception as e:
            self.logger.error(f"Error getting cache: {str(e)}")
            return None

    async def delete_cache(self, key: str) -> bool:
        """Delete data from cache."""
        try:
            if not self.redis:
                return False

            cache_key = f"cache:{key}"
            result = await self.redis.delete(cache_key)
            return result > 0
        except Exception as e:
            self.logger.error(f"Error deleting cache: {str(e)}")
            return False

    async def exists_cache(self, key: str) -> bool:
        """Check if cache key exists."""
        try:
            if not self.redis:
                return False

            cache_key = f"cache:{key}"
            return await self.redis.exists(cache_key) > 0
        except Exception as e:
            self.logger.error(f"Error checking cache existence: {str(e)}")
            return False

    # Rate Limiting
    async def check_rate_limit(
        self,
        key: str,
        limit: int,
        window_seconds: int
    ) -> tuple[bool, int]:
        """
        Check if rate limit is exceeded.

        Returns:
            tuple: (allowed: bool, remaining: int)
        """
        try:
            if not self.redis:
                return True, limit  # Allow if Redis is down

            rate_key = f"ratelimit:{key}"
            current_time = int(datetime.utcnow().timestamp())

            # Use Redis sorted set to track requests
            # Remove old entries outside the window
            await self.redis.zremrangebyscore(rate_key, 0, current_time - window_seconds)

            # Count current requests in window
            count = await self.redis.zcard(rate_key)

            if count >= limit:
                return False, 0

            # Add current request
            await self.redis.zadd(rate_key, {str(current_time): current_time})
            await self.redis.expire(rate_key, window_seconds)

            remaining = limit - count - 1
            return True, max(0, remaining)
        except Exception as e:
            self.logger.error(f"Error checking rate limit: {str(e)}")
            return True, limit  # Allow on error

    # Performance Metrics Caching
    async def cache_performance_metrics(
        self,
        operation: str,
        metrics: Dict[str, Any],
        ttl_seconds: int = 300
    ) -> bool:
        """Cache performance metrics."""
        try:
            if not self.redis:
                return False

            key = f"metrics:{operation}:{int(datetime.utcnow().timestamp())}"
            return await self.set_cache(key, metrics, ttl_seconds)
        except Exception as e:
            self.logger.error(f"Error caching performance metrics: {str(e)}")
            return False

    async def get_performance_metrics(
        self,
        operation: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get recent performance metrics for an operation."""
        try:
            if not self.redis:
                return []

            pattern = f"metrics:{operation}:*"
            keys = await self.redis.keys(pattern)

            if not keys:
                return []

            # Get the most recent metrics
            keys = keys[-limit:] if len(keys) > limit else keys

            metrics = []
            for key in keys:
                data = await self.redis.get(key)
                if data:
                    metrics.append(json.loads(data))

            return sorted(metrics, key=lambda x: x.get('timestamp', ''), reverse=True)
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {str(e)}")
            return []

    # Distributed Locking
    async def acquire_lock(
        self,
        lock_key: str,
        ttl_seconds: int = 30,
        retry_count: int = 3,
        retry_delay: float = 0.1
    ) -> Optional[str]:
        """
        Acquire a distributed lock.

        Returns:
            lock_token: Unique token for the lock, None if failed
        """
        try:
            if not self.redis:
                return None

            lock_token = f"lock:{lock_key}:{datetime.utcnow().isoformat()}"
            full_key = f"distributed_lock:{lock_key}"

            for attempt in range(retry_count):
                # Try to set the lock
                result = await self.redis.set(
                    full_key,
                    lock_token,
                    ex=ttl_seconds,
                    nx=True  # Only set if key doesn't exist
                )

                if result:
                    return lock_token

                # Wait before retry
                if attempt < retry_count - 1:
                    await asyncio.sleep(retry_delay)

            return None
        except Exception as e:
            self.logger.error(f"Error acquiring lock: {str(e)}")
            return None

    async def release_lock(self, lock_key: str, lock_token: str) -> bool:
        """Release a distributed lock."""
        try:
            if not self.redis:
                return False

            full_key = f"distributed_lock:{lock_key}"

            # Use Lua script for atomic check-and-delete
            script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """

            result = await self.redis.eval(script, 1, full_key, lock_token)
            return result == 1
        except Exception as e:
            self.logger.error(f"Error releasing lock: {str(e)}")
            return False

    # Bulk Operations
    async def set_multiple_cache(self, key_value_pairs: Dict[str, Any]) -> bool:
        """Set multiple cache entries."""
        try:
            if not self.redis:
                return False

            pipeline = self.redis.pipeline()
            for key, value in key_value_pairs.items():
                cache_key = f"cache:{key}"
                data = json.dumps(value, default=str)
                pipeline.setex(cache_key, self.default_ttl, data)

            await pipeline.execute()
            return True
        except Exception as e:
            self.logger.error(f"Error setting multiple cache entries: {str(e)}")
            return False

    async def get_multiple_cache(self, keys: List[str]) -> Dict[str, Any]:
        """Get multiple cache entries."""
        try:
            if not self.redis:
                return {}

            cache_keys = [f"cache:{key}" for key in keys]
            values = await self.redis.mget(cache_keys)

            result = {}
            for key, value in zip(keys, values):
                if value:
                    result[key] = json.loads(value)

            return result
        except Exception as e:
            self.logger.error(f"Error getting multiple cache entries: {str(e)}")
            return {}

    # Maintenance
    async def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions (handled automatically by Redis TTL)."""
        try:
            if not self.redis:
                return 0

            # Get all session keys
            session_keys = await self.redis.keys("session:*")
            expired_count = 0

            for key in session_keys:
                # Check if key still exists (TTL not expired)
                if not await self.redis.exists(key):
                    expired_count += 1

            self.logger.info(f"Found {expired_count} expired sessions")
            return expired_count
        except Exception as e:
            self.logger.error(f"Error cleaning up expired sessions: {str(e)}")
            return 0

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            if not self.redis:
                return {"error": "Redis not connected"}

            info = await self.redis.info()

            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "total_connections_received": info.get("total_connections_received", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "cache_hit_ratio": (
                    info.get("keyspace_hits", 0) /
                    (info.get("keyspace_hits", 0) + info.get("keyspace_misses", 1)) * 100
                ),
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {"error": str(e)}

    # Health Check
    async def health_check(self) -> Dict[str, Any]:
        """Perform Redis health check."""
        try:
            if not self.redis:
                return {"healthy": False, "error": "Redis client not initialized"}

            # Test basic operations
            await self.redis.ping()

            # Get some basic stats
            info = await self.redis.info("server")

            return {
                "healthy": True,
                "version": info.get("redis_version", "unknown"),
                "uptime_seconds": info.get("uptime_in_seconds", 0),
                "connected_clients": info.get("connected_clients", 0),
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }