"""
Database infrastructure for AI Law Firm.

This module provides a multi-database architecture supporting:
- PostgreSQL for relational data
- MongoDB for unstructured data
- Redis for caching
- Qdrant for vector operations
"""

from .postgres_repository import PostgreSQLRepository
from .mongodb_repository import MongoDBRepository
from .redis_cache import RedisCache
from .data_models import (
    User, UserSession, APIKey, Document, AnalysisSession,
    AnalysisResult, UsageStats, AuditLog
)

__all__ = [
    "PostgreSQLRepository",
    "MongoDBRepository",
    "RedisCache",
    "User",
    "UserSession",
    "APIKey",
    "Document",
    "AnalysisSession",
    "AnalysisResult",
    "UsageStats",
    "AuditLog",
]