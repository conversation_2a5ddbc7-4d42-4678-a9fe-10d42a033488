"""
PostgreSQL Repository for AI Law Firm.

This module provides a repository pattern implementation for PostgreSQL,
handling all relational data operations including users, sessions, documents,
analysis sessions, and audit logging.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, date
from contextlib import asynccontextmanager
import asyncpg
import json

from .data_models import (
    User, UserSession, APIKey, Document, AnalysisSession,
    AnalysisResult, UsageStats, AuditLog, DocumentType,
    AnalysisType, ProcessingStatus, AnalysisStatus
)
from utils.logging import get_logger
from utils.exceptions import DatabaseError


class PostgreSQLRepository:
    """
    PostgreSQL repository for relational data operations.

    Handles:
    - User management
    - Session management
    - Document metadata
    - Analysis sessions and results
    - Usage statistics
    - Audit logging
    """

    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self._pool: Optional[asyncpg.Pool] = None

    async def initialize(self) -> bool:
        """Initialize the database connection pool."""
        try:
            self._pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            self.logger.info("PostgreSQL connection pool initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize PostgreSQL pool: {str(e)}")
            return False

    async def shutdown(self) -> bool:
        """Shutdown the database connection pool."""
        try:
            if self._pool:
                await self._pool.close()
                self.logger.info("PostgreSQL connection pool closed")
            return True
        except Exception as e:
            self.logger.error(f"Error closing PostgreSQL pool: {str(e)}")
            return False

    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        if not self._pool:
            raise DatabaseError("Database pool not initialized")

        conn = None
        try:
            conn = await self._pool.acquire()
            yield conn
        finally:
            if conn:
                await self._pool.release(conn)

    # User Management
    async def create_user(self, user: User) -> str:
        """Create a new user."""
        async with self.get_connection() as conn:
            try:
                await conn.execute("""
                    INSERT INTO users (
                        user_id, email, username, full_name, hashed_password,
                        is_active, is_superuser, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """, user.user_id, user.email, user.username, user.full_name,
                user.hashed_password, user.is_active, user.is_superuser,
                user.created_at, user.updated_at)

                # Log user creation
                await self._log_audit(
                    user_id=user.user_id,
                    action="user_created",
                    resource_type="user",
                    resource_id=user.user_id,
                    new_values=user.to_dict()
                )

                return user.user_id
            except asyncpg.UniqueViolationError:
                raise DatabaseError("User with this email or username already exists")
            except Exception as e:
                self.logger.error(f"Error creating user: {str(e)}")
                raise DatabaseError(f"Failed to create user: {str(e)}")

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        async with self.get_connection() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM users WHERE user_id = $1 AND is_active = true
            """, user_id)

            return User.from_dict(dict(row)) if row else None

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        async with self.get_connection() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM users WHERE email = $1 AND is_active = true
            """, email)

            return User.from_dict(dict(row)) if row else None

    async def update_user(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update user information."""
        async with self.get_connection() as conn:
            try:
                # Get old values for audit
                old_user = await self.get_user_by_id(user_id)
                if not old_user:
                    return False

                # Build update query dynamically
                set_parts = []
                values = []
                param_count = 1

                for key, value in updates.items():
                    if key in ['email', 'username', 'full_name', 'is_active', 'is_superuser']:
                        set_parts.append(f"{key} = ${param_count}")
                        values.append(value)
                        param_count += 1

                if not set_parts:
                    return False

                values.append(user_id)
                query = f"""
                    UPDATE users
                    SET {', '.join(set_parts)}, updated_at = NOW()
                    WHERE user_id = ${param_count}
                """

                await conn.execute(query, *values)

                # Log update
                await self._log_audit(
                    user_id=user_id,
                    action="user_updated",
                    resource_type="user",
                    resource_id=user_id,
                    old_values=old_user.to_dict(),
                    new_values=updates
                )

                return True
            except Exception as e:
                self.logger.error(f"Error updating user {user_id}: {str(e)}")
                return False

    # Session Management
    async def create_session(self, session: UserSession) -> str:
        """Create a new user session."""
        async with self.get_connection() as conn:
            try:
                await conn.execute("""
                    INSERT INTO user_sessions (
                        session_id, user_id, session_token, ip_address,
                        user_agent, created_at, expires_at, is_active
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """, session.session_id, session.user_id, session.session_token,
                session.ip_address, session.user_agent, session.created_at,
                session.expires_at, session.is_active)

                return session.session_id
            except Exception as e:
                self.logger.error(f"Error creating session: {str(e)}")
                raise DatabaseError(f"Failed to create session: {str(e)}")

    async def get_session(self, session_token: str) -> Optional[UserSession]:
        """Get session by token."""
        async with self.get_connection() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM user_sessions
                WHERE session_token = $1 AND is_active = true
                AND expires_at > NOW()
            """, session_token)

            return UserSession.from_dict(dict(row)) if row else None

    async def invalidate_session(self, session_token: str) -> bool:
        """Invalidate a user session."""
        async with self.get_connection() as conn:
            try:
                result = await conn.execute("""
                    UPDATE user_sessions
                    SET is_active = false
                    WHERE session_token = $1
                """, session_token)

                return result == "UPDATE 1"
            except Exception as e:
                self.logger.error(f"Error invalidating session: {str(e)}")
                return False

    # Document Management
    async def create_document(self, document: Document) -> str:
        """Create a new document record."""
        async with self.get_connection() as conn:
            try:
                await conn.execute("""
                    INSERT INTO documents (
                        document_id, user_id, filename, original_filename,
                        file_size_bytes, mime_type, document_type, checksum,
                        storage_path, mongodb_id, qdrant_id, processing_status,
                        created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                """, document.document_id, document.user_id, document.filename,
                document.original_filename, document.file_size_bytes, document.mime_type,
                document.document_type.value, document.checksum, document.storage_path,
                document.mongodb_id, document.qdrant_id, document.processing_status.value,
                document.created_at)

                # Log document creation
                await self._log_audit(
                    user_id=document.user_id,
                    action="document_uploaded",
                    resource_type="document",
                    resource_id=document.document_id,
                    new_values=document.to_dict()
                )

                return document.document_id
            except Exception as e:
                self.logger.error(f"Error creating document: {str(e)}")
                raise DatabaseError(f"Failed to create document: {str(e)}")

    async def get_document(self, document_id: str) -> Optional[Document]:
        """Get document by ID."""
        async with self.get_connection() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM documents WHERE document_id = $1
            """, document_id)

            return Document.from_dict(dict(row)) if row else None

    async def get_user_documents(self, user_id: str, limit: int = 50) -> List[Document]:
        """Get documents for a user."""
        async with self.get_connection() as conn:
            rows = await conn.fetch("""
                SELECT * FROM documents
                WHERE user_id = $1
                ORDER BY created_at DESC
                LIMIT $2
            """, user_id, limit)

            return [Document.from_dict(dict(row)) for row in rows]

    async def update_document_status(
        self,
        document_id: str,
        status: ProcessingStatus,
        error_message: Optional[str] = None
    ) -> bool:
        """Update document processing status."""
        async with self.get_connection() as conn:
            try:
                if status == ProcessingStatus.COMPLETED:
                    await conn.execute("""
                        UPDATE documents
                        SET processing_status = $1, processed_at = NOW(), error_message = NULL
                        WHERE document_id = $2
                    """, status.value, document_id)
                elif status == ProcessingStatus.FAILED:
                    await conn.execute("""
                        UPDATE documents
                        SET processing_status = $1, processed_at = NOW(), error_message = $2
                        WHERE document_id = $2
                    """, status.value, error_message, document_id)
                else:
                    await conn.execute("""
                        UPDATE documents
                        SET processing_status = $1
                        WHERE document_id = $2
                    """, status.value, document_id)

                return True
            except Exception as e:
                self.logger.error(f"Error updating document status: {str(e)}")
                return False

    # Analysis Management
    async def create_analysis_session(self, session: AnalysisSession) -> str:
        """Create a new analysis session."""
        async with self.get_connection() as conn:
            try:
                await conn.execute("""
                    INSERT INTO analysis_sessions (
                        session_id, user_id, document_id, analysis_type,
                        custom_query, status, created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """, session.session_id, session.user_id, session.document_id,
                session.analysis_type.value, session.custom_query,
                session.status.value, session.created_at)

                return session.session_id
            except Exception as e:
                self.logger.error(f"Error creating analysis session: {str(e)}")
                raise DatabaseError(f"Failed to create analysis session: {str(e)}")

    async def update_analysis_session_status(
        self,
        session_id: str,
        status: AnalysisStatus,
        total_cost: Optional[float] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """Update analysis session status."""
        async with self.get_connection() as conn:
            try:
                if status == AnalysisStatus.RUNNING:
                    await conn.execute("""
                        UPDATE analysis_sessions
                        SET status = $1, started_at = NOW()
                        WHERE session_id = $2
                    """, status.value, session_id)
                elif status in [AnalysisStatus.COMPLETED, AnalysisStatus.FAILED]:
                    await conn.execute("""
                        UPDATE analysis_sessions
                        SET status = $1, completed_at = NOW(),
                            total_cost = COALESCE($2, total_cost),
                            error_message = $3
                        WHERE session_id = $4
                    """, status.value, total_cost, error_message, session_id)
                else:
                    await conn.execute("""
                        UPDATE analysis_sessions
                        SET status = $1
                        WHERE session_id = $2
                    """, status.value, session_id)

                return True
            except Exception as e:
                self.logger.error(f"Error updating analysis session: {str(e)}")
                return False

    async def create_analysis_result(self, result: AnalysisResult) -> str:
        """Create a new analysis result."""
        async with self.get_connection() as conn:
            try:
                await conn.execute("""
                    INSERT INTO analysis_results (
                        result_id, session_id, agent_name, model_used,
                        confidence_score, processing_time, token_count,
                        cost_estimate, mongodb_result_id, created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                """, result.result_id, result.session_id, result.agent_name,
                result.model_used, result.confidence_score, result.processing_time,
                result.token_count, result.cost_estimate, result.mongodb_result_id,
                result.created_at)

                return result.result_id
            except Exception as e:
                self.logger.error(f"Error creating analysis result: {str(e)}")
                raise DatabaseError(f"Failed to create analysis result: {str(e)}")

    # Usage Statistics
    async def record_usage(
        self,
        user_id: str,
        documents_processed: int = 0,
        analyses_performed: int = 0,
        total_tokens: int = 0,
        total_cost: float = 0.0,
        api_calls: int = 0
    ) -> bool:
        """Record usage statistics for a user."""
        async with self.get_connection() as conn:
            try:
                today = date.today().isoformat()

                # Try to update existing record
                result = await conn.execute("""
                    UPDATE usage_stats
                    SET documents_processed = documents_processed + $2,
                        analyses_performed = analyses_performed + $3,
                        total_tokens = total_tokens + $4,
                        total_cost = total_cost + $5,
                        api_calls = api_calls + $6,
                        updated_at = NOW()
                    WHERE user_id = $1 AND date = $7
                """, user_id, documents_processed, analyses_performed,
                total_tokens, total_cost, api_calls, today)

                # If no row was updated, create new record
                if result == "UPDATE 0":
                    await conn.execute("""
                        INSERT INTO usage_stats (
                            user_id, date, documents_processed, analyses_performed,
                            total_tokens, total_cost, api_calls, created_at, updated_at
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
                    """, user_id, today, documents_processed, analyses_performed,
                    total_tokens, total_cost, api_calls)

                return True
            except Exception as e:
                self.logger.error(f"Error recording usage: {str(e)}")
                return False

    async def get_usage_stats(self, user_id: str, days: int = 30) -> List[UsageStats]:
        """Get usage statistics for a user."""
        async with self.get_connection() as conn:
            rows = await conn.fetch("""
                SELECT * FROM usage_stats
                WHERE user_id = $1 AND date >= CURRENT_DATE - INTERVAL '%s days'
                ORDER BY date DESC
            """ % days, user_id)

            return [UsageStats.from_dict(dict(row)) for row in rows]

    # Audit Logging
    async def _log_audit(
        self,
        user_id: Optional[str] = None,
        action: str = "",
        resource_type: str = "",
        resource_id: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """Log an audit event."""
        try:
            async with self.get_connection() as conn:
                await conn.execute("""
                    INSERT INTO audit_log (
                        user_id, action, resource_type, resource_id,
                        old_values, new_values, ip_address, user_agent, created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
                """, user_id, action, resource_type, resource_id,
                json.dumps(old_values) if old_values else None,
                json.dumps(new_values) if new_values else None,
                ip_address, user_agent)
        except Exception as e:
            self.logger.error(f"Error logging audit event: {str(e)}")

    # Generic Query Methods
    async def fetch_all(self, query: str, *args) -> List[Dict[str, Any]]:
        """Execute a query and return all rows as dictionaries."""
        async with self.get_connection() as conn:
            try:
                rows = await conn.fetch(query, *args)
                return [dict(row) for row in rows]
            except Exception as e:
                self.logger.error(f"Error executing fetch_all query: {str(e)}")
                raise DatabaseError(f"Failed to execute query: {str(e)}")

    async def fetch_one(self, query: str, *args) -> Optional[Dict[str, Any]]:
        """Execute a query and return the first row as a dictionary."""
        async with self.get_connection() as conn:
            try:
                row = await conn.fetchrow(query, *args)
                return dict(row) if row else None
            except Exception as e:
                self.logger.error(f"Error executing fetch_one query: {str(e)}")
                raise DatabaseError(f"Failed to execute query: {str(e)}")

    async def execute_query(self, query: str, *args) -> str:
        """Execute a query that doesn't return rows (INSERT, UPDATE, DELETE)."""
        async with self.get_connection() as conn:
            try:
                result = await conn.execute(query, *args)
                return result
            except Exception as e:
                self.logger.error(f"Error executing query: {str(e)}")
                raise DatabaseError(f"Failed to execute query: {str(e)}")

    # Health Check
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        try:
            async with self.get_connection() as conn:
                # Simple query to test connection
                result = await conn.fetchval("SELECT COUNT(*) FROM users")

                return {
                    "healthy": True,
                    "user_count": result,
                    "timestamp": datetime.utcnow().isoformat()
                }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }


# Alias for backward compatibility
PostgresRepository = PostgreSQLRepository