"""
Data models for AI Law Firm multi-database architecture.

This module defines the data structures used across PostgreSQL, MongoDB, and Redis
for comprehensive data persistence of documents, analyses, and system interactions.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum
from uuid import uuid4


class DocumentType(str, Enum):
    """Document type enumeration."""
    CONTRACT = "contract"
    LEGAL_BRIEF = "legal_brief"
    COURT_DOCUMENT = "court_document"
    REGULATORY_FILING = "regulatory_filing"
    AGREEMENT = "agreement"
    MEMORANDUM = "memorandum"
    CORRESPONDENCE = "correspondence"
    OTHER = "other"


class AnalysisType(str, Enum):
    """Analysis type enumeration."""
    CONTRACT_REVIEW = "contract_review"
    LEGAL_RESEARCH = "legal_research"
    RISK_ASSESSMENT = "risk_assessment"
    COMPLIANCE_CHECK = "compliance_check"
    CUSTOM_QUERY = "custom_query"


class ProcessingStatus(str, Enum):
    """Document processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AnalysisStatus(str, Enum):
    """Analysis status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class User:
    """User model for PostgreSQL."""
    email: str
    user_id: str = field(default_factory=lambda: str(uuid4()))
    username: Optional[str] = None
    full_name: Optional[str] = None
    hashed_password: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage."""
        return {
            "user_id": self.user_id,
            "email": self.email,
            "username": self.username,
            "full_name": self.full_name,
            "hashed_password": self.hashed_password,
            "is_active": self.is_active,
            "is_superuser": self.is_superuser,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "last_login": self.last_login
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class UserSession:
    """User session model for PostgreSQL."""
    user_id: str
    session_token: str
    expires_at: datetime
    session_id: str = field(default_factory=lambda: str(uuid4()))
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    is_active: bool = True

    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "session_token": self.session_token,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "created_at": self.created_at,
            "expires_at": self.expires_at,
            "is_active": self.is_active
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserSession':
        return cls(**data)


@dataclass
class APIKey:
    """API key model for PostgreSQL."""
    user_id: str
    provider: str  # 'openai', 'ollama', etc.
    key_name: str
    encrypted_key: str  # Encrypted API key
    key_id: str = field(default_factory=lambda: str(uuid4()))
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_used: Optional[datetime] = None
    usage_count: int = 0

    def to_dict(self) -> Dict[str, Any]:
        return {
            "key_id": self.key_id,
            "user_id": self.user_id,
            "provider": self.provider,
            "key_name": self.key_name,
            "encrypted_key": self.encrypted_key,
            "is_active": self.is_active,
            "created_at": self.created_at,
            "last_used": self.last_used,
            "usage_count": self.usage_count
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'APIKey':
        return cls(**data)


@dataclass
class Document:
    """Document metadata model for PostgreSQL."""
    user_id: str
    filename: str
    original_filename: str
    file_size_bytes: int
    document_id: str = field(default_factory=lambda: str(uuid4()))
    mime_type: Optional[str] = None
    document_type: DocumentType = DocumentType.OTHER
    checksum: Optional[str] = None
    storage_path: Optional[str] = None
    mongodb_id: Optional[str] = None  # Reference to MongoDB document
    qdrant_id: Optional[str] = None   # Reference to Qdrant vector
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "document_id": self.document_id,
            "user_id": self.user_id,
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_size_bytes": self.file_size_bytes,
            "mime_type": self.mime_type,
            "document_type": self.document_type.value,
            "checksum": self.checksum,
            "storage_path": self.storage_path,
            "mongodb_id": self.mongodb_id,
            "qdrant_id": self.qdrant_id,
            "processing_status": self.processing_status.value,
            "created_at": self.created_at,
            "processed_at": self.processed_at,
            "error_message": self.error_message
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        # Convert string values back to enums
        if 'document_type' in data:
            data['document_type'] = DocumentType(data['document_type'])
        if 'processing_status' in data:
            data['processing_status'] = ProcessingStatus(data['processing_status'])
        return cls(**data)


@dataclass
class DocumentContent:
    """Document content model for MongoDB."""
    document_id: str
    content: str
    _id: Optional[str] = None
    chunks: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    processing_metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "_id": self._id,
            "document_id": self.document_id,
            "content": self.content,
            "chunks": self.chunks,
            "metadata": self.metadata,
            "processing_metadata": self.processing_metadata,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DocumentContent':
        return cls(**data)


@dataclass
class AnalysisSession:
    """Analysis session model for PostgreSQL."""
    user_id: str
    document_id: str
    analysis_type: AnalysisType
    session_id: str = field(default_factory=lambda: str(uuid4()))
    custom_query: Optional[str] = None
    status: AnalysisStatus = AnalysisStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_cost: float = 0.0
    error_message: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "document_id": self.document_id,
            "analysis_type": self.analysis_type.value,
            "custom_query": self.custom_query,
            "status": self.status.value,
            "created_at": self.created_at,
            "started_at": self.started_at,
            "completed_at": self.completed_at,
            "total_cost": self.total_cost,
            "error_message": self.error_message
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisSession':
        # Convert string values back to enums
        if 'analysis_type' in data:
            data['analysis_type'] = AnalysisType(data['analysis_type'])
        if 'status' in data:
            data['status'] = AnalysisStatus(data['status'])
        return cls(**data)


@dataclass
class AnalysisResult:
    """Analysis result metadata model for PostgreSQL."""
    session_id: str
    agent_name: str
    result_id: str = field(default_factory=lambda: str(uuid4()))
    model_used: Optional[str] = None
    confidence_score: Optional[float] = None
    processing_time: Optional[float] = None
    token_count: Optional[int] = None
    cost_estimate: Optional[float] = None
    mongodb_result_id: Optional[str] = None  # Reference to full result in MongoDB
    created_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "result_id": self.result_id,
            "session_id": self.session_id,
            "agent_name": self.agent_name,
            "model_used": self.model_used,
            "confidence_score": self.confidence_score,
            "processing_time": self.processing_time,
            "token_count": self.token_count,
            "cost_estimate": self.cost_estimate,
            "mongodb_result_id": self.mongodb_result_id,
            "created_at": self.created_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisResult':
        return cls(**data)


@dataclass
class AnalysisResultContent:
    """Analysis result content model for MongoDB."""
    result_id: str
    session_id: str
    agent_name: str
    query: str
    response: str
    _id: Optional[str] = None
    sources: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "_id": self._id,
            "result_id": self.result_id,
            "session_id": self.session_id,
            "agent_name": self.agent_name,
            "query": self.query,
            "response": self.response,
            "sources": self.sources,
            "metadata": self.metadata,
            "created_at": self.created_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisResultContent':
        return cls(**data)


@dataclass
class UsageStats:
    """Usage statistics model for PostgreSQL."""
    user_id: str
    date: str  # YYYY-MM-DD format
    stat_id: str = field(default_factory=lambda: str(uuid4()))
    documents_processed: int = 0
    analyses_performed: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    api_calls: int = 0
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "stat_id": self.stat_id,
            "user_id": self.user_id,
            "date": self.date,
            "documents_processed": self.documents_processed,
            "analyses_performed": self.analyses_performed,
            "total_tokens": self.total_tokens,
            "total_cost": self.total_cost,
            "api_calls": self.api_calls,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UsageStats':
        return cls(**data)


@dataclass
class AuditLog:
    """Audit log model for PostgreSQL."""
    action: str
    resource_type: str
    audit_id: str = field(default_factory=lambda: str(uuid4()))
    user_id: Optional[str] = None
    resource_id: Optional[str] = None
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "audit_id": self.audit_id,
            "user_id": self.user_id,
            "action": self.action,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "old_values": self.old_values,
            "new_values": self.new_values,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "created_at": self.created_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuditLog':
        return cls(**data)


@dataclass
class CacheEntry:
    """Cache entry model for Redis."""
    key: str
    value: Any
    ttl_seconds: Optional[int] = None
    created_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "value": self.value,
            "ttl_seconds": self.ttl_seconds,
            "created_at": self.created_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        return cls(**data)


@dataclass
class SessionData:
    """Session data model for Redis."""
    session_id: str
    user_id: str
    data: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_accessed: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "data": self.data,
            "created_at": self.created_at,
            "last_accessed": self.last_accessed
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SessionData':
        return cls(**data)