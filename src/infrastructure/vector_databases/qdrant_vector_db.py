"""
Qdrant Vector Database Implementation.

This module provides a Qdrant-based implementation of the VectorDatabase abstract base class,
enabling seamless integration with Qdrant vector database for vector operations.
"""

import asyncio
import time
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams, PointStruct

from core.knowledge.base import VectorDatabase
from utils.logging import get_logger


class QdrantVectorDatabase(VectorDatabase):
    """
    Qdrant implementation of the VectorDatabase interface.

    This class provides a complete implementation of vector database operations
    using Qdrant as the underlying vector storage system.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.client: Optional[QdrantClient] = None
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Extract configuration with environment variable fallbacks
        import os
        self.url = config.get("url", os.getenv("QDRANT_URL", "http://localhost:6333"))
        self.api_key = config.get("api_key", os.getenv("QDRANT_API_KEY"))
        self.collection_name = config.get("collection_name", os.getenv("QDRANT_COLLECTION", "legal_documents"))

    async def connect(self) -> bool:
        """Establish connection to Qdrant database."""
        try:
            self.logger.info(f"Connecting to Qdrant at {self.url}")

            # Create Qdrant client
            if self.api_key:
                self.client = QdrantClient(url=self.url, api_key=self.api_key)
            else:
                self.client = QdrantClient(url=self.url)

            # Test connection
            self.client.get_collections()
            self._is_connected = True
            self.logger.info("Successfully connected to Qdrant")
            return True

        except Exception as e:
            self.logger.error(f"Failed to connect to Qdrant: {str(e)}")
            self._is_connected = False
            return False

    async def disconnect(self) -> bool:
        """Close connection to Qdrant database."""
        try:
            if self.client:
                # Qdrant client doesn't have explicit disconnect method
                # Connection is managed automatically
                self.client = None
                self._is_connected = False
                self.logger.info("Disconnected from Qdrant")
                return True
            return True
        except Exception as e:
            self.logger.error(f"Error disconnecting from Qdrant: {str(e)}")
            return False

    async def is_connected(self) -> bool:
        """Check if database connection is active."""
        if not self.client:
            return False

        try:
            # Test connection by getting collections
            self.client.get_collections()
            return True
        except Exception:
            self._is_connected = False
            return False

    async def create_collection(
        self,
        collection_name: str,
        vector_dimension: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Create a new vector collection."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            self.logger.info(f"Creating collection: {collection_name}")

            # Create collection with vector parameters
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_dimension,
                    distance=Distance.COSINE
                )
            )

            self.logger.info(f"Successfully created collection: {collection_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create collection {collection_name}: {str(e)}")
            return False

    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a vector collection."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            self.logger.info(f"Deleting collection: {collection_name}")
            self.client.delete_collection(collection_name=collection_name)
            self.logger.info(f"Successfully deleted collection: {collection_name}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete collection {collection_name}: {str(e)}")
            return False

    async def collection_exists(self, collection_name: str) -> bool:
        """Check if collection exists."""
        if not self._is_connected or not self.client:
            return False

        try:
            collections = self.client.get_collections()
            return collection_name in [col.name for col in collections.collections]
        except Exception as e:
            self.logger.error(f"Error checking collection existence: {str(e)}")
            return False

    async def insert_vectors(
        self,
        collection_name: str,
        vectors: List[List[float]],
        payloads: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Insert vectors into the collection."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            # Generate IDs if not provided
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in range(len(vectors))]

            # Create points
            points = []
            for i, (vector, payload) in enumerate(zip(vectors, payloads)):
                point = PointStruct(
                    id=ids[i],
                    vector=vector,
                    payload=payload
                )
                points.append(point)

            # Insert points
            self.client.upsert(
                collection_name=collection_name,
                points=points
            )

            self.logger.info(f"Inserted {len(points)} vectors into {collection_name}")
            return ids

        except Exception as e:
            self.logger.error(f"Failed to insert vectors: {str(e)}")
            return []

    async def search_vectors(
        self,
        collection_name: str,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.0,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            # Prepare search parameters
            search_params = {
                "collection_name": collection_name,
                "query_vector": query_vector,
                "limit": limit,
                "score_threshold": score_threshold
            }

            # Add filters if provided
            if filters:
                search_params["query_filter"] = filters

            # Perform search
            results = self.client.search(**search_params)

            # Convert results to expected format
            formatted_results = []
            for result in results:
                formatted_result = {
                    "id": str(result.id),
                    "score": result.score,
                    "payload": result.payload
                }
                formatted_results.append(formatted_result)

            self.logger.info(f"Found {len(formatted_results)} results for vector search")
            return formatted_results

        except Exception as e:
            self.logger.error(f"Failed to search vectors: {str(e)}")
            return []

    async def delete_vectors(
        self,
        collection_name: str,
        vector_ids: List[str]
    ) -> bool:
        """Delete vectors from collection."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            # Delete points by IDs
            self.client.delete(
                collection_name=collection_name,
                points_selector=vector_ids
            )

            self.logger.info(f"Deleted {len(vector_ids)} vectors from {collection_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete vectors: {str(e)}")
            return False

    async def update_vectors(
        self,
        collection_name: str,
        vector_ids: List[str],
        vectors: List[List[float]],
        payloads: List[Dict[str, Any]]
    ) -> bool:
        """Update existing vectors."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            # Create updated points
            points = []
            for i, (vector_id, vector, payload) in enumerate(zip(vector_ids, vectors, payloads)):
                point = PointStruct(
                    id=vector_id,
                    vector=vector,
                    payload=payload
                )
                points.append(point)

            # Update points
            self.client.upsert(
                collection_name=collection_name,
                points=points
            )

            self.logger.info(f"Updated {len(points)} vectors in {collection_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to update vectors: {str(e)}")
            return False

    async def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """Get collection statistics."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            # Get collection info
            collection_info = self.client.get_collection(collection_name)

            # Get point count
            count_result = self.client.count(collection_name=collection_name)

            # Extract stats from the collection info structure
            # CollectionInfo has direct attributes, not nested result
            config = collection_info.config
            params = config.params
            vectors_config = params.vectors

            stats = {
                "collection_name": collection_name,
                "total_vectors": count_result.count,
                "indexed_documents": count_result.count,  # Use total count as indexed count
                "total_documents": count_result.count,     # Use total count as total documents
                "vector_dimension": vectors_config.size,
                "distance_metric": vectors_config.distance,
                "status": "active",
                "index_size_bytes": 0  # Placeholder, not available in this API
            }

            return stats

        except Exception as e:
            self.logger.error(f"Failed to get collection stats: {str(e)}")
            return {
                "collection_name": collection_name,
                "error": str(e),
                "status": "error"
            }

    async def list_collections(self) -> List[str]:
        """List all collections."""
        if not self._is_connected or not self.client:
            return []

        try:
            collections = self.client.get_collections()
            return [col.name for col in collections.collections]
        except Exception as e:
            self.logger.error(f"Failed to list collections: {str(e)}")
            return []

    async def optimize_collection(self, collection_name: str) -> bool:
        """Optimize collection performance."""
        if not self._is_connected or not self.client:
            raise RuntimeError("Not connected to Qdrant")

        try:
            # Force optimization
            self.client.update_collection(
                collection_name=collection_name,
                optimizer_config={
                    "indexing_threshold": 10000,
                    "memmap_threshold": 50000
                }
            )

            self.logger.info(f"Optimized collection: {collection_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to optimize collection {collection_name}: {str(e)}")
            return False