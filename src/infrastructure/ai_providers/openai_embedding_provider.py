"""
OpenAI Embedding Provider for Knowledge Base.

This module provides an embedding provider that implements the EmbeddingProviderProtocol
required by the Knowledge Base for generating vector embeddings from text.
"""

from typing import List
from openai import AsyncOpenAI

from core.knowledge.base import EmbeddingProviderProtocol
from utils.logging import get_logger


class OpenAIEmbeddingProvider(EmbeddingProviderProtocol):
    """
    OpenAI embedding provider for generating vector embeddings.

    Uses OpenAI's text-embedding-ada-002 model for generating embeddings
    suitable for vector similarity search.
    """

    def __init__(self, api_key: str, model: str = "text-embedding-ada-002"):
        self.api_key = api_key
        self.model = model
        self.client = None
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Initialize client if API key is provided
        if api_key and api_key not in ["dummy-key-for-development", "", None]:
            self.client = AsyncOpenAI(api_key=api_key)
        else:
            self.logger.warning("OpenAI API key not configured. Using mock embeddings.")

    def encode(self, texts: List[str]) -> List[List[float]]:
        """Encode texts into embeddings."""
        if not self.client:
            # Return mock embeddings for development
            return self._get_mock_embeddings(texts)

        # Note: This is a synchronous method but we're using async client
        # In production, this should be made async
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self._async_encode(texts))
            loop.close()
            return result
        except Exception as e:
            self.logger.error(f"Failed to encode texts: {str(e)}")
            return self._get_mock_embeddings(texts)

    def encode_single(self, text: str) -> List[float]:
        """Encode a single text into embedding."""
        result = self.encode([text])
        return result[0] if result else []

    async def _async_encode(self, texts: List[str]) -> List[List[float]]:
        """Async method to encode texts."""
        if not self.client:
            return self._get_mock_embeddings(texts)

        try:
            response = await self.client.embeddings.create(
                input=texts,
                model=self.model
            )

            embeddings = []
            for data in response.data:
                embeddings.append(data.embedding)

            return embeddings

        except Exception as e:
            self.logger.error(f"Failed to get embeddings from OpenAI: {str(e)}")
            return self._get_mock_embeddings(texts)

    def _get_mock_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate mock embeddings for development/testing."""
        import random
        import math

        embeddings = []
        for text in texts:
            # Generate a deterministic mock embedding based on text hash
            # This ensures consistent results for the same text
            text_hash = hash(text)
            random.seed(text_hash)

            # Generate a 1536-dimensional embedding (same as OpenAI ada-002)
            embedding = []
            for i in range(1536):
                # Generate values between -1 and 1
                value = random.uniform(-1.0, 1.0)
                embedding.append(value)

            # Normalize the embedding
            magnitude = math.sqrt(sum(x * x for x in embedding))
            if magnitude > 0:
                embedding = [x / magnitude for x in embedding]

            embeddings.append(embedding)

        return embeddings