"""
OpenAI provider implementation for the AI Law Firm system.

This module provides:
- OpenAI GPT model integration
- Cost-effective model selection (GPT-4o-mini by default)
- Token usage tracking and cost estimation
- Error handling and retry logic
- Streaming support for real-time responses
"""
import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from openai import Async<PERSON>penA<PERSON>, OpenAIError
from openai.types.chat import ChatCompletionMessageParam

from .base import AIProvider, AgentResponse, TokenUsage, StreamingProviderMixin
from core.config.settings import OpenAIConfig
from utils.exceptions import AIProviderError, RateLimitError, TokenLimitError
from utils.logging import get_logger


class OpenAIProvider(AIProvider, StreamingProviderMixin):
    """
    OpenAI provider implementation.

    Supports GPT-4o, GPT-4o-mini, and other OpenAI models with:
    - Cost-effective model selection
    - Token usage tracking
    - Automatic retries
    - Streaming responses
    """

    # Cost per 1K tokens (as of 2024)
    COST_PER_1K_TOKENS = {
        "gpt-5-nano": {"input": 0.00005, "output": 0.0002},  # Ultra-efficient nano model
        "gpt-5-mini": {"input": 0.0001, "output": 0.0004},  # Next-gen ultra-efficient
        "gpt-5": {"input": 0.002, "output": 0.008},  # Next-gen flagship model
        "gpt-4o": {"input": 0.005, "output": 0.015},
        "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},  # Cost-effective default
        "gpt-4-turbo": {"input": 0.01, "output": 0.03},
        "gpt-4": {"input": 0.03, "output": 0.06},
        "gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
    }

    def __init__(self, config: OpenAIConfig):
        super().__init__("openai", config)
        self.client = None
        self._initialize_client()
    def _map_model_name(self, model: str) -> str:
        """Map model names to actual OpenAI API model names."""
        # GPT-5 models exist and should be sent as-is
        # Only map legacy or incorrect model names
        model_mapping = {
            # Legacy mappings for backward compatibility
            "gpt5": "gpt-5",  # gpt5 -> gpt-5
            "gpt4": "gpt-4o",  # gpt4 -> gpt-4o
        }
        return model_mapping.get(model, model)
    def _get_mock_response(self, prompt: str, max_tokens: int = 1000) -> AgentResponse:
        """Generate mock response for development without API key."""
        import time
        import uuid

        # Simulate processing time
        processing_time = 0.5 + (len(prompt.split()) * 0.01)

        # Generate mock response based on prompt
        if "contract" in prompt.lower():
            content = "This appears to be a contract analysis request. In a real implementation, I would analyze the contract terms, identify key clauses, and provide detailed legal insights. For now, this is a mock response to demonstrate the system functionality."
        elif "legal" in prompt.lower():
            content = "I understand you're asking about legal matters. In a production environment, I would provide comprehensive legal analysis based on relevant case law, statutes, and legal principles. This mock response shows the system's capability to handle legal queries."
        else:
            content = f"I received your query: '{prompt[:100]}{'...' if len(prompt) > 100 else ''}'. In a real implementation with a valid OpenAI API key, I would provide a comprehensive response using GPT-5 Mini. For now, this demonstrates the system's architecture and response format."

        # Log the mock response for debugging
        self.logger.info(f"Generated mock response with content length: {len(content)}")

        return AgentResponse(
            content=content,
            confidence_score=0.85,
            metadata={
                "model": "gpt-5-mini",
                "finish_reason": "stop",
                "temperature": 0.1,
                "max_tokens": max_tokens
            },
            processing_time=processing_time,
            model_used="gpt-5-mini",
            token_usage=None,  # Mock doesn't track tokens
            provider_name=self.name
        )

    def _initialize_client(self):
        """Initialize the OpenAI client."""
        try:
            # Check if API key is a placeholder
            if self.config.api_key in ["dummy-key-for-development", "", None]:
                self.logger.warning("OpenAI API key not configured. Using mock responses for development.")
                self.client = None  # Will use mock responses
                return

            self.client = AsyncOpenAI(
                api_key=self.config.api_key,
                timeout=self.config.timeout
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize OpenAI client: {str(e)}")
            raise AIProviderError(f"OpenAI client initialization failed: {str(e)}")

    async def generate_response(
        self,
        prompt: str,
        context: Optional[List[str]] = None,
        temperature: float = 0.1,
        max_tokens: int = 1000,
        model: Optional[str] = None,
        **kwargs
    ) -> AgentResponse:
        """
        Generate a response using OpenAI models.

        Args:
            prompt: The main prompt to send
            context: Optional context documents
            temperature: Controls randomness (0.0 to 2.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Additional OpenAI-specific parameters

        Returns:
            AgentResponse: Standardized response object
        """
        start_time = time.time()

        try:
            # Check if using mock mode (no API key)
            if self.client is None:
                self.logger.info("Using mock response (no API key configured)")
                return self._get_mock_response(prompt, max_tokens)

            # Prepare messages
            messages = self._prepare_messages(prompt, context)

            # Make API call with retry logic
            response = await self._make_api_call_with_retry(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                **kwargs
            )

            processing_time = time.time() - start_time

            # Extract response content
            content = response.choices[0].message.content or ""

            # Calculate token usage
            token_usage = self._calculate_token_usage(response.usage)

            # Create standardized response
            agent_response = AgentResponse(
                content=content,
                confidence_score=self._calculate_confidence(response),
                metadata={
                    "model": response.model,
                    "finish_reason": response.choices[0].finish_reason,
                    "temperature": temperature,
                    "max_tokens": max_tokens
                },
                processing_time=processing_time,
                model_used=response.model,
                token_usage=token_usage,
                provider_name=self.name
            )

            # Update metrics
            self._update_metrics(True, processing_time, token_usage)

            return agent_response

        except OpenAIError as e:
            processing_time = time.time() - start_time
            self._handle_error(e, "generate_response")

            if "rate limit" in str(e).lower():
                raise RateLimitError(f"OpenAI rate limit exceeded: {str(e)}")
            elif "token" in str(e).lower() and "limit" in str(e).lower():
                raise TokenLimitError(f"OpenAI token limit exceeded: {str(e)}")
            else:
                raise AIProviderError(f"OpenAI API error: {str(e)}")

        except Exception as e:
            processing_time = time.time() - start_time
            self._handle_error(e, "generate_response")
            raise AIProviderError(f"Unexpected error in OpenAI provider: {str(e)}")

    def _prepare_messages(
        self,
        prompt: str,
        context: Optional[List[str]] = None
    ) -> List[ChatCompletionMessageParam]:
        """Prepare messages for OpenAI API."""
        messages = []

        # Add system message
        messages.append({
            "role": "system",
            "content": "You are a legal assistant specializing in contract analysis, legal research, and strategic advice. Provide accurate, well-reasoned responses based on legal principles and best practices."
        })

        # Add context documents if provided
        if context:
            context_text = "\n\n".join(context)
            messages.append({
                "role": "system",
                "content": f"Use the following context information to inform your response:\n\n{context_text}"
            })

        # Add user prompt
        messages.append({
            "role": "user",
            "content": prompt
        })

        return messages

    async def _make_api_call_with_retry(
        self,
        messages: List[ChatCompletionMessageParam],
        temperature: float,
        max_tokens: int,
        model: Optional[str] = None,
        max_retries: int = 3,
        **kwargs
    ):
        """Make API call with exponential backoff retry logic."""
        last_exception = None

        for attempt in range(max_retries):
            try:
                # Use provided model or fall back to config model
                model_to_use = model or self.config.model
                # Map model name to actual OpenAI API model
                actual_model = self._map_model_name(model_to_use)

                # Prepare API call parameters
                api_params = {
                    "model": actual_model,
                    "messages": messages,
                    "temperature": temperature,
                    **kwargs
                }

                # Handle parameter differences for GPT-5 models
                if model_to_use and model_to_use.startswith("gpt-5"):
                    # GPT-5 models use max_completion_tokens instead of max_tokens
                    api_params["max_completion_tokens"] = max_tokens
                    # GPT-5 models only support temperature = 1.0
                    api_params["temperature"] = 1.0
                else:
                    # Older models use max_tokens and support custom temperature
                    api_params["max_tokens"] = max_tokens

                return await self.client.chat.completions.create(**api_params)
            except (OpenAIError, asyncio.TimeoutError) as e:
                last_exception = e

                if attempt < max_retries - 1:
                    # Exponential backoff: 1s, 2s, 4s
                    delay = 2 ** attempt
                    self.logger.warning(f"OpenAI API call failed (attempt {attempt + 1}), retrying in {delay}s: {str(e)}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"OpenAI API call failed after {max_retries} attempts: {str(e)}")

        raise last_exception
        """Make API call with exponential backoff retry logic."""
        last_exception = None

        for attempt in range(max_retries):
            try:
                # Use provided model or fall back to config model
                model_to_use = model or self.config.model
                # Map model name to actual OpenAI API model
                actual_model = self._map_model_name(model_to_use)
                return await self.client.chat.completions.create(
                    model=actual_model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    **kwargs
                )
            except (OpenAIError, asyncio.TimeoutError) as e:
                last_exception = e

                if attempt < max_retries - 1:
                    # Exponential backoff: 1s, 2s, 4s
                    delay = 2 ** attempt
                    self.logger.warning(f"OpenAI API call failed (attempt {attempt + 1}), retrying in {delay}s: {str(e)}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"OpenAI API call failed after {max_retries} attempts: {str(e)}")

        raise last_exception

    def _calculate_token_usage(self, usage) -> TokenUsage:
        """Calculate token usage and estimated cost."""
        if not usage:
            return TokenUsage()

        prompt_tokens = usage.prompt_tokens
        completion_tokens = usage.completion_tokens
        total_tokens = usage.total_tokens

        # Estimate cost
        model_costs = self.COST_PER_1K_TOKENS.get(self.config.model, {"input": 0.01, "output": 0.03})
        estimated_cost = (
            (prompt_tokens / 1000) * model_costs["input"] +
            (completion_tokens / 1000) * model_costs["output"]
        )

        return TokenUsage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            estimated_cost=estimated_cost,
            model=self.config.model
        )

    def _calculate_confidence(self, response) -> float:
        """Calculate confidence score based on response characteristics."""
        # Simple confidence calculation based on finish reason and content
        finish_reason = response.choices[0].finish_reason

        if finish_reason == "stop":
            # Natural completion - high confidence
            return 0.9
        elif finish_reason == "length":
            # Hit token limit - medium confidence
            return 0.7
        else:
            # Other reasons - lower confidence
            return 0.6

    def is_available(self) -> bool:
        """Check if OpenAI provider is available."""
        # Available in mock mode or with real API key
        return (
            self.client is not None or  # Real client available
            (self.config.api_key in ["dummy-key-for-development", "", None])  # Mock mode
        )

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the configured OpenAI model."""
        model_costs = self.COST_PER_1K_TOKENS.get(self.config.model, {"input": 0.01, "output": 0.03})

        return {
            "name": self.config.model,
            "provider": "openai",
            "context_window": self._get_context_window(self.config.model),
            "cost_per_1k_input": model_costs["input"],
            "cost_per_1k_output": model_costs["output"],
            "supports_streaming": True,
            "supports_function_calling": True
        }

    def _get_context_window(self, model: str) -> int:
        """Get context window size for the model."""
        context_windows = {
            "gpt-5-nano": 128000,  # Efficient context window
            "gpt-5-mini": 256000,  # Next-gen larger context window
            "gpt-5": 512000,  # Flagship model with massive context
            "gpt-4o": 128000,
            "gpt-4o-mini": 128000,
            "gpt-4-turbo": 128000,
            "gpt-4": 8192,
            "gpt-3.5-turbo": 16385,
        }
        return context_windows.get(model, 4096)

    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate the cost of a request."""
        model_costs = self.COST_PER_1K_TOKENS.get(self.config.model, {"input": 0.01, "output": 0.03})

        return (
            (prompt_tokens / 1000) * model_costs["input"] +
            (completion_tokens / 1000) * model_costs["output"]
        )

    async def generate_streaming_response(
        self,
        prompt: str,
        context: Optional[List[str]] = None,
        temperature: float = 0.1,
        max_tokens: int = 1000,
        **kwargs
    ):
        """Generate a streaming response."""
        # Check if using mock mode
        if self.client is None:
            # Simulate streaming by yielding chunks of the mock response
            mock_response = self._get_mock_response(prompt, max_tokens)
            content = mock_response.content
            chunk_size = 50

            for i in range(0, len(content), chunk_size):
                yield content[i:i + chunk_size]
                await asyncio.sleep(0.1)  # Simulate streaming delay
            return
        messages = self._prepare_messages(prompt, context)

        try:
            # Map model name to actual OpenAI API model
            actual_model = self._map_model_name(self.config.model)
            stream = await self.client.chat.completions.create(
                model=actual_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True,
                **kwargs
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            self._handle_error(e, "generate_streaming_response")
            raise AIProviderError(f"OpenAI streaming error: {str(e)}")

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check."""
        try:
            # Quick test with minimal tokens
            response = await self.generate_response(
                "Hello",
                max_tokens=5,
                temperature=0.0
            )

            mode = "mock" if self.client is None else "live"
            return {
                "healthy": True,
                "response_time": response.processing_time,
                "model": self.config.model,
                "provider": self.name,
                "mode": mode,
                "cost_estimate": response.token_usage.estimated_cost if response.token_usage else 0
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "provider": self.name,
                "mode": "mock" if self.client is None else "live"
            }


# Register the provider
from .base import AIProviderFactory
AIProviderFactory.register_provider("openai", OpenAIProvider)