"""
Base classes and interfaces for AI provider implementations.

This module provides:
- Abstract base classes for AI providers
- Standardized interfaces for different AI services
- Cost tracking and token usage monitoring
- Error handling and retry logic
- Performance monitoring
"""
import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

from core.config.settings import (
    OpenAIConfig, OllamaConfig, AnthropicConfig, HuggingFaceConfig,
    OpenRouterConfig, DeepSeekConfig, GeminiConfig
)
from utils.exceptions import AIProviderError, RateLimitError, TokenLimitError
from utils.logging import get_logger


@dataclass
class TokenUsage:
    """Token usage tracking for AI providers."""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    estimated_cost: float = 0.0
    model: str = ""
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ProviderMetrics:
    """Performance metrics for AI providers."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    average_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    error_counts: Dict[str, int] = field(default_factory=dict)


class AgentResponse:
    """Standardized response from AI providers."""

    def __init__(
        self,
        content: str,
        confidence_score: float = 0.0,
        metadata: Optional[Dict[str, Any]] = None,
        sources: Optional[List[Dict[str, Any]]] = None,
        processing_time: float = 0.0,
        model_used: str = "",
        token_usage: Optional[TokenUsage] = None,
        provider_name: str = ""
    ):
        self.content = content
        self.confidence_score = confidence_score
        self.metadata = metadata or {}
        self.sources = sources or []
        self.processing_time = processing_time
        self.model_used = model_used
        self.token_usage = token_usage
        self.provider_name = provider_name
        self.timestamp = datetime.utcnow()


class AIProvider(ABC):
    """
    Abstract base class for AI providers.

    This class defines the standard interface that all AI providers must implement,
    ensuring consistent behavior across different AI services.
    """

    def __init__(self, name: str, config: Any):
        self.name = name
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.metrics = ProviderMetrics()
        self._last_request_time = None

    @abstractmethod
    async def generate_response(
        self,
        prompt: str,
        context: Optional[List[str]] = None,
        temperature: float = 0.1,
        max_tokens: int = 1000,
        **kwargs
    ) -> AgentResponse:
        """
        Generate a response from the AI provider.

        Args:
            prompt: The main prompt to send to the AI
            context: Optional context documents or information
            temperature: Controls randomness (0.0 to 2.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Provider-specific parameters

        Returns:
            AgentResponse: Standardized response object

        Raises:
            AIProviderError: If the provider fails to generate a response
            RateLimitError: If rate limits are exceeded
            TokenLimitError: If token limits are exceeded
        """
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """Check if the AI provider is available and properly configured."""
        pass

    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the configured model."""
        pass

    @abstractmethod
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate the cost of a request based on token usage."""
        pass

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the AI provider."""
        try:
            start_time = time.time()
            # Simple health check - try to generate a minimal response
            response = await self.generate_response(
                "Hello",
                max_tokens=10,
                temperature=0.0
            )
            response_time = time.time() - start_time

            return {
                "healthy": True,
                "response_time": response_time,
                "model": self.get_model_info().get("name", "unknown"),
                "provider": self.name
            }
        except Exception as e:
            self.logger.error(f"Health check failed for {self.name}: {str(e)}")
            return {
                "healthy": False,
                "error": str(e),
                "provider": self.name
            }

    def _update_metrics(self, success: bool, response_time: float, token_usage: Optional[TokenUsage] = None):
        """Update provider metrics."""
        self.metrics.total_requests += 1
        self.metrics.last_request_time = datetime.utcnow()

        if success:
            self.metrics.successful_requests += 1
        else:
            self.metrics.failed_requests += 1

        if token_usage:
            self.metrics.total_tokens += token_usage.total_tokens
            self.metrics.total_cost += token_usage.estimated_cost

        # Update average response time
        if self.metrics.total_requests == 1:
            self.metrics.average_response_time = response_time
        else:
            self.metrics.average_response_time = (
                (self.metrics.average_response_time * (self.metrics.total_requests - 1)) +
                response_time
            ) / self.metrics.total_requests

    def _handle_error(self, error: Exception, operation: str) -> None:
        """Handle and log provider errors."""
        error_type = type(error).__name__
        if error_type not in self.metrics.error_counts:
            self.metrics.error_counts[error_type] = 0
        self.metrics.error_counts[error_type] += 1

        self.logger.error(
            f"AI Provider error in {operation}",
            extra={
                "provider": self.name,
                "error_type": error_type,
                "error_message": str(error),
                "operation": operation
            }
        )

    def get_metrics(self) -> Dict[str, Any]:
        """Get current provider metrics."""
        return {
            "provider_name": self.name,
            "total_requests": self.metrics.total_requests,
            "success_rate": (
                self.metrics.successful_requests / self.metrics.total_requests
                if self.metrics.total_requests > 0 else 0
            ),
            "total_tokens": self.metrics.total_tokens,
            "total_cost": self.metrics.total_cost,
            "average_response_time": self.metrics.average_response_time,
            "error_counts": self.metrics.error_counts.copy(),
            "last_request_time": self.metrics.last_request_time.isoformat() if self.metrics.last_request_time else None
        }


class LocalProviderMixin:
    """Mixin for local AI providers that prefer local inference."""

    def should_use_local(self) -> bool:
        """Determine if local inference should be used."""
        # Override in subclasses based on configuration
        return getattr(self.config, 'use_local', True)

    def get_local_model_path(self) -> Optional[str]:
        """Get the local model path if available."""
        # Override in subclasses
        return None


class StreamingProviderMixin:
    """Mixin for providers that support streaming responses."""

    async def generate_streaming_response(
        self,
        prompt: str,
        context: Optional[List[str]] = None,
        temperature: float = 0.1,
        max_tokens: int = 1000,
        **kwargs
    ):
        """Generate a streaming response (if supported by the provider)."""
        # Default implementation - override in subclasses that support streaming
        response = await self.generate_response(prompt, context, temperature, max_tokens, **kwargs)
        yield response.content


# Provider Factory
class AIProviderFactory:
    """Factory for creating AI provider instances."""

    _providers = {}

    @classmethod
    def register_provider(cls, provider_type: str, provider_class: type):
        """Register a provider class."""
        cls._providers[provider_type] = provider_class

    @classmethod
    def create_provider(cls, provider_type: str, config: Any) -> AIProvider:
        """Create a provider instance."""
        if provider_type not in cls._providers:
            raise ValueError(f"Unknown provider type: {provider_type}")

        provider_class = cls._providers[provider_type]
        return provider_class(config)

    @classmethod
    def get_available_providers(cls) -> List[str]:
        """Get list of available provider types."""
        return list(cls._providers.keys())


# Cost Optimization Strategies
class CostOptimizationStrategy:
    """Base class for cost optimization strategies."""

    def __init__(self, provider: AIProvider):
        self.provider = provider

    def should_use_provider(self, prompt: str, estimated_tokens: int) -> bool:
        """Determine if this provider should be used for the given request."""
        return True

    def optimize_prompt(self, prompt: str) -> str:
        """Optimize the prompt to reduce token usage."""
        return prompt

    def get_cost_estimate(self, prompt_tokens: int, estimated_completion_tokens: int) -> float:
        """Get cost estimate for the request."""
        return self.provider.estimate_cost(prompt_tokens, estimated_completion_tokens)


class BudgetAwareStrategy(CostOptimizationStrategy):
    """Cost optimization strategy that respects budget limits."""

    def __init__(self, provider: AIProvider, daily_budget: float = 10.0):
        super().__init__(provider)
        self.daily_budget = daily_budget
        self.daily_cost = 0.0
        self.reset_date = datetime.utcnow().date()

    def should_use_provider(self, prompt: str, estimated_tokens: int) -> bool:
        """Check if request fits within budget."""
        estimated_cost = self.get_cost_estimate(estimated_tokens, estimated_tokens // 2)

        # Reset daily cost if it's a new day
        if datetime.utcnow().date() != self.reset_date:
            self.daily_cost = 0.0
            self.reset_date = datetime.utcnow().date()

        return (self.daily_cost + estimated_cost) <= self.daily_budget


class TokenEfficientStrategy(CostOptimizationStrategy):
    """Strategy that optimizes for token efficiency."""

    def optimize_prompt(self, prompt: str) -> str:
        """Remove unnecessary words and optimize prompt length."""
        # Simple optimization - remove extra whitespace and redundant words
        import re

        # Remove multiple spaces
        optimized = re.sub(r'\s+', ' ', prompt.strip())

        # Remove common filler phrases
        fillers = [
            "please", "kindly", "could you", "would you mind",
            "i would like", "i want", "can you"
        ]

        for filler in fillers:
            optimized = re.sub(rf'\b{filler}\b', '', optimized, flags=re.IGNORECASE)

        return optimized.strip()


# Alias for backward compatibility
BaseAIProvider = AIProvider

# Export key classes
__all__ = [
    "AIProvider",
    "BaseAIProvider",  # Alias for backward compatibility
    "AgentResponse",
    "TokenUsage",
    "ProviderMetrics",
    "LocalProviderMixin",
    "StreamingProviderMixin",
    "AIProviderFactory",
    "CostOptimizationStrategy",
    "BudgetAwareStrategy",
    "TokenEfficientStrategy",
]