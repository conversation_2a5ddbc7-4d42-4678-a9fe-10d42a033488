"""
Ollama provider implementation for local AI model inference.

This module provides:
- Local Ollama model integration
- Cost-free local inference
- Privacy-focused processing
- Support for various open-source models
"""
import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

try:
    import ollama
    from ollama import Client
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    Client = None

from .base import AIProvider, AgentResponse, TokenUsage, LocalProviderMixin
from core.config.settings import OllamaConfig
from utils.exceptions import AIProviderError, TokenLimitError
from utils.logging import get_logger


class OllamaProvider(AIProvider, LocalProviderMixin):
    """
    Ollama provider implementation for local AI models.

    Features:
    - Local model inference (no API costs)
    - Privacy-focused (data stays local)
    - Support for various open-source models
    - Cost-free operation
    """

    def __init__(self, config: OllamaConfig):
        if not OLLAMA_AVAILABLE:
            raise AIProviderError("Ollama package not installed. Install with: pip install ollama")

        super().__init__("ollama", config)
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize the Ollama client."""
        try:
            self.client = Client(host=self.config.base_url)
        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama client: {str(e)}")
            raise AIProviderError(f"Ollama client initialization failed: {str(e)}")

    async def generate_response(
        self,
        prompt: str,
        context: Optional[List[str]] = None,
        temperature: float = 0.1,
        max_tokens: int = 1000,
        **kwargs
    ) -> AgentResponse:
        """
        Generate a response using local Ollama models.

        Args:
            prompt: The main prompt to send
            context: Optional context documents
            temperature: Controls randomness (0.0 to 2.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Additional Ollama-specific parameters

        Returns:
            AgentResponse: Standardized response object
        """
        start_time = time.time()

        try:
            # Prepare the full prompt
            full_prompt = self._prepare_prompt(prompt, context)

            # Make API call
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.generate(
                    model=self.config.model,
                    prompt=full_prompt,
                    options={
                        "temperature": temperature,
                        "num_predict": max_tokens,
                        **kwargs
                    }
                )
            )

            processing_time = time.time() - start_time

            # Extract response content
            content = response.get("response", "")

            # Estimate token usage (Ollama doesn't provide exact counts)
            token_usage = self._estimate_token_usage(full_prompt, content)

            # Create standardized response
            agent_response = AgentResponse(
                content=content,
                confidence_score=self._calculate_confidence(response),
                metadata={
                    "model": self.config.model,
                    "done": response.get("done", False),
                    "total_duration": response.get("total_duration", 0),
                    "load_duration": response.get("load_duration", 0),
                    "prompt_eval_count": response.get("prompt_eval_count", 0),
                    "eval_count": response.get("eval_count", 0),
                    "temperature": temperature,
                    "max_tokens": max_tokens
                },
                processing_time=processing_time,
                model_used=self.config.model,
                token_usage=token_usage,
                provider_name=self.name
            )

            # Update metrics
            self._update_metrics(True, processing_time, token_usage)

            return agent_response

        except Exception as e:
            processing_time = time.time() - start_time
            self._handle_error(e, "generate_response")
            raise AIProviderError(f"Ollama API error: {str(e)}")

    def _prepare_prompt(self, prompt: str, context: Optional[List[str]] = None) -> str:
        """Prepare the full prompt for Ollama."""
        system_prompt = """You are a legal assistant specializing in contract analysis, legal research, and strategic advice.
Provide accurate, well-reasoned responses based on legal principles and best practices.
Always be thorough, professional, and cite relevant legal concepts when applicable."""

        full_prompt = f"System: {system_prompt}\n\n"

        # Add context if provided
        if context:
            context_text = "\n\n".join(context)
            full_prompt += f"Context:\n{context_text}\n\n"

        # Add user prompt
        full_prompt += f"User: {prompt}\n\nAssistant:"

        return full_prompt

    def _estimate_token_usage(self, prompt: str, response: str) -> TokenUsage:
        """Estimate token usage for Ollama (approximate)."""
        # Rough estimation: 1 token ≈ 4 characters for English text
        prompt_tokens = len(prompt) // 4
        completion_tokens = len(response) // 4
        total_tokens = prompt_tokens + completion_tokens

        # Ollama is free (local inference)
        estimated_cost = 0.0

        return TokenUsage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            estimated_cost=estimated_cost,
            model=self.config.model
        )

    def _calculate_confidence(self, response: Dict[str, Any]) -> float:
        """Calculate confidence score based on response characteristics."""
        # Ollama doesn't provide confidence scores, so we use heuristics
        eval_count = response.get("eval_count", 0)
        total_duration = response.get("total_duration", 0)

        if eval_count == 0:
            return 0.5  # Default confidence

        # Higher confidence for longer, more thorough responses
        tokens_per_second = eval_count / (total_duration / 1e9) if total_duration > 0 else 0

        # Normalize to 0.5-0.9 range based on processing speed
        confidence = min(0.9, max(0.5, tokens_per_second / 50))
        return confidence

    def is_available(self) -> bool:
        """Check if Ollama provider is available."""
        if not OLLAMA_AVAILABLE:
            return False

        try:
            # Try to list models to check if Ollama is running
            models = self.client.list()
            available_models = [model['name'] for model in models.get('models', [])]
            return self.config.model in available_models
        except Exception:
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the configured Ollama model."""
        try:
            # Get model details
            model_info = self.client.show(self.config.model)

            return {
                "name": self.config.model,
                "provider": "ollama",
                "context_window": model_info.get("context_length", 4096),
                "cost_per_1k_input": 0.0,  # Free
                "cost_per_1k_output": 0.0,  # Free
                "supports_streaming": True,
                "supports_function_calling": False,
                "local_inference": True,
                "privacy_focused": True
            }
        except Exception:
            return {
                "name": self.config.model,
                "provider": "ollama",
                "context_window": 4096,
                "cost_per_1k_input": 0.0,
                "cost_per_1k_output": 0.0,
                "supports_streaming": True,
                "supports_function_calling": False,
                "local_inference": True,
                "privacy_focused": True
            }

    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate the cost of a request (always 0 for local models)."""
        return 0.0  # Ollama is free

    def should_use_local(self) -> bool:
        """Ollama is always local."""
        return True

    def get_local_model_path(self) -> Optional[str]:
        """Get the local model path."""
        # Ollama manages models internally
        return None

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check."""
        try:
            # Quick test with minimal tokens
            response = await self.generate_response(
                "Hello",
                max_tokens=5,
                temperature=0.0
            )

            return {
                "healthy": True,
                "response_time": response.processing_time,
                "model": self.config.model,
                "provider": self.name,
                "local_inference": True,
                "cost_estimate": 0.0  # Always free
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "provider": self.name,
                "local_inference": True
            }

    def list_available_models(self) -> List[str]:
        """List all available models in Ollama."""
        try:
            models = self.client.list()
            return [model['name'] for model in models.get('models', [])]
        except Exception:
            return []

    def pull_model(self, model_name: str) -> bool:
        """Pull a model from the Ollama library."""
        try:
            self.client.pull(model_name)
            return True
        except Exception as e:
            self.logger.error(f"Failed to pull model {model_name}: {str(e)}")
            return False


# Register the provider
from .base import AIProviderFactory
AIProviderFactory.register_provider("ollama", OllamaProvider)