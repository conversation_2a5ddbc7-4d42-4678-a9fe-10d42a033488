"""
AI Law Firm - Multi-Agent Legal Document Analysis System

This package provides a comprehensive AI-powered legal document analysis system
with multiple specialized agents working together to provide thorough legal insights.
"""

__version__ = "1.0.0"
__author__ = "AI Law Firm Team"
__description__ = "Multi-Agent Legal Document Analysis System"

# Import key components for easy access
from .core.config.settings import get_config, load_config
from .utils.logging import get_logger, configure_logging
from .utils.exceptions import AILawFirmError

__all__ = [
    "get_config",
    "load_config",
    "get_logger",
    "configure_logging",
    "AILawFirmError",
]