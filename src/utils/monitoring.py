"""
Monitoring and metrics collection for AI Law Firm.

This module provides comprehensive monitoring capabilities including:
- Performance metrics collection
- Health checks for all services
- Request/response monitoring
- Error tracking and alerting
- System resource monitoring
"""

import time
import logging
import psutil
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
from dataclasses import dataclass, field

from .logging import get_logger

logger = get_logger(__name__)


@dataclass
class Metric:
    """Represents a single metric measurement."""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HealthStatus:
    """Represents the health status of a service."""
    service_name: str
    status: str  # "healthy", "degraded", "unhealthy"
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    last_check: datetime = field(default_factory=datetime.utcnow)
    details: Dict[str, Any] = field(default_factory=dict)


class MetricsCollector:
    """Collects and manages application metrics."""

    def __init__(self):
        self.metrics: List[Metric] = []
        self.max_metrics = 10000  # Keep last 10k metrics
        self.retention_hours = 24

    def record_metric(
        self,
        name: str,
        value: float,
        tags: Optional[Dict[str, str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record a metric measurement."""
        metric = Metric(
            name=name,
            value=value,
            timestamp=datetime.utcnow(),
            tags=tags or {},
            metadata=metadata or {}
        )

        self.metrics.append(metric)

        # Maintain size limit
        if len(self.metrics) > self.max_metrics:
            self.metrics = self.metrics[-self.max_metrics:]

        logger.debug(f"Recorded metric: {name}={value}")

    def get_metrics(
        self,
        name: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        since: Optional[datetime] = None
    ) -> List[Metric]:
        """Retrieve metrics with optional filtering."""
        filtered_metrics = self.metrics

        if name:
            filtered_metrics = [m for m in filtered_metrics if m.name == name]

        if tags:
            filtered_metrics = [
                m for m in filtered_metrics
                if all(m.tags.get(k) == v for k, v in tags.items())
            ]

        if since:
            filtered_metrics = [m for m in filtered_metrics if m.timestamp >= since]

        return filtered_metrics

    def get_metric_summary(
        self,
        name: str,
        tags: Optional[Dict[str, str]] = None,
        hours: int = 1
    ) -> Dict[str, Any]:
        """Get summary statistics for a metric."""
        since = datetime.utcnow() - timedelta(hours=hours)
        metrics = self.get_metrics(name=name, tags=tags, since=since)

        if not metrics:
            return {"count": 0, "average": 0, "min": 0, "max": 0}

        values = [m.value for m in metrics]

        return {
            "count": len(values),
            "average": sum(values) / len(values),
            "min": min(values),
            "max": max(values),
            "latest": values[-1] if values else 0
        }

    def cleanup_old_metrics(self):
        """Remove metrics older than retention period."""
        cutoff = datetime.utcnow() - timedelta(hours=self.retention_hours)
        self.metrics = [m for m in self.metrics if m.timestamp >= cutoff]


class HealthChecker:
    """Performs health checks on all services."""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.services: Dict[str, HealthStatus] = {}
        self.check_interval = 60  # seconds

    async def check_service_health(
        self,
        service_name: str,
        check_function: callable,
        timeout: float = 10.0
    ) -> HealthStatus:
        """Check health of a specific service."""
        start_time = time.time()

        try:
            # Perform the health check with timeout
            import asyncio
            result = await asyncio.wait_for(check_function(), timeout=timeout)
            response_time = time.time() - start_time

            status = HealthStatus(
                service_name=service_name,
                status="healthy",
                response_time=response_time,
                details=result if isinstance(result, dict) else {}
            )

        except asyncio.TimeoutError:
            status = HealthStatus(
                service_name=service_name,
                status="unhealthy",
                error_message="Health check timed out",
                response_time=time.time() - start_time
            )

        except Exception as e:
            status = HealthStatus(
                service_name=service_name,
                status="unhealthy",
                error_message=str(e),
                response_time=time.time() - start_time
            )

        # Record metrics
        self.metrics.record_metric(
            "health_check_response_time",
            status.response_time or 0,
            tags={"service": service_name, "status": status.status}
        )

        self.services[service_name] = status
        return status

    def get_service_status(self, service_name: str) -> Optional[HealthStatus]:
        """Get the current health status of a service."""
        return self.services.get(service_name)

    def get_all_statuses(self) -> Dict[str, HealthStatus]:
        """Get health status of all services."""
        return self.services.copy()

    def get_overall_health(self) -> str:
        """Get overall system health status."""
        if not self.services:
            return "unknown"

        statuses = [status.status for status in self.services.values()]

        if "unhealthy" in statuses:
            return "unhealthy"
        elif "degraded" in statuses:
            return "degraded"
        else:
            return "healthy"


class PerformanceMonitor:
    """Monitors system performance metrics."""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector

    def collect_system_metrics(self):
        """Collect system-level performance metrics."""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.metrics.record_metric("system_cpu_percent", cpu_percent)

        # Memory usage
        memory = psutil.virtual_memory()
        self.metrics.record_metric("system_memory_percent", memory.percent)
        self.metrics.record_metric("system_memory_used_mb", memory.used / 1024 / 1024)

        # Disk usage
        disk = psutil.disk_usage('/')
        self.metrics.record_metric("system_disk_percent", disk.percent)

        # Network I/O (if available)
        try:
            net = psutil.net_io_counters()
            self.metrics.record_metric("system_net_bytes_sent", net.bytes_sent)
            self.metrics.record_metric("system_net_bytes_recv", net.bytes_recv)
        except Exception:
            pass  # Network metrics might not be available

    def record_request_metrics(
        self,
        endpoint: str,
        method: str,
        response_time: float,
        status_code: int,
        user_id: Optional[str] = None
    ):
        """Record API request metrics."""
        self.metrics.record_metric(
            "api_request_duration",
            response_time,
            tags={
                "endpoint": endpoint,
                "method": method,
                "status_code": str(status_code),
                "status_class": str(status_code // 100) + "xx"
            }
        )

        if user_id:
            self.metrics.record_metric(
                "api_requests_per_user",
                1,
                tags={"user_id": user_id}
            )

    def record_ai_metrics(
        self,
        operation: str,
        model: str,
        tokens_used: int,
        response_time: float,
        success: bool = True
    ):
        """Record AI operation metrics."""
        self.metrics.record_metric(
            "ai_operation_duration",
            response_time,
            tags={
                "operation": operation,
                "model": model,
                "success": str(success)
            }
        )

        if tokens_used > 0:
            self.metrics.record_metric(
                "ai_tokens_used",
                tokens_used,
                tags={"operation": operation, "model": model}
            )

    def record_database_metrics(
        self,
        operation: str,
        collection: str,
        response_time: float,
        success: bool = True
    ):
        """Record database operation metrics."""
        self.metrics.record_metric(
            "db_operation_duration",
            response_time,
            tags={
                "operation": operation,
                "collection": collection,
                "success": str(success)
            }
        )


class RequestTracker:
    """Tracks and monitors API requests."""

    def __init__(self, performance_monitor: PerformanceMonitor):
        self.monitor = performance_monitor
        self.active_requests: Dict[str, Dict[str, Any]] = {}

    @asynccontextmanager
    async def track_request(self, request_id: str, endpoint: str, method: str):
        """Context manager to track a request."""
        start_time = time.time()
        request_info = {
            "endpoint": endpoint,
            "method": method,
            "start_time": start_time
        }

        self.active_requests[request_id] = request_info

        try:
            yield
        finally:
            # Record request completion
            end_time = time.time()
            response_time = end_time - start_time

            # Remove from active requests
            request_info = self.active_requests.pop(request_id, {})

            # This would be called from the API endpoint with actual status code
            # For now, we'll assume success
            self.monitor.record_request_metrics(
                endpoint=endpoint,
                method=method,
                response_time=response_time,
                status_code=200  # This should be passed from the actual response
            )

    def get_active_requests_count(self) -> int:
        """Get count of currently active requests."""
        return len(self.active_requests)

    def get_active_requests(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active requests."""
        return self.active_requests.copy()


# Global instances
metrics_collector = MetricsCollector()
health_checker = HealthChecker(metrics_collector)
performance_monitor = PerformanceMonitor(metrics_collector)
request_tracker = RequestTracker(performance_monitor)


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    return metrics_collector


def get_health_checker() -> HealthChecker:
    """Get the global health checker instance."""
    return health_checker


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    return performance_monitor


def get_request_tracker() -> RequestTracker:
    """Get the global request tracker instance."""
    return request_tracker


# Utility functions for easy metric recording
def record_metric(name: str, value: float, tags: Optional[Dict[str, str]] = None):
    """Convenience function to record a metric."""
    metrics_collector.record_metric(name, value, tags)


def increment_counter(name: str, tags: Optional[Dict[str, str]] = None):
    """Convenience function to increment a counter metric."""
    current = metrics_collector.get_metric_summary(name, tags, hours=1)["latest"]
    metrics_collector.record_metric(name, current + 1, tags)


async def check_service_health(service_name: str, check_function: callable) -> HealthStatus:
    """Convenience function to check service health."""
    return await health_checker.check_service_health(service_name, check_function)


def get_system_health() -> Dict[str, Any]:
    """Get comprehensive system health information."""
    return {
        "overall_health": health_checker.get_overall_health(),
        "services": {
            name: {
                "status": status.status,
                "response_time": status.response_time,
                "last_check": status.last_check.isoformat(),
                "error_message": status.error_message
            }
            for name, status in health_checker.get_all_statuses().items()
        },
        "active_requests": request_tracker.get_active_requests_count(),
        "metrics_summary": {
            "api_requests": metrics_collector.get_metric_summary("api_request_duration"),
            "ai_operations": metrics_collector.get_metric_summary("ai_operation_duration"),
            "db_operations": metrics_collector.get_metric_summary("db_operation_duration")
        }
    }