"""
Custom exception classes and error handling utilities for the AI Law Firm system.

This module provides:
- Hierarchical exception classes for different error types
- Error handling decorators and utilities
- Standardized error responses
- Logging integration for errors
"""
import logging
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass
from datetime import datetime


@dataclass
class ErrorContext:
    """Context information for errors."""
    operation: str
    component: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    document_id: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None

    def __post_init__(self):
        """Set timestamp if not provided."""
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()


class AILawFirmError(Exception):
    """
    Base exception class for all AI Law Firm errors.

    This provides a standardized way to handle errors with context
    and logging integration.
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        context: Optional[ErrorContext] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize the exception.

        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            context: Additional context information
            cause: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self._get_default_error_code()
        self.context = context
        self.cause = cause

        # Auto-log the error
        self._log_error()

    def _get_default_error_code(self) -> str:
        """Get default error code based on class name."""
        return self.__class__.__name__.replace('Error', '').upper()

    def _log_error(self) -> None:
        """Log the error with context."""
        logger = logging.getLogger(self.__class__.__module__)

        log_data = {
            "error_code": self.error_code,
            "error_message": self.message,
            "error_type": self.__class__.__name__,
        }

        if self.context:
            log_data["error_context"] = {
                "operation": self.context.operation,
                "component": self.context.component,
                "user_id": self.context.user_id,
                "session_id": self.context.session_id,
                "document_id": self.context.document_id,
                "timestamp": self.context.timestamp,
            }
            if self.context.additional_data:
                log_data["error_context"]["additional_data"] = self.context.additional_data

        if self.cause:
            log_data["cause"] = str(self.cause)

        logger.error(
            f"Error occurred: {self.message}",
            extra=log_data,
            exc_info=self.cause
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for API responses."""
        error_dict = {
            "error": {
                "code": self.error_code,
                "message": self.message,
                "type": self.__class__.__name__,
            }
        }

        if self.context:
            error_dict["error"]["context"] = {
                "operation": self.context.operation,
                "component": self.context.component,
            }
            if self.context.user_id:
                error_dict["error"]["context"]["user_id"] = self.context.user_id
            if self.context.session_id:
                error_dict["error"]["context"]["session_id"] = self.context.session_id

        return error_dict


# Configuration Errors
class ConfigurationError(AILawFirmError):
    """Errors related to system configuration."""
    pass


class ValidationError(AILawFirmError):
    """Errors related to data validation."""
    pass


class EnvironmentError(AILawFirmError):
    """Errors related to environment detection or setup."""
    pass


# AI Provider Errors
class AIProviderError(AILawFirmError):
    """Base class for AI provider related errors."""
    pass


class OpenAIError(AIProviderError):
    """Errors specific to OpenAI provider."""
    pass


class OllamaError(AIProviderError):
    """Errors specific to Ollama provider."""
    pass


class ModelNotAvailableError(AIProviderError):
    """Error when requested AI model is not available."""
    pass


class RateLimitError(AIProviderError):
    """Error when API rate limits are exceeded."""
    pass


class TokenLimitError(AIProviderError):
    """Error when token limits are exceeded."""
    pass


# Document Processing Errors
class DocumentError(AILawFirmError):
    """Base class for document processing errors."""
    pass


class DocumentNotFoundError(DocumentError):
    """Error when requested document is not found."""
    pass


class DocumentFormatError(DocumentError):
    """Error when document format is not supported."""
    pass


class DocumentSizeError(DocumentError):
    """Error when document exceeds size limits."""
    pass


class DocumentProcessingError(DocumentError):
    """Error during document processing operations."""
    pass


class FileUploadError(DocumentError):
    """Error during file upload operations."""
    pass


class StorageError(DocumentError):
    """Error during file storage operations."""
    pass


# Knowledge Base Errors
class KnowledgeBaseError(AILawFirmError):
    """Base class for knowledge base errors."""
    pass


class VectorDatabaseError(KnowledgeBaseError):
    """Errors related to vector database operations."""
    pass


class SearchError(KnowledgeBaseError):
    """Errors during search operations."""
    pass


class IndexingError(KnowledgeBaseError):
    """Errors during document indexing."""
    pass


# Agent Errors
class AgentError(AILawFirmError):
    """Base class for agent-related errors."""
    pass


class AgentInitializationError(AgentError):
    """Error during agent initialization."""
    pass


class AgentProcessingError(AgentError):
    """Error during agent query processing."""
    pass


class AgentCapabilityError(AgentError):
    """Error when agent lacks required capabilities."""
    pass


class TeamCoordinationError(AgentError):
    """Error during team coordination operations."""
    pass


# Pipeline Errors
class PipelineError(AILawFirmError):
    """Error during pipeline execution."""
    pass


class AgentPoolError(AILawFirmError):
    """Error related to agent pool operations."""
    pass


class TaskExecutionError(AILawFirmError):
    """Error during task execution."""
    pass


class AgentNotFoundError(AILawFirmError):
    """Error when no suitable agent is found for a query."""
    pass


class RoutingError(AILawFirmError):
    """Error during agent routing operations."""
    pass


# User Interface Errors
class UIError(AILawFirmError):
    """Base class for user interface errors."""
    pass


class SessionError(UIError):
    """Errors related to user sessions."""
    pass


class AuthenticationError(UIError):
    """Errors related to user authentication."""
    pass


class AuthorizationError(UIError):
    """Errors related to user authorization."""
    pass


# External Service Errors
class ExternalServiceError(AILawFirmError):
    """Base class for external service errors."""
    pass


class NetworkError(ExternalServiceError):
    """Network-related errors."""
    pass


class TimeoutError(ExternalServiceError):
    """Timeout errors."""
    pass


class ServiceUnavailableError(ExternalServiceError):
    """Service unavailable errors."""
    pass


# AI-specific errors
class AIError(AILawFirmError):
    """Base class for AI-related errors."""
    pass


# Database Errors
class DatabaseError(AILawFirmError):
    """Base class for database-related errors."""
    pass


class ConnectionError(DatabaseError):
    """Database connection errors."""
    pass


class QueryError(DatabaseError):
    """Database query execution errors."""
    pass


class TransactionError(DatabaseError):
    """Database transaction errors."""
    pass


# Error Handling Utilities
def handle_errors(error_map: Optional[Dict[type, callable]] = None):
    """
    Decorator for standardized error handling.

    Args:
        error_map: Optional mapping of exception types to handler functions
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Use custom error handler if provided
                if error_map and type(e) in error_map:
                    return error_map[type(e)](e)

                # Re-raise AILawFirmError instances as-is
                if isinstance(e, AILawFirmError):
                    raise

                # Wrap other exceptions
                context = ErrorContext(
                    operation=func.__name__,
                    component=func.__module__,
                    additional_data={"args": str(args), "kwargs": str(kwargs)}
                )

                raise AILawFirmError(
                    message=f"Unexpected error in {func.__name__}: {str(e)}",
                    context=context,
                    cause=e
                )
        return wrapper
    return decorator


def create_error_response(error: Exception) -> Dict[str, Any]:
    """
    Create standardized error response from exception.

    Args:
        error: The exception to convert

    Returns:
        Dict containing standardized error response
    """
    if isinstance(error, AILawFirmError):
        return error.to_dict()

    # Handle standard exceptions
    error_code = "INTERNAL_ERROR"
    if isinstance(error, ValueError):
        error_code = "VALIDATION_ERROR"
    elif isinstance(error, PermissionError):
        error_code = "PERMISSION_DENIED"
    elif isinstance(error, FileNotFoundError):
        error_code = "FILE_NOT_FOUND"
    elif isinstance(error, ConnectionError):
        error_code = "CONNECTION_ERROR"
    elif isinstance(error, TimeoutError):
        error_code = "TIMEOUT_ERROR"

    return {
        "error": {
            "code": error_code,
            "message": str(error),
            "type": error.__class__.__name__,
        }
    }


def log_and_raise(
    error_class: type,
    message: str,
    error_code: Optional[str] = None,
    context: Optional[ErrorContext] = None,
    cause: Optional[Exception] = None
) -> None:
    """
    Log and raise an error with context.

    Args:
        error_class: The exception class to raise
        message: Error message
        error_code: Optional error code
        context: Optional error context
        cause: Optional original exception
    """
    error = error_class(message, error_code, context, cause)
    raise error


def validate_condition(
    condition: bool,
    message: str,
    error_class: type = ValidationError,
    context: Optional[ErrorContext] = None
) -> None:
    """
    Validate a condition and raise error if false.

    Args:
        condition: Condition to validate
        message: Error message if condition is false
        error_class: Exception class to raise
        context: Optional error context
    """
    if not condition:
        raise error_class(message, context=context)


def safe_execute(
    func: callable,
    *args,
    error_message: str = "Operation failed",
    error_class: type = AILawFirmError,
    **kwargs
) -> Any:
    """
    Execute a function safely with error handling.

    Args:
        func: Function to execute
        *args: Positional arguments for the function
        error_message: Error message if function fails
        error_class: Exception class to raise on failure
        **kwargs: Keyword arguments for the function

    Returns:
        Result of the function call

    Raises:
        error_class: If the function call fails
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        context = ErrorContext(
            operation=func.__name__ if hasattr(func, '__name__') else str(func),
            component="safe_execute",
            additional_data={"args": str(args), "kwargs": str(kwargs)}
        )

        raise error_class(
            message=f"{error_message}: {str(e)}",
            context=context,
            cause=e
        )


# Error Recovery Strategies
class ErrorRecoveryStrategy:
    """Base class for error recovery strategies."""

    def can_recover(self, error: Exception) -> bool:
        """Check if this strategy can recover from the error."""
        raise NotImplementedError

    def recover(self, error: Exception) -> Any:
        """Attempt to recover from the error."""
        raise NotImplementedError


class RetryStrategy(ErrorRecoveryStrategy):
    """Retry strategy for transient errors."""

    def __init__(self, max_attempts: int = 3, delay: float = 1.0):
        self.max_attempts = max_attempts
        self.delay = delay

    def can_recover(self, error: Exception) -> bool:
        """Check if error is retryable."""
        retryable_errors = (
            NetworkError,
            TimeoutError,
            ServiceUnavailableError,
            RateLimitError
        )
        return isinstance(error, retryable_errors)

    def recover(self, error: Exception) -> Any:
        """Retry the operation with exponential backoff."""
        import time

        for attempt in range(self.max_attempts):
            try:
                # This would need to be implemented with the actual operation
                # For now, just log the retry attempt
                logger = logging.getLogger(__name__)
                logger.info(f"Retry attempt {attempt + 1} for error: {str(error)}")
                time.sleep(self.delay * (2 ** attempt))  # Exponential backoff
                return None  # Would return actual result
            except Exception:
                if attempt == self.max_attempts - 1:
                    raise
                continue


class FallbackStrategy(ErrorRecoveryStrategy):
    """Fallback strategy for critical operations."""

    def __init__(self, fallback_function: callable):
        self.fallback_function = fallback_function

    def can_recover(self, error: Exception) -> bool:
        """Check if fallback is available."""
        return self.fallback_function is not None

    def recover(self, error: Exception) -> Any:
        """Execute fallback function."""
        logger = logging.getLogger(__name__)
        logger.warning(f"Using fallback strategy for error: {str(error)}")
        return self.fallback_function()


def with_error_recovery(
    recovery_strategies: List[ErrorRecoveryStrategy],
    default_error_class: type = AILawFirmError
):
    """
    Decorator to add error recovery to functions.

    Args:
        recovery_strategies: List of recovery strategies to try
        default_error_class: Default exception class for unrecoverable errors
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Try recovery strategies
                for strategy in recovery_strategies:
                    if strategy.can_recover(e):
                        try:
                            return strategy.recover(e)
                        except Exception as recovery_error:
                            # Recovery failed, continue to next strategy
                            continue

                # No recovery strategy worked, raise original error
                if isinstance(e, AILawFirmError):
                    raise
                else:
                    raise default_error_class(str(e), cause=e)
        return wrapper
    return decorator


# Global error recovery strategies
DEFAULT_RECOVERY_STRATEGIES = [
    RetryStrategy(max_attempts=3, delay=1.0),
]

# Export commonly used classes and functions
__all__ = [
    # Base classes
    "AILawFirmError",
    "ErrorContext",

    # Configuration errors
    "ConfigurationError",
    "ValidationError",
    "EnvironmentError",

    # AI provider errors
    "AIProviderError",
    "OpenAIError",
    "OllamaError",
    "ModelNotAvailableError",
    "RateLimitError",
    "TokenLimitError",

    # Document errors
    "DocumentError",
    "DocumentNotFoundError",
    "DocumentFormatError",
    "DocumentSizeError",
    "DocumentProcessingError",
    "FileUploadError",
    "StorageError",

    # Knowledge base errors
    "KnowledgeBaseError",
    "VectorDatabaseError",
    "SearchError",
    "IndexingError",

    # Agent errors
    "AgentError",
    "AgentInitializationError",
    "AgentProcessingError",
    "AgentCapabilityError",
    "TeamCoordinationError",

    # Pipeline errors
    "PipelineError",
    "AgentPoolError",
    "TaskExecutionError",
    "AgentNotFoundError",
    "RoutingError",

    # UI errors
    "UIError",
    "SessionError",
    "AuthenticationError",
    "AuthorizationError",

    # External service errors
    "ExternalServiceError",
    "NetworkError",
    "TimeoutError",
    "ServiceUnavailableError",

    # AI errors
    "AIError",

    # Database errors
    "DatabaseError",
    "ConnectionError",
    "QueryError",
    "TransactionError",

    # Utilities
    "handle_errors",
    "create_error_response",
    "log_and_raise",
    "validate_condition",
    "safe_execute",

    # Recovery
    "ErrorRecoveryStrategy",
    "RetryStrategy",
    "FallbackStrategy",
    "with_error_recovery",
    "DEFAULT_RECOVERY_STRATEGIES",
]