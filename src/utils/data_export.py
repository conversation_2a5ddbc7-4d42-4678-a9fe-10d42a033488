"""
Data Export/Import Utilities for AI Law Firm.

This module provides comprehensive data export and import capabilities
for backup, migration, and data portability across the multi-database system.
"""

import json
import csv
import zipfile
from io import BytesIO, String<PERSON>
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
import pandas as pd

from core.services.data_persistence_service import DataPersistenceService
from utils.logging import get_logger


class DataExportService:
    """
    Service for exporting data from the AI Law Firm system.

    Supports multiple formats:
    - JSON (structured data)
    - CSV (tabular data)
    - ZIP (compressed archives)
    - PDF reports (summary reports)
    """

    def __init__(self, persistence_service: DataPersistenceService):
        self.persistence = persistence_service
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    async def export_user_data(
        self,
        user_id: str,
        format: str = "json",
        include_content: bool = True
    ) -> Union[str, bytes, Dict[str, Any]]:
        """
        Export all user data in the specified format.

        Args:
            user_id: User ID to export data for
            format: Export format ('json', 'csv', 'zip')
            include_content: Whether to include full document content

        Returns:
            Exported data in the requested format
        """
        try:
            self.logger.info(f"Starting data export for user {user_id} in {format} format")

            # Get user data from persistence service
            user_data = await self.persistence.export_user_data(user_id)

            if format == "json":
                return self._export_as_json(user_data, include_content)
            elif format == "csv":
                return self._export_as_csv(user_data)
            elif format == "zip":
                return await self._export_as_zip(user_data, include_content)
            else:
                raise ValueError(f"Unsupported export format: {format}")

        except Exception as e:
            self.logger.error(f"Error exporting user data: {str(e)}")
            raise

    def _export_as_json(self, user_data: Dict[str, Any], include_content: bool) -> str:
        """Export data as JSON string."""
        # Create a clean copy for export
        export_data = {
            "export_info": {
                "user_id": user_data.get("user_id"),
                "export_timestamp": datetime.utcnow().isoformat(),
                "format": "json",
                "version": "1.0"
            },
            "data": user_data.get("data", {})
        }

        return json.dumps(export_data, indent=2, default=str)

    def _export_as_csv(self, user_data: Dict[str, Any]) -> str:
        """Export data as CSV string."""
        output = StringIO()

        # Documents CSV
        if "documents" in user_data.get("data", {}):
            output.write("=== DOCUMENTS ===\n")
            documents = user_data["data"]["documents"]

            if documents:
                # Create DataFrame from documents
                docs_data = []
                for doc in documents:
                    metadata = doc.get("metadata", {})
                    docs_data.append({
                        "document_id": metadata.get("document_id"),
                        "filename": metadata.get("filename"),
                        "document_type": metadata.get("document_type", {}).get("value") if isinstance(metadata.get("document_type"), dict) else metadata.get("document_type"),
                        "file_size_bytes": metadata.get("file_size_bytes"),
                        "processing_status": metadata.get("processing_status", {}).get("value") if isinstance(metadata.get("processing_status"), dict) else metadata.get("processing_status"),
                        "created_at": metadata.get("created_at"),
                        "processed_at": metadata.get("processed_at")
                    })

                df = pd.DataFrame(docs_data)
                df.to_csv(output, index=False)
                output.write("\n\n")

        # Usage stats CSV
        if "usage_stats" in user_data.get("data", {}):
            output.write("=== USAGE STATISTICS ===\n")
            usage_stats = user_data["data"]["usage_stats"]

            if isinstance(usage_stats, dict) and "daily_stats" in usage_stats:
                stats_data = []
                for stat in usage_stats["daily_stats"]:
                    stats_data.append({
                        "date": stat.get("date"),
                        "documents_processed": stat.get("documents_processed"),
                        "analyses_performed": stat.get("analyses_performed"),
                        "total_tokens": stat.get("total_tokens"),
                        "total_cost": stat.get("total_cost"),
                        "api_calls": stat.get("api_calls")
                    })

                df = pd.DataFrame(stats_data)
                df.to_csv(output, index=False)

        return output.getvalue()

    async def _export_as_zip(self, user_data: Dict[str, Any], include_content: bool) -> bytes:
        """Export data as ZIP archive."""
        zip_buffer = BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add metadata
            metadata = {
                "user_id": user_data.get("user_id"),
                "export_timestamp": datetime.utcnow().isoformat(),
                "format": "zip",
                "version": "1.0",
                "total_documents": len(user_data.get("data", {}).get("documents", []))
            }
            zip_file.writestr("metadata.json", json.dumps(metadata, indent=2, default=str))

            # Add user profile
            if "user_profile" in user_data.get("data", {}):
                profile = user_data["data"]["user_profile"]
                # Remove sensitive data
                safe_profile = {k: v for k, v in profile.items() if k not in ["hashed_password"]}
                zip_file.writestr("user_profile.json", json.dumps(safe_profile, indent=2, default=str))

            # Add documents
            if "documents" in user_data.get("data", {}):
                documents = user_data["data"]["documents"]
                for i, doc in enumerate(documents):
                    # Document metadata
                    metadata = doc.get("metadata", {})
                    safe_metadata = {k: v for k, v in metadata.items() if k != "mongodb_id"}
                    zip_file.writestr(f"documents/doc_{i}_metadata.json", json.dumps(safe_metadata, indent=2, default=str))

                    # Document content (if requested)
                    if include_content and "content" in doc:
                        content = doc["content"]
                        if isinstance(content, dict) and "content" in content:
                            zip_file.writestr(f"documents/doc_{i}_content.txt", content["content"])

            # Add usage statistics
            if "usage_stats" in user_data.get("data", {}):
                usage_stats = user_data["data"]["usage_stats"]
                zip_file.writestr("usage_stats.json", json.dumps(usage_stats, indent=2, default=str))

        zip_buffer.seek(0)
        return zip_buffer.getvalue()

    async def export_system_data(
        self,
        include_user_data: bool = False,
        format: str = "json"
    ) -> Union[str, bytes, Dict[str, Any]]:
        """
        Export system-wide data for backup purposes.

        Args:
            include_user_data: Whether to include all user data
            format: Export format ('json', 'zip')

        Returns:
            Exported system data
        """
        try:
            self.logger.info("Starting system data export")

            export_data = {
                "export_info": {
                    "export_timestamp": datetime.utcnow().isoformat(),
                    "format": format,
                    "version": "1.0",
                    "include_user_data": include_user_data
                },
                "system_stats": await self.persistence.get_system_stats()
            }

            if include_user_data:
                # This would require getting all users and their data
                # For now, just include system stats
                pass

            if format == "json":
                return json.dumps(export_data, indent=2, default=str)
            elif format == "zip":
                return await self._export_system_as_zip(export_data)
            else:
                raise ValueError(f"Unsupported format: {format}")

        except Exception as e:
            self.logger.error(f"Error exporting system data: {str(e)}")
            raise

    async def _export_system_as_zip(self, system_data: Dict[str, Any]) -> bytes:
        """Export system data as ZIP archive."""
        zip_buffer = BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add system data
            zip_file.writestr("system_data.json", json.dumps(system_data, indent=2, default=str))

            # Add database statistics
            for db_name, stats in system_data.get("system_stats", {}).get("databases", {}).items():
                zip_file.writestr(f"database_stats/{db_name}_stats.json", json.dumps(stats, indent=2, default=str))

        zip_buffer.seek(0)
        return zip_buffer.getvalue()


class DataImportService:
    """
    Service for importing data into the AI Law Firm system.

    Supports importing data from various formats and sources,
    with validation and conflict resolution.
    """

    def __init__(self, persistence_service: DataPersistenceService):
        self.persistence = persistence_service
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    async def import_user_data(
        self,
        data: Union[str, bytes, Dict[str, Any]],
        data_format: str = "json",
        conflict_resolution: str = "skip"  # 'skip', 'overwrite', 'merge'
    ) -> Dict[str, Any]:
        """
        Import user data from various formats.

        Args:
            data: Data to import
            data_format: Format of the data ('json', 'zip', 'csv')
            conflict_resolution: How to handle conflicts ('skip', 'overwrite', 'merge')

        Returns:
            Import results summary
        """
        try:
            self.logger.info(f"Starting data import in {data_format} format")

            if data_format == "json":
                import_data = self._parse_json_data(data)
            elif data_format == "zip":
                import_data = await self._parse_zip_data(data)
            elif data_format == "csv":
                import_data = self._parse_csv_data(data)
            else:
                raise ValueError(f"Unsupported import format: {data_format}")

            return await self._process_import_data(import_data, conflict_resolution)

        except Exception as e:
            self.logger.error(f"Error importing data: {str(e)}")
            raise

    def _parse_json_data(self, data: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Parse JSON data."""
        if isinstance(data, str):
            return json.loads(data)
        return data

    async def _parse_zip_data(self, data: bytes) -> Dict[str, Any]:
        """Parse ZIP archive data."""
        import_data = {}

        with zipfile.ZipFile(BytesIO(data), 'r') as zip_file:
            for filename in zip_file.namelist():
                if filename.endswith('.json'):
                    with zip_file.open(filename) as f:
                        content = f.read().decode('utf-8')
                        if filename == "metadata.json":
                            import_data["metadata"] = json.loads(content)
                        elif filename == "user_profile.json":
                            import_data["user_profile"] = json.loads(content)
                        elif filename.startswith("documents/"):
                            if "documents" not in import_data:
                                import_data["documents"] = []
                            import_data["documents"].append(json.loads(content))

        return import_data

    def _parse_csv_data(self, data: str) -> Dict[str, Any]:
        """Parse CSV data."""
        # This would parse CSV files and convert to structured data
        # Implementation depends on the specific CSV structure
        return {"csv_data": data}

    async def _process_import_data(
        self,
        import_data: Dict[str, Any],
        conflict_resolution: str
    ) -> Dict[str, Any]:
        """Process and import the parsed data."""
        results = {
            "imported": 0,
            "skipped": 0,
            "errors": 0,
            "details": []
        }

        try:
            # Import user profile if present
            if "user_profile" in import_data:
                user_data = import_data["user_profile"]
                # Note: User creation would need to be handled carefully
                # to avoid conflicts with existing users
                results["details"].append(f"User profile found: {user_data.get('email', 'unknown')}")

            # Import documents if present
            if "documents" in import_data:
                for doc in import_data["documents"]:
                    try:
                        # This would create documents in the system
                        # Implementation depends on the document structure
                        results["imported"] += 1
                        results["details"].append(f"Document imported: {doc.get('filename', 'unknown')}")
                    except Exception as e:
                        results["errors"] += 1
                        results["details"].append(f"Error importing document: {str(e)}")

            results["success"] = True

        except Exception as e:
            results["success"] = False
            results["error"] = str(e)

        return results


class DataBackupService:
    """
    Service for automated data backup and recovery operations.
    """

    def __init__(self, persistence_service: DataPersistenceService, backup_dir: str = "./backups"):
        self.persistence = persistence_service
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    async def create_backup(
        self,
        backup_type: str = "full",  # 'full', 'incremental', 'user'
        user_id: Optional[str] = None,
        include_content: bool = True
    ) -> str:
        """
        Create a data backup.

        Args:
            backup_type: Type of backup to create
            user_id: User ID for user-specific backups
            include_content: Whether to include document content

        Returns:
            Path to the created backup file
        """
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{backup_type}_{timestamp}.zip"

            if backup_type == "user" and user_id:
                backup_name = f"backup_user_{user_id}_{timestamp}.zip"

            backup_path = self.backup_dir / backup_name

            self.logger.info(f"Creating {backup_type} backup: {backup_name}")

            if backup_type == "full":
                # Export system data
                export_service = DataExportService(self.persistence)
                backup_data = await export_service.export_system_data(
                    include_user_data=True,
                    format="zip"
                )
            elif backup_type == "user" and user_id:
                # Export user data
                export_service = DataExportService(self.persistence)
                backup_data = await export_service.export_user_data(
                    user_id=user_id,
                    format="zip",
                    include_content=include_content
                )
            else:
                raise ValueError(f"Unsupported backup type: {backup_type}")

            # Write backup to file
            with open(backup_path, 'wb') as f:
                f.write(backup_data)

            self.logger.info(f"Backup created successfully: {backup_path}")
            return str(backup_path)

        except Exception as e:
            self.logger.error(f"Error creating backup: {str(e)}")
            raise

    async def restore_backup(
        self,
        backup_path: str,
        restore_type: str = "merge"  # 'merge', 'overwrite', 'preview'
    ) -> Dict[str, Any]:
        """
        Restore data from a backup.

        Args:
            backup_path: Path to the backup file
            restore_type: Type of restore operation

        Returns:
            Restore operation results
        """
        try:
            self.logger.info(f"Starting backup restore: {backup_path}")

            # Read backup file
            with open(backup_path, 'rb') as f:
                backup_data = f.read()

            # Import data
            import_service = DataImportService(self.persistence)
            results = await import_service.import_user_data(
                data=backup_data,
                data_format="zip",
                conflict_resolution=restore_type
            )

            self.logger.info(f"Backup restore completed: {results}")
            return results

        except Exception as e:
            self.logger.error(f"Error restoring backup: {str(e)}")
            raise

    async def list_backups(self) -> List[Dict[str, Any]]:
        """List available backups."""
        try:
            backups = []
            for backup_file in self.backup_dir.glob("backup_*.zip"):
                stat = backup_file.stat()
                backups.append({
                    "filename": backup_file.name,
                    "path": str(backup_file),
                    "size_bytes": stat.st_size,
                    "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "type": self._get_backup_type_from_filename(backup_file.name)
                })

            return sorted(backups, key=lambda x: x["created_at"], reverse=True)

        except Exception as e:
            self.logger.error(f"Error listing backups: {str(e)}")
            return []

    def _get_backup_type_from_filename(self, filename: str) -> str:
        """Extract backup type from filename."""
        if "full" in filename:
            return "full"
        elif "user" in filename:
            return "user"
        elif "incremental" in filename:
            return "incremental"
        else:
            return "unknown"

    async def cleanup_old_backups(self, retention_days: int = 30) -> int:
        """Clean up old backup files."""
        try:
            cutoff_date = datetime.utcnow().timestamp() - (retention_days * 24 * 60 * 60)
            deleted_count = 0

            for backup_file in self.backup_dir.glob("backup_*.zip"):
                if backup_file.stat().st_ctime < cutoff_date:
                    backup_file.unlink()
                    deleted_count += 1

            self.logger.info(f"Cleaned up {deleted_count} old backups")
            return deleted_count

        except Exception as e:
            self.logger.error(f"Error cleaning up old backups: {str(e)}")
            return 0