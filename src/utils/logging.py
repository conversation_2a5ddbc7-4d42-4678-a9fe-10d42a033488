"""
Logging configuration and utilities for the AI Law Firm system.

This module provides centralized logging configuration with support for:
- Multiple log levels and formats
- Structured logging with JSON output
- File and console logging
- Log rotation and management
- Performance monitoring
"""
import logging
import logging.handlers
import json
import sys
import os
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict

from core.config.settings import LoggingConfig


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: str
    level: str
    logger_name: str
    message: str
    module: Optional[str] = None
    function: Optional[str] = None
    line_number: Optional[int] = None
    exception: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert log entry to dictionary."""
        return {k: v for k, v in asdict(self).items() if v is not None}


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        # Create base log entry
        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created).isoformat(),
            level=record.levelname,
            logger_name=record.name,
            message=record.getMessage(),
            module=getattr(record, 'module', None),
            function=getattr(record, 'funcName', None),
            line_number=getattr(record, 'lineno', None),
        )

        # Add exception info if present
        if record.exc_info:
            log_entry.exception = self.formatException(record.exc_info)

        # Add extra data from record
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'exc_info', 'exc_text', 'stack_info',
                'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
                'thread', 'threadName', 'processName', 'process', 'message'
            }:
                extra_data[key] = value

        if extra_data:
            log_entry.extra_data = extra_data

        return json.dumps(log_entry.to_dict(), indent=None, separators=(',', ':'))


class ConsoleFormatter(logging.Formatter):
    """Human-readable console formatter."""

    def __init__(self, include_timestamp: bool = True):
        """Initialize console formatter."""
        if include_timestamp:
            fmt = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        else:
            fmt = "%(name)s - %(levelname)s - %(message)s"

        super().__init__(fmt, datefmt="%Y-%m-%d %H:%M:%S")


class LoggerManager:
    """Centralized logger management."""

    def __init__(self):
        self._loggers: Dict[str, logging.Logger] = {}
        self._configured = False

    def configure_logging(self, config: LoggingConfig) -> None:
        """
        Configure logging system based on configuration.

        Args:
            config: Logging configuration
        """
        if self._configured:
            return

        # Clear existing handlers
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Set root logger level
        root_logger.setLevel(getattr(logging, config.level.upper(), logging.INFO))

        # Create formatters
        console_formatter = ConsoleFormatter()
        structured_formatter = StructuredFormatter()

        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, config.level.upper(), logging.INFO))
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

        # File handler (if configured)
        if config.file_path:
            # Ensure directory exists
            log_dir = Path(config.file_path).parent
            log_dir.mkdir(parents=True, exist_ok=True)

            # Create rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                config.file_path,
                maxBytes=config.max_file_size,
                backupCount=config.backup_count
            )
            file_handler.setLevel(getattr(logging, config.level.upper(), logging.INFO))
            file_handler.setFormatter(structured_formatter)
            root_logger.addHandler(file_handler)

        self._configured = True

    def get_logger(self, name: str) -> logging.Logger:
        """
        Get or create a logger with the specified name.

        Args:
            name: Logger name (usually module name)

        Returns:
            Configured logger instance
        """
        if name not in self._loggers:
            logger = logging.getLogger(name)
            self._loggers[name] = logger

        return self._loggers[name]

    def set_level(self, name: str, level: str) -> None:
        """
        Set logging level for a specific logger.

        Args:
            name: Logger name
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        logger = self.get_logger(name)
        logger.setLevel(getattr(logging, level.upper(), logging.INFO))

    def add_context(self, logger_name: str, **context) -> None:
        """
        Add context information to a logger.

        Args:
            logger_name: Name of the logger
            **context: Context key-value pairs
        """
        logger = self.get_logger(logger_name)

        # Add context as extra data to all log records
        class ContextFilter(logging.Filter):
            def filter(self, record):
                for key, value in context.items():
                    setattr(record, key, value)
                return True

        logger.addFilter(ContextFilter())


# Global logger manager instance
_logger_manager = LoggerManager()


def configure_logging(config: LoggingConfig) -> None:
    """Configure the logging system."""
    _logger_manager.configure_logging(config)


# Alias for backward compatibility
setup_logging = configure_logging


def get_logger(name: str) -> logging.Logger:
    """
    Get a configured logger instance.

    Args:
        name: Logger name (usually __name__)

    Returns:
        Configured logger instance
    """
    return _logger_manager.get_logger(name)


def set_log_level(name: str, level: str) -> None:
    """Set logging level for a specific logger."""
    _logger_manager.set_level(name, level)


def add_log_context(logger_name: str, **context) -> None:
    """Add context information to a logger."""
    _logger_manager.add_context(logger_name, **context)


# Convenience functions for different log levels
def log_performance(logger: logging.Logger, operation: str, duration: float, **extra) -> None:
    """Log performance information."""
    logger.info(
        f"Performance: {operation} completed in {duration:.3f}s",
        extra={"performance_data": {"operation": operation, "duration": duration, **extra}}
    )


def log_error_with_context(
    logger: logging.Logger,
    message: str,
    error: Exception,
    **context
) -> None:
    """Log error with additional context."""
    logger.error(
        f"{message}: {str(error)}",
        exc_info=True,
        extra={"error_context": context}
    )


def log_agent_activity(
    logger: logging.Logger,
    agent_name: str,
    action: str,
    **details
) -> None:
    """Log agent activity."""
    logger.info(
        f"Agent {agent_name}: {action}",
        extra={"agent_activity": {"agent": agent_name, "action": action, **details}}
    )


def log_user_interaction(
    logger: logging.Logger,
    user_id: Optional[str],
    action: str,
    **details
) -> None:
    """Log user interaction."""
    logger.info(
        f"User interaction: {action}",
        extra={"user_interaction": {"user_id": user_id, "action": action, **details}}
    )


def create_request_logger(request_id: str) -> logging.Logger:
    """
    Create a logger with request-specific context.

    Args:
        request_id: Unique request identifier

    Returns:
        Logger with request context
    """
    logger = get_logger("request")
    add_log_context("request", request_id=request_id)
    return logger


# Performance monitoring decorator
def log_execution_time(logger: Optional[logging.Logger] = None):
    """
    Decorator to log execution time of functions.

    Args:
        logger: Logger to use (creates default if None)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)

            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                logger.info(
                    f"Function {func.__name__} executed successfully",
                    extra={
                        "performance": {
                            "function": func.__name__,
                            "duration": duration,
                            "success": True
                        }
                    }
                )
                return result
            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                logger.error(
                    f"Function {func.__name__} failed",
                    exc_info=True,
                    extra={
                        "performance": {
                            "function": func.__name__,
                            "duration": duration,
                            "success": False,
                            "error": str(e)
                        }
                    }
                )
                raise

        return wrapper
    return decorator


# Context manager for request logging
class RequestLogger:
    """Context manager for logging request lifecycle."""

    def __init__(self, request_id: str, operation: str):
        self.request_id = request_id
        self.operation = operation
        self.logger = create_request_logger(request_id)
        self.start_time = None

    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(f"Starting {self.operation}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()

        if exc_type is None:
            self.logger.info(
                f"Completed {self.operation}",
                extra={"request_complete": {"duration": duration, "success": True}}
            )
        else:
            self.logger.error(
                f"Failed {self.operation}: {str(exc_val)}",
                exc_info=True,
                extra={"request_complete": {"duration": duration, "success": False, "error": str(exc_val)}}
            )


# Utility functions for log analysis
def parse_log_file(log_file_path: str) -> List[Dict[str, Any]]:
    """
    Parse a structured log file into a list of log entries.

    Args:
        log_file_path: Path to the log file

    Returns:
        List of parsed log entries
    """
    entries = []

    with open(log_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    entry = json.loads(line)
                    entries.append(entry)
                except json.JSONDecodeError:
                    # Skip malformed lines
                    continue

    return entries


def get_logs_by_level(log_entries: List[Dict[str, Any]], level: str) -> List[Dict[str, Any]]:
    """Filter log entries by level."""
    return [entry for entry in log_entries if entry.get('level') == level.upper()]


def get_logs_by_logger(log_entries: List[Dict[str, Any]], logger_name: str) -> List[Dict[str, Any]]:
    """Filter log entries by logger name."""
    return [entry for entry in log_entries if entry.get('logger_name') == logger_name]


def get_performance_metrics(log_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Extract performance metrics from log entries."""
    performance_entries = [
        entry for entry in log_entries
        if entry.get('extra_data', {}).get('performance_data') or
           entry.get('extra_data', {}).get('performance')
    ]

    metrics = {
        "total_operations": len(performance_entries),
        "successful_operations": 0,
        "failed_operations": 0,
        "average_duration": 0.0,
        "operations": []
    }

    total_duration = 0.0

    for entry in performance_entries:
        perf_data = entry.get('extra_data', {}).get('performance_data') or \
                   entry.get('extra_data', {}).get('performance', {})

        if perf_data.get('success', True):
            metrics["successful_operations"] += 1
        else:
            metrics["failed_operations"] += 1

        duration = perf_data.get('duration', 0)
        total_duration += duration

        metrics["operations"].append({
            "operation": perf_data.get('operation') or perf_data.get('function'),
            "duration": duration,
            "success": perf_data.get('success', True),
            "timestamp": entry.get('timestamp')
        })

    if metrics["total_operations"] > 0:
        metrics["average_duration"] = total_duration / metrics["total_operations"]

    return metrics


# Initialize default logging on import
def _setup_default_logging():
    """Setup default logging configuration."""
    if not _logger_manager._configured:
        default_config = LoggingConfig()
        configure_logging(default_config)

# Setup default logging
_setup_default_logging()