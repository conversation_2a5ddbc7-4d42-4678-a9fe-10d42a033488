#!/usr/bin/env python3
"""
Document Upload and Persistence System for AI Law Firm.

This module provides comprehensive document management capabilities:
- Secure document upload with validation and virus scanning
- Multi-format support (PDF, DOCX, TXT, legal formats)
- Intelligent file storage with deduplication
- Document metadata extraction and management
- Version control and change tracking
- User quota management and storage limits
- Secure access control and sharing
- Integration with search and analysis systems
- Backup and disaster recovery
- Audit logging for compliance
"""

import asyncio
import hashlib
import json
import mimetypes
import os
import shutil
import tempfile
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, BinaryIO
from dataclasses import dataclass, field
from enum import Enum

import aiofiles
import magic
from PIL import Image
import pypdf
from docx import Document as DocxDocument
import pytesseract
from motor.motor_asyncio import AsyncIOMotorClient
import asyncpg

from utils.logging import get_logger
from utils.exceptions import ValidationError, FileUploadError, StorageError
from core.auth.user_management import User, Permission


class DocumentType(str, Enum):
    """Supported document types."""
    PDF = "pdf"
    WORD_DOC = "docx"
    TEXT = "txt"
    RTF = "rtf"
    HTML = "html"
    XML = "xml"
    JSON = "json"
    CSV = "csv"
    IMAGE = "image"
    OTHER = "other"


class DocumentStatus(str, Enum):
    """Document processing status."""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    QUARANTINED = "quarantined"
    DELETED = "deleted"


class StorageBackend(str, Enum):
    """Storage backend types."""
    LOCAL = "local"
    S3 = "s3"
    AZURE = "azure"
    GCP = "gcp"


@dataclass
class DocumentMetadata:
    """Document metadata extracted from file."""
    document_id: str
    filename: str
    original_filename: str
    file_size: int
    mime_type: str
    document_type: DocumentType
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    character_count: Optional[int] = None
    language: Optional[str] = None
    encoding: Optional[str] = None
    checksum: str = ""
    extracted_text: str = ""
    ocr_text: str = ""
    title: Optional[str] = None
    author: Optional[str] = None
    created_date: Optional[datetime] = None
    modified_date: Optional[datetime] = None
    keywords: List[str] = field(default_factory=list)
    entities: Dict[str, List[str]] = field(default_factory=dict)
    classification: Dict[str, Any] = field(default_factory=dict)
    risk_assessment: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DocumentVersion:
    """Document version information."""
    version_id: str
    document_id: str
    version_number: int
    file_path: str
    file_size: int
    checksum: str
    changes_summary: str
    created_by: str
    created_at: datetime
    is_current: bool = False


@dataclass
class Document:
    """Document model with comprehensive metadata."""
    document_id: str
    user_id: str
    organization_id: Optional[str]
    filename: str
    original_filename: str
    file_path: str
    storage_backend: StorageBackend
    status: DocumentStatus
    metadata: DocumentMetadata
    versions: List[DocumentVersion] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    permissions: Dict[str, List[str]] = field(default_factory=dict)
    share_links: List[Dict[str, Any]] = field(default_factory=list)
    processing_attempts: int = 0
    last_processed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    deleted_at: Optional[datetime] = None

    @property
    def is_deleted(self) -> bool:
        """Check if document is deleted."""
        return self.deleted_at is not None

    @property
    def current_version(self) -> Optional[DocumentVersion]:
        """Get current version of document."""
        return next((v for v in self.versions if v.is_current), None)

    @property
    def file_size_mb(self) -> float:
        """Get file size in MB."""
        return self.metadata.file_size / (1024 * 1024)

    def can_access(self, user: User) -> bool:
        """Check if user can access this document."""
        # Owner always has access
        if user.user_id == self.user_id:
            return True

        # Check organization access
        if self.organization_id and user.organization_id == self.organization_id:
            return True

        # Check explicit permissions
        user_permissions = self.permissions.get(user.user_id, [])
        if "read" in user_permissions or "write" in user_permissions:
            return True

        return False

    def can_modify(self, user: User) -> bool:
        """Check if user can modify this document."""
        # Owner always has access
        if user.user_id == self.user_id:
            return True

        # Check organization admin access
        if (self.organization_id and user.organization_id == self.organization_id and
            user.role.value in ["admin", "super_admin"]):
            return True

        # Check explicit permissions
        user_permissions = self.permissions.get(user.user_id, [])
        return "write" in user_permissions


class DocumentUploadService:
    """
    Comprehensive document upload and management service.

    Features:
    - Secure file upload with validation and virus scanning
    - Multi-format document processing (PDF, DOCX, TXT, etc.)
    - Intelligent storage with deduplication
    - Document metadata extraction and indexing
    - Version control and change tracking
    - User quota management and storage limits
    - Secure access control and sharing
    - Integration with search and analysis systems
    - Backup and disaster recovery
    - Audit logging for compliance
    """

    def __init__(self, config: Dict[str, Any]):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.config = config

        # Storage configuration
        self.storage_config = config.get('storage', {})
        self.storage_backend = StorageBackend(self.storage_config.get('backend', 'local'))
        self.storage_path = Path(self.storage_config.get('path', './storage/documents'))
        self.max_file_size_mb = self.storage_config.get('max_file_size_mb', 50)
        self.allowed_extensions = self.storage_config.get('allowed_extensions',
            ['.pdf', '.docx', '.txt', '.rtf', '.html', '.xml', '.json', '.csv'])

        # Processing configuration
        self.enable_ocr = config.get('processing', {}).get('enable_ocr', True)
        self.enable_entity_extraction = config.get('processing', {}).get('enable_entity_extraction', True)
        self.enable_classification = config.get('processing', {}).get('enable_classification', True)

        # Quota configuration
        self.user_quota_mb = config.get('quotas', {}).get('user_mb', 1000)
        self.organization_quota_mb = config.get('quotas', {}).get('organization_mb', 10000)

        # Database connections
        self.pg_pool = None
        self.mongo_client = None
        self.mongo_db = None

        # Supported MIME types
        self.supported_mime_types = {
            'application/pdf': DocumentType.PDF,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': DocumentType.WORD_DOC,
            'text/plain': DocumentType.TEXT,
            'text/rtf': DocumentType.RTF,
            'text/html': DocumentType.HTML,
            'application/xml': DocumentType.XML,
            'application/json': DocumentType.JSON,
            'text/csv': DocumentType.CSV,
            'image/jpeg': DocumentType.IMAGE,
            'image/png': DocumentType.IMAGE,
            'image/gif': DocumentType.IMAGE,
            'image/webp': DocumentType.IMAGE,
        }

    async def initialize(self):
        """Initialize database connections and storage."""
        try:
            # PostgreSQL connection
            self.pg_pool = await asyncpg.create_pool(
                host=self.config.get('postgres_host', 'localhost'),
                port=self.config.get('postgres_port', 54320),
                database=self.config.get('postgres_db', 'ai_law_firm'),
                user=self.config.get('postgres_user', 'ai_law_user'),
                password=self.config.get('postgres_password', 'ai_law_password_2024'),
                min_size=1,
                max_size=10
            )

            # MongoDB connection
            mongo_uri = f"mongodb://{self.config.get('mongo_host', 'localhost')}:{self.config.get('mongo_port', 27019)}"
            self.mongo_client = AsyncIOMotorClient(mongo_uri)
            self.mongo_db = self.mongo_client.ai_law_firm

            # Create storage directory
            self.storage_path.mkdir(parents=True, exist_ok=True)

            # Create database tables
            await self._create_tables()

            self.logger.info("Document Upload Service initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize document upload service: {str(e)}")
            return False

    async def _create_tables(self):
        """Create necessary database tables."""
        try:
            async with self.pg_pool.acquire() as conn:
                # Documents table - drop and recreate if exists to ensure proper schema
                await conn.execute("DROP TABLE IF EXISTS document_versions CASCADE")
                await conn.execute("DROP TABLE IF EXISTS documents CASCADE")

                await conn.execute("""
                    CREATE TABLE documents (
                        document_id VARCHAR(36) PRIMARY KEY,
                        user_id VARCHAR(36) NOT NULL,
                        organization_id VARCHAR(36),
                        filename VARCHAR(255) NOT NULL,
                        original_filename VARCHAR(255) NOT NULL,
                        file_path VARCHAR(500) NOT NULL,
                        storage_backend VARCHAR(20) NOT NULL,
                        status VARCHAR(20) NOT NULL,
                        metadata JSONB DEFAULT '{}',
                        versions JSONB DEFAULT '[]',
                        tags TEXT[] DEFAULT '{}',
                        permissions JSONB DEFAULT '{}',
                        share_links JSONB DEFAULT '[]',
                        processing_attempts INTEGER DEFAULT 0,
                        last_processed_at TIMESTAMP,
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        deleted_at TIMESTAMP
                    )
                """)

                # Document versions table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS document_versions (
                        version_id VARCHAR(36) PRIMARY KEY,
                        document_id VARCHAR(36) NOT NULL,
                        version_number INTEGER NOT NULL,
                        file_path VARCHAR(500) NOT NULL,
                        file_size INTEGER NOT NULL,
                        checksum VARCHAR(64) NOT NULL,
                        changes_summary TEXT,
                        created_by VARCHAR(36) NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_current BOOLEAN DEFAULT FALSE,
                        FOREIGN KEY (document_id) REFERENCES documents(document_id) ON DELETE CASCADE
                    )
                """)

                # Document shares table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS document_shares (
                        share_id VARCHAR(36) PRIMARY KEY,
                        document_id VARCHAR(36) NOT NULL REFERENCES documents(document_id),
                        shared_by VARCHAR(36) NOT NULL,
                        shared_with VARCHAR(36) NOT NULL,
                        permissions TEXT[] DEFAULT '{}',
                        expires_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Create indexes
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_documents_user ON documents(user_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_documents_org ON documents(organization_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_document_versions_doc ON document_versions(document_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_document_shares_doc ON document_shares(document_id)")

        except Exception as e:
            self.logger.error(f"Error creating tables: {str(e)}")

    async def upload_document(
        self,
        file_data: bytes,
        filename: str,
        user: User,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Document:
        """
        Upload and process a document.

        Args:
            file_data: Raw file data
            filename: Original filename
            user: User uploading the document
            metadata: Additional metadata

        Returns:
            Document: Created document object

        Raises:
            FileUploadError: If upload fails
            ValidationError: If file is invalid
        """
        try:
            # Validate file
            await self._validate_file(file_data, filename, user)

            # Generate document ID and paths
            document_id = str(uuid.uuid4())
            safe_filename = self._sanitize_filename(filename)
            file_path = self._generate_file_path(document_id, safe_filename)

            # Save file temporarily for processing
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name

            try:
                # Extract metadata
                doc_metadata = await self._extract_metadata(temp_file_path, filename, document_id)

                # Create document object
                document = Document(
                    document_id=document_id,
                    user_id=user.user_id,
                    organization_id=user.organization_id,
                    filename=safe_filename,
                    original_filename=filename,
                    file_path=str(file_path),
                    storage_backend=self.storage_backend,
                    status=DocumentStatus.UPLOADING,
                    metadata=doc_metadata
                )

                # Save to permanent storage
                await self._save_file(temp_file_path, file_path, file_data)

                # Create initial version
                version = DocumentVersion(
                    version_id=str(uuid.uuid4()),
                    document_id=document_id,
                    version_number=1,
                    file_path=str(file_path),
                    file_size=len(file_data),
                    checksum=self._calculate_checksum(file_data),
                    changes_summary="Initial upload",
                    created_by=user.user_id,
                    created_at=datetime.utcnow(),
                    is_current=True
                )
                document.versions = [version]

                # Save to database
                await self._save_document(document)

                # Start background processing
                asyncio.create_task(self._process_document_async(document, temp_file_path))

                # Log upload
                await self._log_document_event(
                    "document_uploaded",
                    document_id=document.document_id,
                    user_id=user.user_id,
                    details={"filename": filename, "size": len(file_data)}
                )

                self.logger.info(f"Document uploaded successfully: {document.filename}")
                return document

            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_file_path)
                except OSError:
                    pass

        except Exception as e:
            self.logger.error(f"Error uploading document: {str(e)}")
            raise FileUploadError(f"Document upload failed: {str(e)}")

    async def _validate_file(self, file_data: bytes, filename: str, user: User):
        """Validate uploaded file."""
        # Check file size
        file_size_mb = len(file_data) / (1024 * 1024)
        if file_size_mb > self.max_file_size_mb:
            raise ValidationError(f"File size {file_size_mb:.1f}MB exceeds maximum {self.max_file_size_mb}MB")

        # Check file extension
        file_ext = Path(filename).suffix.lower()
        if file_ext not in self.allowed_extensions:
            raise ValidationError(f"File type {file_ext} not allowed")

        # Check MIME type
        mime_type = magic.from_buffer(file_data, mime=True)
        if mime_type not in self.supported_mime_types:
            raise ValidationError(f"MIME type {mime_type} not supported")

        # Check user quota
        await self._check_user_quota(user, file_size_mb)

        # Basic security check (file signature validation)
        if not self._is_safe_file(file_data, mime_type):
            raise ValidationError("File failed security validation")

    async def _check_user_quota(self, user: User, file_size_mb: float):
        """Check if user has sufficient quota."""
        async with self.pg_pool.acquire() as conn:
            # Get current usage
            result = await conn.fetchrow("""
                SELECT COALESCE(SUM((metadata->>'file_size')::bigint), 0) as total_size
                FROM documents
                WHERE user_id = $1 AND deleted_at IS NULL
            """, user.user_id)

            current_usage_mb = (result['total_size'] or 0) / (1024 * 1024)
            quota_limit = self.user_quota_mb

            if user.organization_id:
                # Check organization quota
                org_result = await conn.fetchrow("""
                    SELECT COALESCE(SUM((metadata->>'file_size')::bigint), 0) as total_size
                    FROM documents
                    WHERE organization_id = $1 AND deleted_at IS NULL
                """, user.organization_id)

                org_usage_mb = (org_result['total_size'] or 0) / (1024 * 1024)
                quota_limit = min(quota_limit, self.organization_quota_mb - org_usage_mb + current_usage_mb)

            if float(current_usage_mb) + file_size_mb > quota_limit:
                raise ValidationError(f"Upload would exceed quota limit of {quota_limit:.1f}MB")

    def _is_safe_file(self, file_data: bytes, mime_type: str) -> bool:
        """Basic security check for file safety."""
        # Check for common malicious patterns
        dangerous_patterns = [
            b'<?php', b'<%', b'<script', b'eval(', b'exec(',
            b'system(', b'shell_exec(', b'passthru('
        ]

        file_content = file_data[:1024].lower()  # Check first 1KB

        for pattern in dangerous_patterns:
            if pattern in file_content:
                return False

        return True

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage."""
        # Remove path separators and dangerous characters
        safe_name = "".join(c for c in filename if c.isalnum() or c in "._- ")
        return safe_name[:255]  # Limit length

    def _generate_file_path(self, document_id: str, filename: str) -> Path:
        """Generate safe file path."""
        # Create directory structure: documents/{user_id}/{document_id}/
        file_path = self.storage_path / document_id[:2] / document_id / filename
        file_path.parent.mkdir(parents=True, exist_ok=True)
        return file_path

    async def _extract_metadata(self, file_path: str, filename: str, document_id: str) -> DocumentMetadata:
        """Extract comprehensive metadata from document."""
        file_size = os.path.getsize(file_path)
        mime_type = magic.from_file(file_path, mime=True)

        metadata = DocumentMetadata(
            document_id=document_id,
            filename=Path(filename).stem,
            original_filename=filename,
            file_size=file_size,
            mime_type=mime_type,
            document_type=self.supported_mime_types.get(mime_type, DocumentType.OTHER),
            checksum=self._calculate_checksum_file(file_path)
        )

        try:
            if metadata.document_type == DocumentType.PDF:
                metadata = await self._extract_pdf_metadata(file_path, metadata)
            elif metadata.document_type == DocumentType.WORD_DOC:
                metadata = await self._extract_docx_metadata(file_path, metadata)
            elif metadata.document_type == DocumentType.TEXT:
                metadata = await self._extract_text_metadata(file_path, metadata)
            elif metadata.document_type == DocumentType.IMAGE and self.enable_ocr:
                metadata = await self._extract_image_metadata(file_path, metadata)

            # Extract entities if enabled
            if self.enable_entity_extraction and metadata.extracted_text:
                metadata.entities = await self._extract_entities(metadata.extracted_text)

            # Classify document if enabled
            if self.enable_classification and metadata.extracted_text:
                metadata.classification = await self._classify_document(metadata.extracted_text)

        except Exception as e:
            self.logger.warning(f"Error extracting metadata: {str(e)}")

        return metadata

    async def _extract_pdf_metadata(self, file_path: str, metadata: DocumentMetadata) -> DocumentMetadata:
        """Extract metadata from PDF file."""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = pypdf.PdfReader(file)

                metadata.page_count = len(pdf_reader.pages)

                # Extract text
                text_content = []
                for page in pdf_reader.pages[:10]:  # First 10 pages
                    text_content.append(page.extract_text())

                metadata.extracted_text = '\n'.join(text_content)
                metadata.word_count = len(metadata.extracted_text.split())
                metadata.character_count = len(metadata.extracted_text)

                # Extract PDF metadata
                pdf_info = pdf_reader.metadata
                if pdf_info:
                    metadata.title = pdf_info.title
                    metadata.author = pdf_info.author
                    if pdf_info.creation_date:
                        metadata.created_date = pdf_info.creation_date
                    if pdf_info.modification_date:
                        metadata.modified_date = pdf_info.modification_date

        except Exception as e:
            self.logger.warning(f"Error extracting PDF metadata: {str(e)}")

        return metadata

    async def _extract_docx_metadata(self, file_path: str, metadata: DocumentMetadata) -> DocumentMetadata:
        """Extract metadata from DOCX file."""
        try:
            doc = DocxDocument(file_path)

            # Extract text
            text_content = []
            for paragraph in doc.paragraphs:
                text_content.append(paragraph.text)

            metadata.extracted_text = '\n'.join(text_content)
            metadata.word_count = len(metadata.extracted_text.split())
            metadata.character_count = len(metadata.extracted_text)

            # Extract document properties
            core_props = doc.core_properties
            metadata.title = core_props.title
            metadata.author = core_props.author
            if core_props.created:
                metadata.created_date = core_props.created
            if core_props.modified:
                metadata.modified_date = core_props.modified

        except Exception as e:
            self.logger.warning(f"Error extracting DOCX metadata: {str(e)}")

        return metadata

    async def _extract_text_metadata(self, file_path: str, metadata: DocumentMetadata) -> DocumentMetadata:
        """Extract metadata from text file."""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                metadata.extracted_text = await f.read()

            metadata.word_count = len(metadata.extracted_text.split())
            metadata.character_count = len(metadata.extracted_text)

        except UnicodeDecodeError:
            # Try with different encoding
            async with aiofiles.open(file_path, 'r', encoding='latin-1') as f:
                metadata.extracted_text = await f.read()
                metadata.encoding = 'latin-1'

        return metadata

    async def _extract_image_metadata(self, file_path: str, metadata: DocumentMetadata) -> DocumentMetadata:
        """Extract metadata from image file with OCR."""
        try:
            # Basic image info
            with Image.open(file_path) as img:
                metadata.page_count = 1  # Images are single page

            # OCR text extraction
            if self.enable_ocr:
                metadata.ocr_text = pytesseract.image_to_string(Image.open(file_path))
                metadata.extracted_text = metadata.ocr_text
                metadata.word_count = len(metadata.ocr_text.split())
                metadata.character_count = len(metadata.ocr_text)

        except Exception as e:
            self.logger.warning(f"Error extracting image metadata: {str(e)}")

        return metadata

    async def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract named entities from text."""
        # Simple entity extraction (can be enhanced with NLP models)
        entities = {
            'dates': [],
            'money': [],
            'organizations': [],
            'people': [],
            'locations': []
        }

        # Basic pattern matching (placeholder for more sophisticated NLP)
        import re

        # Date patterns
        date_patterns = [
            r'\b\d{1,2}/\d{1,2}/\d{2,4}\b',
            r'\b\d{1,2}-\d{1,2}-\d{2,4}\b',
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b'
        ]

        for pattern in date_patterns:
            entities['dates'].extend(re.findall(pattern, text))

        # Money patterns
        money_patterns = [r'\$\d+(?:,\d{3})*(?:\.\d{2})?']
        entities['money'].extend(re.findall('|'.join(money_patterns), text))

        return entities

    async def _classify_document(self, text: str) -> Dict[str, Any]:
        """Classify document type and extract legal categories."""
        # Simple classification based on keywords (can be enhanced with ML models)
        legal_keywords = {
            'contract': ['agreement', 'contract', 'party', 'terms', 'conditions', 'breach', 'termination'],
            'court_document': ['court', 'judge', 'plaintiff', 'defendant', 'complaint', 'motion', 'ruling'],
            'legal_brief': ['brief', 'argument', 'precedent', 'case law', 'statute', 'citation'],
            'regulatory': ['regulation', 'compliance', 'agency', 'permit', 'license', 'certification'],
            'corporate': ['corporation', 'shareholder', 'board', 'meeting', 'minutes', 'resolution']
        }

        classification = {
            'document_type': 'general',
            'legal_categories': [],
            'confidence_score': 0.0,
            'keywords_found': []
        }

        text_lower = text.lower()
        max_score = 0

        for doc_type, keywords in legal_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > max_score:
                max_score = score
                classification['document_type'] = doc_type
                classification['confidence_score'] = min(score / len(keywords), 1.0)

            if score > 0:
                classification['legal_categories'].append(doc_type)
                classification['keywords_found'].extend([kw for kw in keywords if kw in text_lower])

        return classification

    def _calculate_checksum(self, data: bytes) -> str:
        """Calculate SHA-256 checksum of data."""
        return hashlib.sha256(data).hexdigest()

    def _calculate_checksum_file(self, file_path: str) -> str:
        """Calculate SHA-256 checksum of file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()

    async def _save_file(self, temp_path: str, final_path: Path, file_data: bytes):
        """Save file to permanent storage."""
        try:
            if self.storage_backend == StorageBackend.LOCAL:
                # Ensure directory exists
                final_path.parent.mkdir(parents=True, exist_ok=True)

                # Move file to final location
                shutil.move(temp_path, final_path)

            else:
                # Cloud storage implementation would go here
                raise NotImplementedError(f"Storage backend {self.storage_backend} not implemented")

        except Exception as e:
            raise StorageError(f"Failed to save file: {str(e)}")

    async def _save_document(self, document: Document):
        """Save document to database."""
        try:
            async with self.pg_pool.acquire() as conn:
                # Prepare metadata as JSON
                import json
                metadata_json = {
                    'document_id': document.metadata.document_id,
                    'filename': document.metadata.filename,
                    'original_filename': document.metadata.original_filename,
                    'file_size': document.metadata.file_size,
                    'mime_type': document.metadata.mime_type,
                    'document_type': document.metadata.document_type.value,
                    'page_count': document.metadata.page_count,
                    'word_count': document.metadata.word_count,
                    'character_count': document.metadata.character_count,
                    'language': document.metadata.language,
                    'encoding': document.metadata.encoding,
                    'checksum': document.metadata.checksum,
                    'title': document.metadata.title,
                    'author': document.metadata.author,
                    'created_date': document.metadata.created_date.isoformat() if document.metadata.created_date else None,
                    'modified_date': document.metadata.modified_date.isoformat() if document.metadata.modified_date else None,
                    'keywords': document.metadata.keywords,
                    'entities': document.metadata.entities,
                    'classification': document.metadata.classification,
                    'risk_assessment': document.metadata.risk_assessment
                }

                # Prepare versions as JSON
                versions_json = [{'version_id': v.version_id, 'version_number': v.version_number,
                  'file_path': v.file_path, 'file_size': v.file_size,
                  'checksum': v.checksum, 'changes_summary': v.changes_summary,
                  'created_by': v.created_by, 'created_at': v.created_at.isoformat(),
                  'is_current': v.is_current} for v in document.versions]

                await conn.execute("""
                    INSERT INTO documents (
                        document_id, user_id, organization_id, filename, original_filename,
                        file_path, storage_backend, status, metadata, versions, tags,
                        permissions, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                """,
                document.document_id, document.user_id, document.organization_id,
                document.filename, document.original_filename, document.file_path,
                document.storage_backend.value, document.status.value,
                json.dumps(metadata_json), json.dumps(versions_json), document.tags, json.dumps(document.permissions),
                document.created_at, document.updated_at
                )

        except Exception as e:
            self.logger.error(f"Error saving document: {str(e)}")
            raise

    async def _process_document_async(self, document: Document, temp_path: str):
        """Process document asynchronously."""
        try:
            # Update status to processing
            await self._update_document_status(document.document_id, DocumentStatus.PROCESSING)

            # Additional processing can be added here:
            # - Full text indexing
            # - Advanced entity extraction
            # - Document similarity analysis
            # - Risk assessment
            # - Compliance checking

            # For now, just mark as processed
            await asyncio.sleep(1)  # Simulate processing time

            # Update status to processed
            await self._update_document_status(document.document_id, DocumentStatus.PROCESSED)

            self.logger.info(f"Document processing completed: {document.filename}")

        except Exception as e:
            # Update status to failed
            await self._update_document_status(document.document_id, DocumentStatus.FAILED, str(e))
            self.logger.error(f"Document processing failed: {str(e)}")

    async def _update_document_status(self, document_id: str, status: DocumentStatus, error_message: Optional[str] = None):
        """Update document status."""
        try:
            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE documents
                    SET status = $1, error_message = $2, last_processed_at = $3, updated_at = $3
                    WHERE document_id = $4
                """, status.value, error_message, datetime.utcnow(), document_id)

        except Exception as e:
            self.logger.error(f"Error updating document status: {str(e)}")

    async def get_document(self, document_id: str, user: User) -> Optional[Document]:
        """Get document by ID with access control."""
        try:
            async with self.pg_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    SELECT * FROM documents WHERE document_id = $1 AND deleted_at IS NULL
                """, document_id)

                if not result:
                    return None

                # Parse metadata from JSON
                metadata_dict = json.loads(result['metadata']) if isinstance(result['metadata'], str) else result['metadata']

                # Create document object
                metadata = DocumentMetadata(
                    document_id=result['document_id'],
                    filename=metadata_dict['filename'],
                    original_filename=metadata_dict['original_filename'],
                    file_size=metadata_dict['file_size'],
                    mime_type=metadata_dict['mime_type'],
                    document_type=DocumentType(metadata_dict['document_type'])
                )

                document = Document(
                    document_id=result['document_id'],
                    user_id=result['user_id'],
                    organization_id=result['organization_id'],
                    filename=result['filename'],
                    original_filename=result['original_filename'],
                    file_path=result['file_path'],
                    storage_backend=StorageBackend(result['storage_backend']),
                    status=DocumentStatus(result['status']),
                    metadata=metadata,
                    tags=result['tags'] or [],
                    permissions=result['permissions'] or {},
                    created_at=result['created_at'],
                    updated_at=result['updated_at']
                )

                # Check access
                if not document.can_access(user):
                    raise ValidationError("Access denied")

                return document

        except Exception as e:
            self.logger.error(f"Error getting document: {str(e)}")
            raise

    async def list_user_documents(self, user: User, limit: int = 50, offset: int = 0) -> List[Document]:
        """List documents for user."""
        try:
            async with self.pg_pool.acquire() as conn:
                query = """
                    SELECT * FROM documents
                    WHERE (user_id = $1 OR organization_id = $2) AND deleted_at IS NULL
                    ORDER BY created_at DESC
                    LIMIT $3 OFFSET $4
                """

                results = await conn.fetch(query, user.user_id, user.organization_id, limit, offset)

                documents = []
                for result in results:
                    # Parse metadata from JSON
                    metadata_dict = json.loads(result['metadata']) if isinstance(result['metadata'], str) else result['metadata']

                    metadata = DocumentMetadata(
                        document_id=result['document_id'],
                        filename=metadata_dict['filename'],
                        original_filename=metadata_dict['original_filename'],
                        file_size=metadata_dict['file_size'],
                        mime_type=metadata_dict['mime_type'],
                        document_type=DocumentType(metadata_dict['document_type'])
                    )

                    document = Document(
                        document_id=result['document_id'],
                        user_id=result['user_id'],
                        organization_id=result['organization_id'],
                        filename=result['filename'],
                        original_filename=result['original_filename'],
                        file_path=result['file_path'],
                        storage_backend=StorageBackend(result['storage_backend']),
                        status=DocumentStatus(result['status']),
                        metadata=metadata,
                        tags=result['tags'] or [],
                        permissions=result['permissions'] or {},
                        created_at=result['created_at'],
                        updated_at=result['updated_at']
                    )

                    documents.append(document)

                return documents

        except Exception as e:
            self.logger.error(f"Error listing documents: {str(e)}")
            raise

    async def delete_document(self, document_id: str, user: User):
        """Delete document with access control."""
        try:
            document = await self.get_document(document_id, user)

            if not document:
                raise ValidationError("Document not found")

            if not document.can_modify(user):
                raise ValidationError("Permission denied")

            # Soft delete
            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE documents
                    SET deleted_at = $1, updated_at = $1
                    WHERE document_id = $2
                """, datetime.utcnow(), document_id)

            # Log deletion
            await self._log_document_event(
                "document_deleted",
                document_id=document_id,
                user_id=user.user_id,
                details={"filename": document.filename}
            )

            self.logger.info(f"Document deleted: {document.filename}")

        except Exception as e:
            self.logger.error(f"Error deleting document: {str(e)}")
            raise

    async def _log_document_event(self, event_type: str, document_id: str, user_id: str, details: Optional[Dict[str, Any]] = None):
        """Log document-related event."""
        try:
            event = {
                'event_type': event_type,
                'document_id': document_id,
                'user_id': user_id,
                'details': details or {},
                'timestamp': datetime.utcnow()
            }

            await self.mongo_db.document_events.insert_one(event)

        except Exception as e:
            self.logger.error(f"Error logging document event: {str(e)}")

    async def get_user_storage_usage(self, user: User) -> Dict[str, Any]:
        """Get user's storage usage statistics."""
        try:
            async with self.pg_pool.acquire() as conn:
                # Individual usage
                result = await conn.fetchrow("""
                    SELECT
                        COUNT(*) as document_count,
                        COALESCE(SUM((metadata->>'file_size')::bigint), 0) as total_size_bytes
                    FROM documents
                    WHERE user_id = $1 AND deleted_at IS NULL
                """, user.user_id)

                individual_usage = {
                    'document_count': result['document_count'] or 0,
                    'total_size_bytes': result['total_size_bytes'] or 0,
                    'total_size_mb': (result['total_size_bytes'] or 0) / (1024 * 1024)
                }

                # Organization usage (if applicable)
                org_usage = {}
                if user.organization_id:
                    org_result = await conn.fetchrow("""
                        SELECT
                            COUNT(*) as document_count,
                            COALESCE(SUM((metadata->>'file_size')::bigint), 0) as total_size_bytes
                        FROM documents
                        WHERE organization_id = $1 AND deleted_at IS NULL
                    """, user.organization_id)

                    org_usage = {
                        'document_count': org_result['document_count'] or 0,
                        'total_size_bytes': org_result['total_size_bytes'] or 0,
                        'total_size_mb': (org_result['total_size_bytes'] or 0) / (1024 * 1024)
                    }

                return {
                    'individual': individual_usage,
                    'organization': org_usage,
                    'quotas': {
                        'individual_mb': self.user_quota_mb,
                        'organization_mb': self.organization_quota_mb
                    }
                }

        except Exception as e:
            self.logger.error(f"Error getting storage usage: {str(e)}")
            raise

    async def close(self):
        """Close all connections."""
        if self.pg_pool:
            await self.pg_pool.close()
        if self.mongo_client:
            self.mongo_client.close()

        self.logger.info("Document Upload Service connections closed")


# Convenience functions

async def upload_document(
    file_data: bytes,
    filename: str,
    user_id: str,
    config: Optional[Dict[str, Any]] = None
) -> Document:
    """Convenience function for document upload."""
    if config is None:
        config = {
            'postgres_host': 'localhost',
            'postgres_port': 54320,
            'postgres_db': 'ai_law_firm',
            'postgres_user': 'ai_law_user',
            'postgres_password': 'ai_law_password_2024',
            'mongo_host': 'localhost',
            'mongo_port': 27019,
            'storage': {
                'backend': 'local',
                'path': './storage/documents',
                'max_file_size_mb': 50
            }
        }

    service = DocumentUploadService(config)
    if await service.initialize():
        # Create mock user (in real implementation, get from auth service)
        from core.auth.user_management import User, UserRole
        user = User(
            user_id=user_id,
            email="<EMAIL>",
            username="user",
            first_name="User",
            last_name="Name",
            role=UserRole.USER
        )

        result = await service.upload_document(file_data, filename, user)
        await service.close()
        return result
    else:
        raise Exception("Failed to initialize document upload service")


# Export key classes and functions
__all__ = [
    "DocumentUploadService",
    "Document",
    "DocumentMetadata",
    "DocumentVersion",
    "DocumentType",
    "DocumentStatus",
    "StorageBackend",
    "upload_document"
]