#!/usr/bin/env python3
"""
Document Comparison Tool for AI Law Firm.

This module provides comprehensive document comparison capabilities:
- Text diffing with highlighting
- Metadata comparison
- AI-powered difference analysis
- Risk assessment of changes
- Export and reporting features
"""

import asyncio
import difflib
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import asyncpg
import redis
from motor.motor_asyncio import AsyncIOMotorClient

from utils.logging import get_logger
from infrastructure.ai_providers.base import AIProvider


class ComparisonType(Enum):
    """Types of document comparisons."""
    TEXT_DIFF = "text_diff"              # Line-by-line text comparison
    METADATA_COMPARISON = "metadata"     # Metadata field comparison
    STRUCTURAL_ANALYSIS = "structure"    # Document structure comparison
    RISK_ASSESSMENT = "risk"            # Risk analysis of changes
    COMPREHENSIVE = "comprehensive"     # All analysis types combined


class ChangeType(Enum):
    """Types of changes detected."""
    ADDITION = "addition"
    DELETION = "deletion"
    MODIFICATION = "modification"
    CONTEXT = "context"  # Unchanged context lines


@dataclass
class TextDiff:
    """Represents a text difference between documents."""
    line_number: int
    change_type: ChangeType
    old_text: str
    new_text: str
    context_before: List[str]
    context_after: List[str]


@dataclass
class MetadataComparison:
    """Comparison of document metadata."""
    field_name: str
    old_value: Any
    new_value: Any
    change_type: ChangeType
    significance: str  # high, medium, low


@dataclass
class RiskAssessment:
    """Risk assessment of document changes."""
    risk_level: str  # critical, high, medium, low
    risk_factors: List[str]
    recommendations: List[str]
    affected_clauses: List[str]


@dataclass
class ComparisonResult:
    """Complete comparison result."""
    document_id_1: str
    document_id_2: str
    comparison_type: ComparisonType
    text_diffs: List[TextDiff]
    metadata_changes: List[MetadataComparison]
    risk_assessment: Optional[RiskAssessment]
    summary: Dict[str, Any]
    generated_at: datetime
    processing_time_ms: float


class DocumentComparator:
    """
    Advanced document comparison tool for legal documents.

    Features:
    - Intelligent text diffing with legal context awareness
    - Metadata comparison and significance analysis
    - AI-powered risk assessment of changes
    - Comprehensive reporting and export
    - Performance monitoring and caching
    """

    def __init__(self, db_config: Dict[str, Any], ai_provider: Optional[AIProvider] = None):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.db_config = db_config
        self.ai_provider = ai_provider

        # Database connections
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None
        self.mongo_db = None

        # Comparison cache
        self.comparison_cache_ttl = 1800  # 30 minutes

    async def initialize(self):
        """Initialize database connections."""
        try:
            # PostgreSQL connection
            self.pg_pool = await asyncpg.create_pool(
                host=self.db_config.get('postgres_host', 'localhost'),
                port=self.db_config.get('postgres_port', 54320),
                database=self.db_config.get('postgres_db', 'ai_law_firm'),
                user=self.db_config.get('postgres_user', 'ai_law_user'),
                password=self.db_config.get('postgres_password', 'ai_law_password_2024'),
                min_size=1,
                max_size=10
            )

            # Redis connection
            self.redis_client = redis.Redis(
                host=self.db_config.get('redis_host', 'localhost'),
                port=self.db_config.get('redis_port', 63790),
                password=self.db_config.get('redis_password', 'ai_law_redis_password_2024'),
                decode_responses=True
            )

            # MongoDB connection
            mongo_uri = f"mongodb://{self.db_config.get('mongo_host', 'localhost')}:{self.db_config.get('mongo_port', 27019)}"
            self.mongo_client = AsyncIOMotorClient(mongo_uri)
            self.mongo_db = self.mongo_client.ai_law_firm

            self.logger.info("Document Comparator initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize comparator: {str(e)}")
            return False

    async def compare_documents(
        self,
        document_id_1: str,
        document_id_2: str,
        comparison_type: ComparisonType = ComparisonType.COMPREHENSIVE
    ) -> ComparisonResult:
        """
        Compare two documents comprehensively.

        Args:
            document_id_1: First document ID
            document_id_2: Second document ID
            comparison_type: Type of comparison to perform

        Returns:
            ComparisonResult with all comparison data
        """
        start_time = datetime.utcnow()

        try:
            # Check cache first
            cache_key = self._generate_comparison_cache_key(document_id_1, document_id_2, comparison_type)
            cached_result = self._get_cached_comparison(cache_key)
            if cached_result:
                self.logger.info(f"Comparison cache hit for {document_id_1} vs {document_id_2}")
                return cached_result

            # Fetch document data
            doc1_data = await self._fetch_document_data(document_id_1)
            doc2_data = await self._fetch_document_data(document_id_2)

            if not doc1_data or not doc2_data:
                raise ValueError("One or both documents not found")

            # Perform comparisons based on type
            text_diffs = []
            metadata_changes = []
            risk_assessment = None

            if comparison_type in [ComparisonType.TEXT_DIFF, ComparisonType.COMPREHENSIVE]:
                text_diffs = self._compare_text_content(doc1_data, doc2_data)

            if comparison_type in [ComparisonType.METADATA_COMPARISON, ComparisonType.COMPREHENSIVE]:
                metadata_changes = self._compare_metadata(doc1_data, doc2_data)

            if comparison_type in [ComparisonType.RISK_ASSESSMENT, ComparisonType.COMPREHENSIVE]:
                if self.ai_provider:
                    risk_assessment = await self._assess_risks(doc1_data, doc2_data, text_diffs, metadata_changes)

            # Generate summary
            summary = self._generate_comparison_summary(text_diffs, metadata_changes, risk_assessment)

            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            result = ComparisonResult(
                document_id_1=document_id_1,
                document_id_2=document_id_2,
                comparison_type=comparison_type,
                text_diffs=text_diffs,
                metadata_changes=metadata_changes,
                risk_assessment=risk_assessment,
                summary=summary,
                generated_at=datetime.utcnow(),
                processing_time_ms=processing_time
            )

            # Cache result
            self._cache_comparison(cache_key, result)

            # Update comparison analytics
            await self._update_comparison_analytics(result)

            return result

        except Exception as e:
            self.logger.error(f"Comparison error: {str(e)}")
            raise

    async def _fetch_document_data(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Fetch complete document data from all sources."""
        try:
            # Fetch from PostgreSQL (metadata)
            async with self.pg_pool.acquire() as conn:
                pg_data = await conn.fetchrow("""
                    SELECT
                        document_id, filename, content_type, file_size,
                        upload_timestamp, metadata
                    FROM documents
                    WHERE document_id = $1
                """, document_id)

            if not pg_data:
                return None

            # Parse metadata JSON
            metadata = pg_data['metadata']
            if isinstance(metadata, str):
                import json
                try:
                    metadata = json.loads(metadata)
                except:
                    metadata = {}

            # Fetch from MongoDB (content)
            try:
                mongo_data = await self.mongo_db.documents.find_one({"document_id": document_id})
                content = mongo_data.get('content', '') if mongo_data else ''
            except:
                content = ''

            return {
                'document_id': str(pg_data['document_id']),
                'filename': pg_data['filename'],
                'content_type': pg_data['content_type'],
                'file_size': pg_data['file_size'],
                'upload_timestamp': pg_data['upload_timestamp'],
                'metadata': metadata,
                'content': content
            }

        except Exception as e:
            self.logger.error(f"Error fetching document {document_id}: {str(e)}")
            return None

    def _compare_text_content(self, doc1: Dict[str, Any], doc2: Dict[str, Any]) -> List[TextDiff]:
        """Compare text content of two documents."""
        content1 = doc1.get('content', '').splitlines(keepends=True)
        content2 = doc2.get('content', '').splitlines(keepends=True)

        # Use difflib for intelligent diffing
        matcher = difflib.SequenceMatcher(None, content1, content2)
        diffs = []

        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                # Add some context lines
                context_lines = content1[i1:i2]
                for idx, line in enumerate(context_lines):
                    if len(diffs) < 10:  # Limit context
                        diffs.append(TextDiff(
                            line_number=i1 + idx + 1,
                            change_type=ChangeType.CONTEXT,
                            old_text=line.rstrip('\n'),
                            new_text=line.rstrip('\n'),
                            context_before=[],
                            context_after=[]
                        ))
            elif tag == 'delete':
                for idx, line in enumerate(content1[i1:i2]):
                    diffs.append(TextDiff(
                        line_number=i1 + idx + 1,
                        change_type=ChangeType.DELETION,
                        old_text=line.rstrip('\n'),
                        new_text='',
                        context_before=content1[max(0, i1-2):i1],
                        context_after=content1[i2:min(len(content1), i2+2)]
                    ))
            elif tag == 'insert':
                for idx, line in enumerate(content2[j1:j2]):
                    diffs.append(TextDiff(
                        line_number=j1 + idx + 1,
                        change_type=ChangeType.ADDITION,
                        old_text='',
                        new_text=line.rstrip('\n'),
                        context_before=content2[max(0, j1-2):j1],
                        context_after=content2[j2:min(len(content2), j2+2)]
                    ))
            elif tag == 'replace':
                old_lines = content1[i1:i2]
                new_lines = content2[j1:j2]
                max_lines = max(len(old_lines), len(new_lines))

                for idx in range(max_lines):
                    old_text = old_lines[idx].rstrip('\n') if idx < len(old_lines) else ''
                    new_text = new_lines[idx].rstrip('\n') if idx < len(new_lines) else ''

                    if old_text != new_text:
                        diffs.append(TextDiff(
                            line_number=i1 + idx + 1,
                            change_type=ChangeType.MODIFICATION,
                            old_text=old_text,
                            new_text=new_text,
                            context_before=content1[max(0, i1-2):i1],
                            context_after=content1[i2:min(len(content1), i2+2)]
                        ))

        return diffs[:50]  # Limit to first 50 diffs for performance

    def _compare_metadata(self, doc1: Dict[str, Any], doc2: Dict[str, Any]) -> List[MetadataComparison]:
        """Compare metadata between two documents."""
        metadata1 = doc1.get('metadata', {})
        metadata2 = doc2.get('metadata', {})

        changes = []
        all_keys = set(metadata1.keys()) | set(metadata2.keys())

        for key in all_keys:
            old_value = metadata1.get(key)
            new_value = metadata2.get(key)

            if old_value != new_value:
                # Determine change type
                if old_value is None:
                    change_type = ChangeType.ADDITION
                elif new_value is None:
                    change_type = ChangeType.DELETION
                else:
                    change_type = ChangeType.MODIFICATION

                # Assess significance
                significance = self._assess_metadata_significance(key, old_value, new_value)

                changes.append(MetadataComparison(
                    field_name=key,
                    old_value=old_value,
                    new_value=new_value,
                    change_type=change_type,
                    significance=significance
                ))

        return changes

    def _assess_metadata_significance(self, field: str, old_value: Any, new_value: Any) -> str:
        """Assess the significance of a metadata change."""
        # High significance fields
        high_significance = [
            'document_type', 'parties', 'effective_date', 'termination_date',
            'governing_law', 'jurisdiction', 'compensation', 'confidentiality'
        ]

        if field in high_significance:
            return 'high'

        # Medium significance fields
        medium_significance = [
            'filename', 'upload_timestamp', 'file_size', 'analysis_type'
        ]

        if field in medium_significance:
            return 'medium'

        return 'low'

    async def _assess_risks(
        self,
        doc1: Dict[str, Any],
        doc2: Dict[str, Any],
        text_diffs: List[TextDiff],
        metadata_changes: List[MetadataComparison]
    ) -> RiskAssessment:
        """Assess risks of document changes using AI."""
        if not self.ai_provider:
            return RiskAssessment(
                risk_level="unknown",
                risk_factors=["AI provider not available for risk assessment"],
                recommendations=["Configure AI provider for risk analysis"],
                affected_clauses=[]
            )

        try:
            # Prepare analysis prompt
            prompt = self._build_risk_assessment_prompt(doc1, doc2, text_diffs, metadata_changes)

            # Get AI analysis
            response = await self.ai_provider.generate_response(
                prompt=prompt,
                temperature=0.1,
                max_tokens=1000
            )

            # Parse AI response
            analysis = self._parse_risk_assessment_response(response.content)

            return RiskAssessment(
                risk_level=analysis.get('risk_level', 'medium'),
                risk_factors=analysis.get('risk_factors', []),
                recommendations=analysis.get('recommendations', []),
                affected_clauses=analysis.get('affected_clauses', [])
            )

        except Exception as e:
            self.logger.error(f"Risk assessment error: {str(e)}")
            return RiskAssessment(
                risk_level="error",
                risk_factors=[f"Risk assessment failed: {str(e)}"],
                recommendations=["Manual review recommended"],
                affected_clauses=[]
            )

    def _build_risk_assessment_prompt(
        self,
        doc1: Dict[str, Any],
        doc2: Dict[str, Any],
        text_diffs: List[TextDiff],
        metadata_changes: List[MetadataComparison]
    ) -> str:
        """Build prompt for AI risk assessment."""
        prompt = f"""
        Analyze the changes between two legal documents and assess the legal risks.

        DOCUMENT 1: {doc1.get('filename', 'Unknown')}
        Type: {doc1.get('metadata', {}).get('document_type', 'Unknown')}

        DOCUMENT 2: {doc2.get('filename', 'Unknown')}
        Type: {doc2.get('metadata', {}).get('document_type', 'Unknown')}

        METADATA CHANGES:
        """

        for change in metadata_changes:
            prompt += f"- {change.field_name}: '{change.old_value}' → '{change.new_value}' (Significance: {change.significance})\n"

        prompt += "\nTEXT CHANGES:\n"
        for diff in text_diffs[:10]:  # Limit for token efficiency
            if diff.change_type != ChangeType.CONTEXT:
                prompt += f"- {diff.change_type.value.upper()}: '{diff.old_text}' → '{diff.new_text}'\n"

        prompt += """

        Based on this analysis, provide a JSON response with:
        {
            "risk_level": "critical|high|medium|low",
            "risk_factors": ["List specific legal risks identified"],
            "recommendations": ["Specific recommendations to mitigate risks"],
            "affected_clauses": ["Which contract clauses or legal provisions are affected"]
        }

        Focus on legal implications, compliance issues, and potential liabilities.
        """

        return prompt

    def _parse_risk_assessment_response(self, response: str) -> Dict[str, Any]:
        """Parse AI risk assessment response."""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Fallback parsing
                return {
                    "risk_level": "medium",
                    "risk_factors": ["Unable to parse AI response"],
                    "recommendations": ["Manual review recommended"],
                    "affected_clauses": []
                }
        except:
            return {
                "risk_level": "medium",
                "risk_factors": ["AI response parsing failed"],
                "recommendations": ["Manual review recommended"],
                "affected_clauses": []
            }

    def _generate_comparison_summary(
        self,
        text_diffs: List[TextDiff],
        metadata_changes: List[MetadataComparison],
        risk_assessment: Optional[RiskAssessment]
    ) -> Dict[str, Any]:
        """Generate summary statistics for the comparison."""
        # Count different types of changes
        additions = sum(1 for d in text_diffs if d.change_type == ChangeType.ADDITION)
        deletions = sum(1 for d in text_diffs if d.change_type == ChangeType.DELETION)
        modifications = sum(1 for d in text_diffs if d.change_type == ChangeType.MODIFICATION)

        # Count metadata changes by significance
        high_changes = sum(1 for c in metadata_changes if c.significance == 'high')
        medium_changes = sum(1 for c in metadata_changes if c.significance == 'medium')
        low_changes = sum(1 for c in metadata_changes if c.significance == 'low')

        return {
            "text_changes": {
                "total": len(text_diffs),
                "additions": additions,
                "deletions": deletions,
                "modifications": modifications
            },
            "metadata_changes": {
                "total": len(metadata_changes),
                "high_significance": high_changes,
                "medium_significance": medium_changes,
                "low_significance": low_changes
            },
            "risk_level": risk_assessment.risk_level if risk_assessment else "unknown",
            "change_intensity": self._calculate_change_intensity(text_diffs, metadata_changes)
        }

    def _calculate_change_intensity(
        self,
        text_diffs: List[TextDiff],
        metadata_changes: List[MetadataComparison]
    ) -> str:
        """Calculate overall change intensity."""
        text_change_count = len(text_diffs)
        high_metadata_changes = sum(1 for c in metadata_changes if c.significance == 'high')

        if text_change_count > 50 or high_metadata_changes > 3:
            return "high"
        elif text_change_count > 20 or high_metadata_changes > 1:
            return "medium"
        elif text_change_count > 0 or high_metadata_changes > 0:
            return "low"
        else:
            return "none"

    def _generate_comparison_cache_key(
        self,
        doc_id_1: str,
        doc_id_2: str,
        comparison_type: ComparisonType
    ) -> str:
        """Generate cache key for comparison."""
        return f"comparison:{doc_id_1}:{doc_id_2}:{comparison_type.value}"

    def _get_cached_comparison(self, cache_key: str) -> Optional[ComparisonResult]:
        """Get cached comparison result."""
        try:
            cached = self.redis_client.get(cache_key)
            if cached:
                # Would need proper deserialization in production
                return None
        except:
            pass
        return None

    def _cache_comparison(self, cache_key: str, result: ComparisonResult):
        """Cache comparison result."""
        try:
            # Simplified caching
            self.redis_client.setex(cache_key, self.comparison_cache_ttl, "cached")
        except:
            pass

    async def _update_comparison_analytics(self, result: ComparisonResult):
        """Update comparison analytics."""
        try:
            # Update comparison count
            self.redis_client.incr("comparisons:total")

            # Update comparison types
            self.redis_client.incr(f"comparisons:type:{result.comparison_type.value}")

            # Update processing times (keep last 100)
            self.redis_client.lpush("comparisons:processing_times", result.processing_time_ms)
            self.redis_client.ltrim("comparisons:processing_times", 0, 99)

        except Exception as e:
            self.logger.error(f"Error updating comparison analytics: {str(e)}")

    async def close(self):
        """Close all database connections."""
        if self.pg_pool:
            await self.pg_pool.close()
        if self.redis_client:
            self.redis_client.close()
        if self.mongo_client:
            self.mongo_client.close()

        self.logger.info("Document Comparator connections closed")


# Convenience functions

async def compare_documents(
    document_id_1: str,
    document_id_2: str,
    comparison_type: ComparisonType = ComparisonType.COMPREHENSIVE,
    db_config: Dict[str, Any] = None,
    ai_provider: Optional[AIProvider] = None
) -> ComparisonResult:
    """Convenience function for document comparison."""
    if db_config is None:
        db_config = {
            'postgres_host': 'localhost',
            'postgres_port': 54320,
            'postgres_db': 'ai_law_firm',
            'postgres_user': 'ai_law_user',
            'postgres_password': 'ai_law_password_2024',
            'redis_host': 'localhost',
            'redis_port': 63790,
            'redis_password': 'ai_law_redis_password_2024',
            'mongo_host': 'localhost',
            'mongo_port': 27019
        }

    comparator = DocumentComparator(db_config, ai_provider)
    if await comparator.initialize():
        result = await comparator.compare_documents(document_id_1, document_id_2, comparison_type)
        await comparator.close()
        return result
    else:
        raise Exception("Failed to initialize document comparator")


# Export key classes and functions
__all__ = [
    "DocumentComparator",
    "ComparisonResult",
    "ComparisonType",
    "ChangeType",
    "TextDiff",
    "MetadataComparison",
    "RiskAssessment",
    "compare_documents"
]