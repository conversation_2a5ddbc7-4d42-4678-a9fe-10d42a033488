"""
API Key Management Service for AI Law Firm.

This service handles secure storage, retrieval, and management of API keys
in the PostgreSQL database with encryption.
"""

import os
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from infrastructure.databases.postgres_repository import PostgresRepository
from infrastructure.databases.mongodb_repository import MongoDBRepository
from utils.logging import get_logger
from utils.exceptions import DatabaseError


class APIKeyService:
    """
    Service for managing API keys with encryption and database persistence.
    """

    def __init__(self, postgres_repo: PostgresRepository, mongodb_repo: Optional[MongoDBRepository] = None):
        self.postgres_repo = postgres_repo
        self.mongodb_repo = mongodb_repo
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self._encryption_key = self._get_or_create_encryption_key()

    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for API keys."""
        # Use a consistent salt for the application
        salt = b'ai_law_firm_salt_2024'

        # Use the database password as the base for key derivation
        password = os.getenv('POSTGRES_PASSWORD', 'ai_law_password_2024').encode()

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )

        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key

    def _encrypt_key(self, api_key: str) -> str:
        """Encrypt an API key."""
        f = Fernet(self._encryption_key)
        encrypted = f.encrypt(api_key.encode())
        return encrypted.decode()

    def _decrypt_key(self, encrypted_key: str) -> str:
        """Decrypt an API key."""
        f = Fernet(self._encryption_key)
        decrypted = f.decrypt(encrypted_key.encode())
        return decrypted.decode()

    async def store_api_key(self, provider: str, key_name: str, api_key: str, user_id: Optional[str] = None) -> bool:
        """Store an API key in both PostgreSQL and MongoDB databases."""
        try:
            # Debug: Check if postgres repo is initialized
            if not hasattr(self.postgres_repo, '_pool') or self.postgres_repo._pool is None:
                self.logger.error("PostgreSQL repository pool not initialized during store")
                raise DatabaseError("Database pool not initialized")

            encrypted_key = self._encrypt_key(api_key)
            key_id = str(uuid.uuid4())
            now = datetime.utcnow()

            # Store in PostgreSQL
            existing = await self.get_api_key(provider, key_name, user_id)
            if existing:
                # Update existing key
                query = """
                    UPDATE api_keys
                    SET encrypted_key = $1, updated_at = NOW()
                    WHERE provider = $2 AND key_name = $3 AND (user_id = $4 OR (user_id IS NULL AND $4 IS NULL))
                """
                await self.postgres_repo.execute_query(query, encrypted_key, provider, key_name, user_id)
                self.logger.info(f"Updated API key for provider {provider}, key {key_name}")
            else:
                # Insert new key
                query = """
                    INSERT INTO api_keys (key_id, user_id, provider, key_name, encrypted_key, is_active, created_at)
                    VALUES ($1, $2, $3, $4, $5, true, NOW())
                """
                await self.postgres_repo.execute_query(query, key_id, user_id, provider, key_name, encrypted_key)
                self.logger.info(f"Stored new API key for provider {provider}, key {key_name}")

            # Store in MongoDB if available
            if self.mongodb_repo and self.mongodb_repo.database is not None:
                try:
                    api_keys_collection = self.mongodb_repo.database.api_keys

                    # Prepare MongoDB document
                    mongo_doc = {
                        "_id": f"{provider}_{key_name}_{user_id or 'system'}",
                        "key_id": key_id,
                        "user_id": user_id,
                        "provider": provider,
                        "key_name": key_name,
                        "encrypted_key": encrypted_key,
                        "is_active": True,
                        "created_at": now,
                        "updated_at": now,
                        "usage_count": 0
                    }

                    # Upsert the document
                    await api_keys_collection.replace_one(
                        {"_id": mongo_doc["_id"]},
                        mongo_doc,
                        upsert=True
                    )

                    self.logger.info(f"Stored API key in MongoDB for provider {provider}, key {key_name}")
                except Exception as mongo_error:
                    self.logger.warning(f"Failed to store API key in MongoDB: {mongo_error}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to store API key for {provider}: {e}")
            return False

    async def get_api_key(self, provider: str, key_name: str, user_id: Optional[str] = None) -> Optional[str]:
        """Retrieve an API key from PostgreSQL, with MongoDB fallback."""
        try:
            # Debug: Check if postgres repo is initialized
            if not hasattr(self.postgres_repo, '_pool') or self.postgres_repo._pool is None:
                self.logger.error("PostgreSQL repository pool not initialized")
                raise DatabaseError("Database pool not initialized")

            # Try PostgreSQL first
            query = """
                SELECT encrypted_key FROM api_keys
                WHERE provider = $1 AND key_name = $2 AND is_active = true
                AND (user_id = $3 OR (user_id IS NULL AND $3 IS NULL))
                ORDER BY created_at DESC
                LIMIT 1
            """
            result = await self.postgres_repo.fetch_one(query, provider, key_name, user_id)

            if result:
                encrypted_key = result['encrypted_key']
                decrypted_key = self._decrypt_key(encrypted_key)

                # Update last_used timestamp
                update_query = """
                    UPDATE api_keys
                    SET last_used = NOW(), usage_count = usage_count + 1
                    WHERE provider = $1 AND key_name = $2 AND is_active = true
                    AND (user_id = $3 OR (user_id IS NULL AND $3 IS NULL))
                """
                await self.postgres_repo.execute_query(update_query, provider, key_name, user_id)

                return decrypted_key

            # Fallback to MongoDB if available
            if self.mongodb_repo and self.mongodb_repo.database is not None:
                try:
                    api_keys_collection = self.mongodb_repo.database.api_keys
                    doc_id = f"{provider}_{key_name}_{user_id or 'system'}"

                    doc = await api_keys_collection.find_one({"_id": doc_id, "is_active": True})
                    if doc:
                        encrypted_key = doc['encrypted_key']
                        decrypted_key = self._decrypt_key(encrypted_key)

                        # Update usage in MongoDB
                        await api_keys_collection.update_one(
                            {"_id": doc_id},
                            {"$inc": {"usage_count": 1}, "$set": {"last_used": datetime.utcnow()}}
                        )

                        self.logger.info(f"Retrieved API key from MongoDB for provider {provider}, key {key_name}")
                        return decrypted_key
                except Exception as mongo_error:
                    self.logger.warning(f"Failed to retrieve API key from MongoDB: {mongo_error}")

            return None

        except Exception as e:
            self.logger.error(f"Failed to retrieve API key for {provider}: {e}")
            return None

    async def get_all_api_keys(self, user_id: Optional[str] = None) -> Dict[str, Dict[str, str]]:
        """Get all API keys for a user or system-wide."""
        try:
            query = """
                SELECT provider, key_name, encrypted_key, created_at, last_used, usage_count
                FROM api_keys
                WHERE is_active = true
                AND (user_id = $1 OR (user_id IS NULL AND $1 IS NULL))
                ORDER BY provider, key_name
            """
            results = await self.postgres_repo.fetch_all(query, user_id)

            keys = {}
            for row in results:
                provider = row['provider']
                key_name = row['key_name']

                if provider not in keys:
                    keys[provider] = {}

                keys[provider][key_name] = {
                    'encrypted_key': row['encrypted_key'],
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                    'last_used': row['last_used'].isoformat() if row['last_used'] else None,
                    'usage_count': row['usage_count']
                }

            return keys

        except Exception as e:
            self.logger.error(f"Failed to retrieve all API keys: {e}")
            return {}

    async def delete_api_key(self, provider: str, key_name: str, user_id: Optional[str] = None) -> bool:
        """Delete an API key from the database."""
        try:
            query = """
                UPDATE api_keys
                SET is_active = false
                WHERE provider = $1 AND key_name = $2
                AND (user_id = $3 OR (user_id IS NULL AND $3 IS NULL))
            """
            await self.postgres_repo.execute_query(query, provider, key_name, user_id)
            self.logger.info(f"Deleted API key for provider {provider}, key {key_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete API key for {provider}: {e}")
            return False

    async def get_api_key_stats(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics about API key usage."""
        try:
            query = """
                SELECT
                    provider,
                    COUNT(*) as key_count,
                    SUM(usage_count) as total_usage,
                    MAX(last_used) as last_used
                FROM api_keys
                WHERE is_active = true
                AND (user_id = $1 OR (user_id IS NULL AND $1 IS NULL))
                GROUP BY provider
                ORDER BY provider
            """
            results = await self.postgres_repo.fetch_all(query, user_id)

            stats = {
                'total_keys': 0,
                'total_usage': 0,
                'providers': {}
            }

            for row in results:
                provider = row['provider']
                stats['providers'][provider] = {
                    'key_count': row['key_count'],
                    'total_usage': row['total_usage'],
                    'last_used': row['last_used'].isoformat() if row['last_used'] else None
                }
                stats['total_keys'] += row['key_count']
                stats['total_usage'] += row['total_usage']

            return stats

        except Exception as e:
            self.logger.error(f"Failed to get API key stats: {e}")
            return {'total_keys': 0, 'total_usage': 0, 'providers': {}}


# Global instance
_api_key_service: Optional[APIKeyService] = None


def get_api_key_service() -> APIKeyService:
    """Get the global API key service instance."""
    global _api_key_service
    if _api_key_service is None:
        # This will be initialized during app startup
        raise RuntimeError("API Key Service not initialized")
    return _api_key_service


def init_api_key_service(postgres_repo: PostgresRepository, mongodb_repo: Optional[MongoDBRepository] = None) -> APIKeyService:
    """Initialize the global API key service instance."""
    global _api_key_service
    _api_key_service = APIKeyService(postgres_repo, mongodb_repo)
    return _api_key_service