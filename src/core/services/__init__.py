"""
Services layer for AI Law Firm.

This module provides orchestration services that coordinate between
different components of the system, implementing business logic workflows.
"""

from .orchestration_service import OrchestrationService
# from .document_service import DocumentService  # TODO: Implement DocumentService
# from .analysis_service import AnalysisService  # TODO: Implement AnalysisService
# from .agent_service import AgentService       # TODO: Implement AgentService

__all__ = [
    "OrchestrationService",
    # "DocumentService",  # TODO: Implement DocumentService
    # "AnalysisService",  # TODO: Implement AnalysisService
    # "AgentService",     # TODO: Implement AgentService
]