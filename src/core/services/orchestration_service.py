"""
Orchestration Service for AI Law Firm.

This service coordinates the entire document analysis workflow,
managing the interaction between document processing, agent management,
and analysis execution.
"""

import asyncio
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

from core.config.settings import AppConfig
from core.document.processor import DocumentProcessor
from core.document.types import DocumentProcessingResult, DocumentProcessingRequest
from core.agents.base import Agent, AgentResponse
from core.analysis.pipeline_orchestrator import PipelineOrchestrator
from infrastructure.ai_providers.base import AIProvider
from core.knowledge.base import KnowledgeBase
from utils.logging import get_logger
from utils.exceptions import AILawFirmError


@dataclass
class AnalysisRequest:
    """Request for document analysis."""
    document_content: str
    analysis_type: str
    document_type: Optional[str] = None
    custom_query: Optional[str] = None
    priority: int = 2
    context_documents: Optional[List[str]] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    model_preference: Optional[str] = None


@dataclass
class AnalysisResult:
    """Result of document analysis."""
    request: AnalysisRequest
    responses: List[AgentResponse]
    processing_time: float
    cost_estimate: float
    timestamp: datetime = None
    success: bool = True
    error_message: Optional[str] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class OrchestrationService:
    """
    Main orchestration service that coordinates the entire analysis workflow.

    Features:
    - Document processing orchestration
    - Agent management and routing
    - Analysis pipeline execution
    - Cost and performance tracking
    - Error handling and recovery
    """

    def __init__(self, config: AppConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Initialize components
        self.document_processor = DocumentProcessor(config.document)
        self.pipeline_orchestrator = PipelineOrchestrator(config)

        # Service state
        self.is_initialized = False
        self.active_requests: Dict[str, AnalysisRequest] = {}
        self.completed_analyses: List[AnalysisResult] = []

        # Performance tracking
        self.total_analyses = 0
        self.total_processing_time = 0.0
        self.total_cost = 0.0

    async def initialize(self) -> bool:
        """Initialize the orchestration service."""
        try:
            self.logger.info("Initializing Orchestration Service...")

            # Pipeline orchestrator is already initialized in __init__
            # No additional initialization needed

            self.is_initialized = True
            self.logger.info("Orchestration Service initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Orchestration Service initialization failed: {str(e)}")
            return False

    async def shutdown(self) -> bool:
        """Shutdown the orchestration service."""
        try:
            self.logger.info("Shutting down Orchestration Service...")

            # Clear active requests
            self.active_requests.clear()

            # Shutdown pipeline orchestrator
            await self.pipeline_orchestrator.shutdown()

            self.is_initialized = False
            self.logger.info("Orchestration Service shutdown complete")
            return True

        except Exception as e:
            self.logger.error(f"Orchestration Service shutdown failed: {str(e)}")
            return False

    async def analyze_document(self, request: AnalysisRequest) -> AnalysisResult:
        """
        Perform complete document analysis using the orchestrated workflow.

        Args:
            request: Analysis request with document and parameters

        Returns:
            AnalysisResult: Complete analysis result
        """
        if not self.is_initialized:
            raise AILawFirmError("Orchestration Service not initialized")

        start_time = asyncio.get_event_loop().time()
        request_id = f"analysis_{int(start_time * 1000)}"

        try:
            self.logger.info(f"Starting analysis {request_id} for {request.analysis_type}")

            # Register active request
            self.active_requests[request_id] = request

            # Step 1: Process document if needed
            processed_content = request.document_content
            if self._requires_preprocessing(request.document_type):
                processed_content = await self._preprocess_document(request)

            # Step 2: Execute analysis pipeline
            pipeline_result = await self.pipeline_orchestrator.analyze_document(
                document_content=processed_content,
                analysis_type=request.analysis_type,
                context=request.context_documents,
                priority=request.priority,
                model_preference=getattr(request, 'model_preference', None)
            )

            # Step 3: Extract responses from pipeline result
            responses = self._extract_responses_from_pipeline(pipeline_result)

            # Step 4: Calculate cost and timing
            processing_time = asyncio.get_event_loop().time() - start_time
            cost_estimate = self._calculate_cost_estimate(responses)

            # Step 5: Create result
            result = AnalysisResult(
                request=request,
                responses=responses,
                processing_time=processing_time,
                cost_estimate=cost_estimate,
                success=True
            )

            # Step 6: Update metrics
            self._update_metrics(result)

            # Step 7: Store completed analysis
            self.completed_analyses.append(result)

            self.logger.info(f"Analysis {request_id} completed successfully in {processing_time:.2f}s")
            return result

        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time

            error_result = AnalysisResult(
                request=request,
                responses=[],
                processing_time=processing_time,
                cost_estimate=0.0,
                success=False,
                error_message=str(e)
            )

            self.logger.error(f"Analysis {request_id} failed: {str(e)}")
            return error_result

        finally:
            # Clean up active request
            if request_id in self.active_requests:
                del self.active_requests[request_id]

    async def _preprocess_document(self, request: AnalysisRequest) -> str:
        """Preprocess document if needed."""
        # For now, return content as-is
        # In the future, this could include:
        # - Document format conversion
        # - Content extraction
        # - Preprocessing for specific document types
        return request.document_content

    def _requires_preprocessing(self, document_type: Optional[str]) -> bool:
        """Determine if document requires preprocessing."""
        if not document_type:
            return False

        # Define document types that require preprocessing
        preprocess_types = ['pdf', 'docx', 'html']
        return document_type.lower() in preprocess_types

    def _extract_responses_from_pipeline(self, pipeline_result: Dict[str, Any]) -> List[AgentResponse]:
        """Extract agent responses from pipeline result."""
        responses = []

        # Extract responses from different pipeline stages
        for stage_key, stage_result in pipeline_result.get('stage_results', {}).items():
            if stage_result and 'response' in stage_result:
                response = stage_result['response']
                # Ensure it has the required attributes
                if hasattr(response, 'content'):
                    responses.append(response)

        # If no responses found in stages, try to get from combined result
        if not responses and 'combined_analysis' in pipeline_result:
            # Create a synthetic response from combined analysis
            from core.agents.base import AgentResponse
            synthetic_response = AgentResponse(
                content=pipeline_result['combined_analysis'],
                confidence_score=0.85,
                metadata={"source": "pipeline_synthesis"},
                sources=[],
                processing_time=pipeline_result.get('pipeline_metadata', {}).get('total_processing_time', 0.0)
            )
            responses.append(synthetic_response)

        return responses

    def _calculate_cost_estimate(self, responses: List[AgentResponse]) -> float:
        """Calculate total cost estimate from responses."""
        total_cost = 0.0

        for response in responses:
            if hasattr(response, 'token_usage') and response.token_usage:
                cost = getattr(response.token_usage, 'estimated_cost', 0.0)
                total_cost += cost

        return total_cost

    def _update_metrics(self, result: AnalysisResult):
        """Update service metrics."""
        self.total_analyses += 1
        self.total_processing_time += result.processing_time
        self.total_cost += result.cost_estimate

    def get_service_status(self) -> Dict[str, Any]:
        """Get current service status and metrics."""
        avg_processing_time = (
            self.total_processing_time / self.total_analyses
            if self.total_analyses > 0 else 0.0
        )

        return {
            "is_initialized": self.is_initialized,
            "active_requests": len(self.active_requests),
            "completed_analyses": len(self.completed_analyses),
            "total_analyses": self.total_analyses,
            "average_processing_time": avg_processing_time,
            "total_cost": self.total_cost,
            "pipeline_status": self.pipeline_orchestrator.get_pipeline_status() if hasattr(self.pipeline_orchestrator, 'get_pipeline_status') else {}
        }

    def get_recent_analyses(self, limit: int = 10) -> List[AnalysisResult]:
        """Get recent analysis results."""
        return self.completed_analyses[-limit:] if self.completed_analyses else []

    def clear_analysis_history(self):
        """Clear completed analysis history."""
        self.completed_analyses.clear()
        self.total_analyses = 0
        self.total_processing_time = 0.0
        self.total_cost = 0.0

    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        try:
            health_status = {
                "service": "orchestration",
                "healthy": True,
                "components": {},
                "issues": []
            }

            # Check pipeline orchestrator
            if hasattr(self.pipeline_orchestrator, 'health_check'):
                pipeline_health = await self.pipeline_orchestrator.health_check()
                health_status["components"]["pipeline_orchestrator"] = pipeline_health

                if not pipeline_health.get("healthy", False):
                    health_status["issues"].append("Pipeline orchestrator unhealthy")
                    health_status["healthy"] = False

            # Check document processor
            if hasattr(self.document_processor, 'health_check'):
                processor_health = await self.document_processor.health_check()
                health_status["components"]["document_processor"] = processor_health

                if not processor_health.get("healthy", False):
                    health_status["issues"].append("Document processor unhealthy")
                    health_status["healthy"] = False

            # Overall assessment
            if not self.is_initialized:
                health_status["healthy"] = False
                health_status["issues"].append("Service not initialized")

            if self.active_requests:
                health_status["issues"].append(f"{len(self.active_requests)} active requests")

            return health_status

        except Exception as e:
            return {
                "service": "orchestration",
                "healthy": False,
                "error": str(e)
            }

    def get_supported_analysis_types(self) -> List[Dict[str, Any]]:
        """Get list of supported analysis types."""
        return [
            {
                "type": "contract_review",
                "name": "Contract Review",
                "description": "Comprehensive contract analysis and risk assessment",
                "estimated_time": "3-7 minutes",
                "cost_range": "Medium"
            },
            {
                "type": "legal_research",
                "name": "Legal Research",
                "description": "Research relevant cases and legal precedents",
                "estimated_time": "5-10 minutes",
                "cost_range": "High"
            },
            {
                "type": "risk_assessment",
                "name": "Risk Assessment",
                "description": "Analyze potential legal risks and liabilities",
                "estimated_time": "4-8 minutes",
                "cost_range": "High"
            },
            {
                "type": "compliance_check",
                "name": "Compliance Check",
                "description": "Check document compliance with regulations",
                "estimated_time": "3-6 minutes",
                "cost_range": "Medium"
            },
            {
                "type": "custom_query",
                "name": "Custom Analysis",
                "description": "Custom analysis with flexible parameters",
                "estimated_time": "Variable",
                "cost_range": "Variable"
            }
        ]

    async def process_query(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        document_ids: Optional[List[str]] = None
    ) -> AgentResponse:
        """
        Process a general query using the AI provider.

        Args:
            query: The query to process
            context: Additional context information
            document_ids: Related document IDs

        Returns:
            AgentResponse: Response from the AI provider
        """
        try:
            # Get the AI provider from pipeline orchestrator
            # Use the first available provider (typically OpenAI)
            if not self.pipeline_orchestrator.providers:
                raise AILawFirmError("No AI providers available")

            # Prefer OpenAI if available, otherwise use the first available provider
            ai_provider = None
            if "openai" in self.pipeline_orchestrator.providers:
                ai_provider = self.pipeline_orchestrator.providers["openai"]
            else:
                ai_provider = next(iter(self.pipeline_orchestrator.providers.values()))

            if not ai_provider:
                raise AILawFirmError("No AI provider available")

            # Prepare context documents if provided
            context_docs = []
            if document_ids:
                # In a real implementation, you'd fetch document content here
                # For now, we'll just pass the IDs as context
                context_docs = document_ids

            # Create analysis request for the query
            analysis_request = AnalysisRequest(
                document_content=query,
                analysis_type="custom_query",
                custom_query=query,
                context_documents=context_docs,
                user_id=context.get("user_id") if context else None,
                session_id=context.get("session_id") if context else None
            )

            # Process through pipeline
            result = await self.analyze_document(analysis_request)

            if result.responses:
                # Return the first response
                response = result.responses[0]
                # Update metadata with query-specific info
                if hasattr(response, 'metadata'):
                    response.metadata.update({
                        "query_type": "general_query",
                        "context_provided": bool(context_docs),
                        "processing_time": result.processing_time
                    })
                return response
            else:
                # Create a fallback response
                from core.agents.base import AgentResponse
                return AgentResponse(
                    content="I apologize, but I was unable to process your query at this time. Please try again.",
                    confidence_score=0.0,
                    metadata={"error": "No response generated"},
                    sources=[],
                    processing_time=result.processing_time
                )

        except Exception as e:
            self.logger.error(f"Query processing failed: {str(e)}")
            # Return error response
            from core.agents.base import AgentResponse
            return AgentResponse(
                content="I apologize, but I encountered an error while processing your request. Please try again or contact support if the issue persists.",
                confidence_score=0.0,
                metadata={"error": str(e)},
                sources=[],
                processing_time=0.0
            )

    def get_analysis_history_summary(self) -> Dict[str, Any]:
        """Get summary of analysis history."""
        if not self.completed_analyses:
            return {"total_analyses": 0, "analysis_types": {}, "performance": {}}

        # Count analysis types
        analysis_types = {}
        total_time = 0.0
        total_cost = 0.0

        for analysis in self.completed_analyses:
            analysis_type = analysis.request.analysis_type
            analysis_types[analysis_type] = analysis_types.get(analysis_type, 0) + 1
            total_time += analysis.processing_time
            total_cost += analysis.cost_estimate

        avg_time = total_time / len(self.completed_analyses) if self.completed_analyses else 0.0

        return {
            "total_analyses": len(self.completed_analyses),
            "analysis_types": analysis_types,
            "performance": {
                "average_time": avg_time,
                "total_cost": total_cost,
                "success_rate": len([a for a in self.completed_analyses if a.success]) / len(self.completed_analyses)
            }
        }