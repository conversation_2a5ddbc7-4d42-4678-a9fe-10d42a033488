"""
Data Persistence Service for AI Law Firm.

This service provides a unified interface for all data persistence operations
across PostgreSQL, MongoDB, Redis, and Qdrant, implementing comprehensive
data management for documents, analyses, users, and system interactions.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, date
from pathlib import Path

from core.config.settings import AppConfig
from infrastructure.databases.postgres_repository import PostgreSQLRepository
from infrastructure.databases.mongodb_repository import MongoDBRepository
from infrastructure.databases.redis_cache import RedisCache
from infrastructure.databases.data_models import (
    User, UserSession, APIKey, Document, DocumentContent,
    AnalysisSession, AnalysisResult, AnalysisResultContent,
    UsageStats, AuditLog, DocumentType, AnalysisType,
    ProcessingStatus, AnalysisStatus
)
from utils.logging import get_logger
from utils.exceptions import DatabaseError


class DataPersistenceService:
    """
    Unified data persistence service for the AI Law Firm.

    This service orchestrates data operations across multiple databases:
    - PostgreSQL: Relational data (users, sessions, metadata)
    - MongoDB: Unstructured data (documents, analysis results)
    - Redis: Caching and session management
    - Qdrant: Vector storage (via existing integration)

    Features:
    - Transaction management across databases
    - Data consistency and integrity
    - Comprehensive audit logging
    - Performance monitoring
    - Backup and recovery support
    """

    def __init__(self, config: AppConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Initialize repositories
        self.postgres = PostgreSQLRepository(self._get_postgres_connection_string())
        self.mongodb = MongoDBRepository(
            self._get_mongodb_connection_string(),
            "ai_law_firm"
        )
        self.redis = RedisCache(self._get_redis_connection_string())

        # Service state
        self.is_initialized = False
        self.repositories = [self.postgres, self.mongodb, self.redis]

    def _get_postgres_connection_string(self) -> str:
        """Get PostgreSQL connection string from config."""
        # This would be configured in the AppConfig
        # For now, using default Docker values
        return (
            f"postgresql://ai_law_user:ai_law_password_2024@"
            f"localhost:54320/ai_law_firm"
        )

    def _get_mongodb_connection_string(self) -> str:
        """Get MongoDB connection string from config."""
        return (
            f"mongodb://ai_law_admin:ai_law_mongo_password_2024@"
            f"localhost:27017/ai_law_firm?authSource=admin"
        )

    def _get_redis_connection_string(self) -> str:
        """Get Redis connection string from config."""
        return f"redis://:ai_law_redis_password_2024@localhost:63790/0"

    async def initialize(self) -> bool:
        """Initialize all database connections."""
        try:
            self.logger.info("Initializing Data Persistence Service...")

            # Initialize all repositories
            init_tasks = []
            for repo in self.repositories:
                init_tasks.append(repo.initialize())

            results = await asyncio.gather(*init_tasks, return_exceptions=True)

            # Check results
            failed_repos = []
            for repo, result in zip(self.repositories, results):
                if isinstance(result, Exception):
                    failed_repos.append((repo.__class__.__name__, str(result)))
                elif not result:
                    failed_repos.append((repo.__class__.__name__, "Initialization returned False"))

            if failed_repos:
                for repo_name, error in failed_repos:
                    self.logger.error(f"Failed to initialize {repo_name}: {error}")
                return False

            self.is_initialized = True
            self.logger.info("Data Persistence Service initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Data Persistence Service initialization failed: {str(e)}")
            return False

    async def shutdown(self) -> bool:
        """Shutdown all database connections."""
        try:
            self.logger.info("Shutting down Data Persistence Service...")

            # Shutdown all repositories
            shutdown_tasks = []
            for repo in self.repositories:
                shutdown_tasks.append(repo.shutdown())

            await asyncio.gather(*shutdown_tasks, return_exceptions=True)

            self.is_initialized = False
            self.logger.info("Data Persistence Service shutdown complete")
            return True

        except Exception as e:
            self.logger.error(f"Error during Data Persistence Service shutdown: {str(e)}")
            return False

    # User Management
    async def create_user(self, user: User) -> str:
        """Create a new user across all databases."""
        if not self.is_initialized:
            raise DatabaseError("Data Persistence Service not initialized")

        try:
            # Create user in PostgreSQL
            user_id = await self.postgres.create_user(user)

            # Initialize user data in MongoDB (if needed)
            # For now, users are primarily stored in PostgreSQL

            self.logger.info(f"User created successfully: {user_id}")
            return user_id

        except Exception as e:
            self.logger.error(f"Error creating user: {str(e)}")
            raise

    async def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        return await self.postgres.get_user_by_id(user_id)

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return await self.postgres.get_user_by_email(email)

    # Session Management
    async def create_session(self, session: UserSession) -> str:
        """Create a new user session."""
        try:
            # Create in PostgreSQL for persistence
            session_id = await self.postgres.create_session(session)

            # Also cache in Redis for fast access
            session_data = SessionData(
                session_id=session.session_id,
                user_id=session.user_id,
                data={
                    "ip_address": session.ip_address,
                    "user_agent": session.user_agent,
                    "created_at": session.created_at.isoformat(),
                    "expires_at": session.expires_at.isoformat()
                }
            )
            await self.redis.store_session(session_data)

            return session_id

        except Exception as e:
            self.logger.error(f"Error creating session: {str(e)}")
            raise

    async def get_session(self, session_token: str) -> Optional[UserSession]:
        """Get session by token (try Redis first, then PostgreSQL)."""
        try:
            # Try Redis first for speed
            session_data = await self.redis.get_session(session_token)
            if session_data:
                return UserSession(
                    session_id=session_data.session_id,
                    user_id=session_data.user_id,
                    session_token=session_token,
                    ip_address=session_data.data.get("ip_address"),
                    user_agent=session_data.data.get("user_agent"),
                    created_at=datetime.fromisoformat(session_data.data["created_at"]),
                    expires_at=datetime.fromisoformat(session_data.data["expires_at"]),
                    is_active=True
                )

            # Fallback to PostgreSQL
            return await self.postgres.get_session(session_token)

        except Exception as e:
            self.logger.error(f"Error getting session: {str(e)}")
            return None

    # Document Management
    async def store_document(
        self,
        document: Document,
        content: str,
        chunks: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store a complete document across databases."""
        if not self.is_initialized:
            raise DatabaseError("Data Persistence Service not initialized")

        try:
            # Store metadata in PostgreSQL
            document_id = await self.postgres.create_document(document)

            # Store content in MongoDB
            document_content = DocumentContent(
                document_id=document_id,
                content=content,
                chunks=chunks or [],
                metadata=metadata or {},
                processing_metadata={
                    "stored_at": datetime.utcnow(),
                    "storage_version": "1.0"
                }
            )
            await self.mongodb.store_document_content(document_content)

            # Cache document info in Redis for quick access
            cache_data = {
                "document_id": document_id,
                "filename": document.filename,
                "user_id": document.user_id,
                "document_type": document.document_type.value,
                "processing_status": document.processing_status.value,
                "created_at": document.created_at.isoformat()
            }
            await self.redis.set_cache(f"document:{document_id}", cache_data, ttl_seconds=3600)

            self.logger.info(f"Document stored successfully: {document_id}")
            return document_id

        except Exception as e:
            self.logger.error(f"Error storing document: {str(e)}")
            raise

    async def get_document(self, document_id: str) -> Optional[Tuple[Document, DocumentContent]]:
        """Get complete document data."""
        try:
            # Try cache first
            cache_key = f"document:{document_id}"
            cached_data = await self.redis.get_cache(cache_key)

            if cached_data:
                # Get from databases
                document = await self.postgres.get_document(document_id)
                content = await self.mongodb.get_document_content(document_id)

                if document and content:
                    return document, content

            # Get from databases
            document = await self.postgres.get_document(document_id)
            content = await self.mongodb.get_document_content(document_id)

            if document and content:
                # Update cache
                cache_data = {
                    "document_id": document_id,
                    "filename": document.filename,
                    "user_id": document.user_id,
                    "document_type": document.document_type.value,
                    "processing_status": document.processing_status.value,
                    "created_at": document.created_at.isoformat()
                }
                await self.redis.set_cache(cache_key, cache_data, ttl_seconds=3600)

                return document, content

            return None

        except Exception as e:
            self.logger.error(f"Error getting document: {str(e)}")
            return None

    async def update_document_status(
        self,
        document_id: str,
        status: ProcessingStatus,
        error_message: Optional[str] = None
    ) -> bool:
        """Update document processing status."""
        try:
            # Update PostgreSQL
            success = await self.postgres.update_document_status(document_id, status, error_message)

            if success:
                # Update cache
                cache_key = f"document:{document_id}"
                cached_data = await self.redis.get_cache(cache_key)
                if cached_data:
                    cached_data["processing_status"] = status.value
                    await self.redis.set_cache(cache_key, cached_data, ttl_seconds=3600)

            return success

        except Exception as e:
            self.logger.error(f"Error updating document status: {str(e)}")
            return False

    # Analysis Management
    async def create_analysis_session(self, session: AnalysisSession) -> str:
        """Create a new analysis session."""
        try:
            session_id = await self.postgres.create_analysis_session(session)

            # Cache session info
            cache_data = {
                "session_id": session_id,
                "user_id": session.user_id,
                "document_id": session.document_id,
                "analysis_type": session.analysis_type.value,
                "status": session.status.value,
                "created_at": session.created_at.isoformat()
            }
            await self.redis.set_cache(f"analysis_session:{session_id}", cache_data, ttl_seconds=1800)

            return session_id

        except Exception as e:
            self.logger.error(f"Error creating analysis session: {str(e)}")
            raise

    async def store_analysis_result(
        self,
        result: AnalysisResult,
        response_content: str,
        sources: List[Dict[str, Any]] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store a complete analysis result."""
        try:
            # Store metadata in PostgreSQL
            result_id = await self.postgres.create_analysis_result(result)

            # Store content in MongoDB
            result_content = AnalysisResultContent(
                result_id=result_id,
                session_id=result.session_id,
                agent_name=result.agent_name,
                query="",  # Would be passed in from analysis
                response=response_content,
                sources=sources or [],
                metadata=metadata or {}
            )
            await self.mongodb.store_analysis_result(result_content)

            # Update session cost
            if result.cost_estimate:
                await self.postgres.update_analysis_session_status(
                    result.session_id,
                    AnalysisStatus.COMPLETED,
                    total_cost=result.cost_estimate
                )

            self.logger.info(f"Analysis result stored: {result_id}")
            return result_id

        except Exception as e:
            self.logger.error(f"Error storing analysis result: {str(e)}")
            raise

    # Usage Tracking
    async def record_usage(
        self,
        user_id: str,
        documents_processed: int = 0,
        analyses_performed: int = 0,
        total_tokens: int = 0,
        total_cost: float = 0.0,
        api_calls: int = 0
    ) -> bool:
        """Record usage statistics."""
        try:
            return await self.postgres.record_usage(
                user_id=user_id,
                documents_processed=documents_processed,
                analyses_performed=analyses_performed,
                total_tokens=total_tokens,
                total_cost=total_cost,
                api_calls=api_calls
            )
        except Exception as e:
            self.logger.error(f"Error recording usage: {str(e)}")
            return False

    # Bulk Operations
    async def bulk_store_documents(
        self,
        documents: List[Tuple[Document, DocumentContent]]
    ) -> List[str]:
        """Bulk store multiple documents."""
        try:
            stored_ids = []

            for document, content in documents:
                document_id = await self.store_document(
                    document,
                    content.content,
                    content.chunks,
                    content.metadata
                )
                stored_ids.append(document_id)

            self.logger.info(f"Bulk stored {len(stored_ids)} documents")
            return stored_ids

        except Exception as e:
            self.logger.error(f"Error in bulk document storage: {str(e)}")
            return []

    async def bulk_store_analysis_results(
        self,
        results: List[Tuple[AnalysisResult, AnalysisResultContent]]
    ) -> List[str]:
        """Bulk store multiple analysis results."""
        try:
            stored_ids = []

            for result, content in results:
                result_id = await self.store_analysis_result(
                    result,
                    content.response,
                    content.sources,
                    content.metadata
                )
                stored_ids.append(result_id)

            self.logger.info(f"Bulk stored {len(stored_ids)} analysis results")
            return stored_ids

        except Exception as e:
            self.logger.error(f"Error in bulk result storage: {str(e)}")
            return []

    # Search and Retrieval
    async def search_documents(
        self,
        query: str,
        user_id: Optional[str] = None,
        document_type: Optional[DocumentType] = None,
        limit: int = 20
    ) -> List[Tuple[Document, DocumentContent]]:
        """Search documents across databases."""
        try:
            # Search in MongoDB for content matches
            mongo_results = await self.mongodb.search_documents(
                query=query,
                limit=limit
            )

            results = []
            for mongo_doc in mongo_results:
                # Get corresponding PostgreSQL metadata
                document = await self.postgres.get_document(mongo_doc.document_id)

                if document:
                    # Filter by user if specified
                    if user_id and document.user_id != user_id:
                        continue

                    # Filter by document type if specified
                    if document_type and document.document_type != document_type:
                        continue

                    results.append((document, mongo_doc))

            return results[:limit]

        except Exception as e:
            self.logger.error(f"Error searching documents: {str(e)}")
            return []

    async def get_user_documents(
        self,
        user_id: str,
        limit: int = 50
    ) -> List[Tuple[Document, DocumentContent]]:
        """Get all documents for a user."""
        try:
            # Get metadata from PostgreSQL
            documents = await self.postgres.get_user_documents(user_id, limit)

            results = []
            for document in documents:
                # Get content from MongoDB
                content = await self.mongodb.get_document_content(document.document_id)
                if content:
                    results.append((document, content))

            return results

        except Exception as e:
            self.logger.error(f"Error getting user documents: {str(e)}")
            return []

    # Analytics and Reporting
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        try:
            stats = {
                "timestamp": datetime.utcnow().isoformat(),
                "databases": {}
            }

            # PostgreSQL stats
            postgres_health = await self.postgres.health_check()
            stats["databases"]["postgresql"] = postgres_health

            # MongoDB stats
            mongo_stats = await self.mongodb.get_document_stats()
            mongo_health = await self.mongodb.health_check()
            stats["databases"]["mongodb"] = {**mongo_stats, **mongo_health}

            # Redis stats
            redis_stats = await self.redis.get_cache_stats()
            redis_health = await self.redis.health_check()
            stats["databases"]["redis"] = {**redis_stats, **redis_health}

            return stats

        except Exception as e:
            self.logger.error(f"Error getting system stats: {str(e)}")
            return {"error": str(e)}

    async def get_user_stats(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get usage statistics for a user."""
        try:
            usage_stats = await self.postgres.get_usage_stats(user_id, days)

            if not usage_stats:
                return {
                    "user_id": user_id,
                    "period_days": days,
                    "total_documents": 0,
                    "total_analyses": 0,
                    "total_tokens": 0,
                    "total_cost": 0.0,
                    "total_api_calls": 0
                }

            # Aggregate stats
            total_documents = sum(stat.documents_processed for stat in usage_stats)
            total_analyses = sum(stat.analyses_performed for stat in usage_stats)
            total_tokens = sum(stat.total_tokens for stat in usage_stats)
            total_cost = sum(stat.total_cost for stat in usage_stats)
            total_api_calls = sum(stat.api_calls for stat in usage_stats)

            return {
                "user_id": user_id,
                "period_days": days,
                "total_documents": total_documents,
                "total_analyses": total_analyses,
                "total_tokens": total_tokens,
                "total_cost": total_cost,
                "total_api_calls": total_api_calls,
                "daily_stats": [stat.to_dict() for stat in usage_stats]
            }

        except Exception as e:
            self.logger.error(f"Error getting user stats: {str(e)}")
            return {"error": str(e)}

    # Data Export/Import
    async def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """Export all user data for backup or migration."""
        try:
            export_data = {
                "user_id": user_id,
                "export_timestamp": datetime.utcnow().isoformat(),
                "data": {}
            }

            # Export user profile
            user = await self.get_user(user_id)
            if user:
                export_data["data"]["user_profile"] = user.to_dict()

            # Export documents
            documents = await self.get_user_documents(user_id)
            export_data["data"]["documents"] = []

            for document, content in documents:
                doc_data = {
                    "metadata": document.to_dict(),
                    "content": content.to_dict()
                }
                export_data["data"]["documents"].append(doc_data)

            # Export usage stats
            usage_stats = await self.get_user_stats(user_id)
            export_data["data"]["usage_stats"] = usage_stats

            return export_data

        except Exception as e:
            self.logger.error(f"Error exporting user data: {str(e)}")
            return {"error": str(e)}

    # Health Check
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check across all databases."""
        try:
            health_status = {
                "service": "data_persistence",
                "healthy": True,
                "timestamp": datetime.utcnow().isoformat(),
                "databases": {}
            }

            # Check all repositories
            for repo in self.repositories:
                repo_name = repo.__class__.__name__.lower().replace("repository", "").replace("cache", "")
                if hasattr(repo, 'health_check'):
                    health = await repo.health_check()
                    health_status["databases"][repo_name] = health

                    if not health.get("healthy", False):
                        health_status["healthy"] = False

            # Overall assessment
            if not self.is_initialized:
                health_status["healthy"] = False
                health_status["issues"] = ["Service not initialized"]

            return health_status

        except Exception as e:
            return {
                "service": "data_persistence",
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }