"""
Cache Service for AI Law Firm

This module provides a comprehensive caching layer that integrates with Redis
to improve performance across all application components.
"""

import json
import hashlib
from typing import Any, Dict, Optional, List, Union
from datetime import datetime, timedelta
import asyncio
import logging

from infrastructure.databases.redis_cache import RedisCache
from core.config.settings import get_config

logger = logging.getLogger(__name__)


class CacheService:
    """
    Comprehensive caching service with Redis backend.

    Features:
    - Multi-level caching (memory + Redis)
    - TTL-based expiration
    - Cache invalidation strategies
    - Performance metrics
    - Compression for large objects
    """

    def __init__(self, redis_cache: Optional[RedisCache] = None):
        self.config = get_config()
        self.redis = redis_cache or RedisCache(self.config)

        # Memory cache for frequently accessed data
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.memory_cache_size = 1000  # Max items in memory cache

        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "memory_cache_hits": 0,
            "redis_cache_hits": 0
        }

    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache with fallback to Redis.

        Args:
            key: Cache key
            default: Default value if key not found

        Returns:
            Cached value or default
        """
        # Check memory cache first
        if key in self.memory_cache:
            cache_entry = self.memory_cache[key]
            if self._is_expired(cache_entry):
                del self.memory_cache[key]
            else:
                self.stats["memory_cache_hits"] += 1
                self.stats["hits"] += 1
                return cache_entry["value"]

        # Check Redis cache
        try:
            redis_value = await self.redis.get_cache(key)
            if redis_value is not None:
                # Store in memory cache for faster future access
                self._set_memory_cache(key, redis_value, ttl=300)  # 5 minutes in memory
                self.stats["redis_cache_hits"] += 1
                self.stats["hits"] += 1
                return redis_value
        except Exception as e:
            logger.warning(f"Redis cache error: {e}")

        self.stats["misses"] += 1
        return default

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        memory_ttl: Optional[int] = None
    ) -> bool:
        """
        Set value in cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (Redis)
            memory_ttl: Time to live in memory cache

        Returns:
            True if successful
        """
        try:
            # Serialize complex objects
            if not isinstance(value, (str, int, float, bool)):
                value = json.dumps(value, default=str)

            # Set in Redis
            redis_ttl = ttl or self._get_default_ttl(key)
            success = await self.redis.set(key, value, ttl=redis_ttl)

            if success:
                # Set in memory cache
                mem_ttl = memory_ttl or min(redis_ttl, 300)  # Max 5 minutes in memory
                self._set_memory_cache(key, value, ttl=mem_ttl)
                self.stats["sets"] += 1

            return success

        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """
        Delete value from cache.

        Args:
            key: Cache key to delete

        Returns:
            True if successful
        """
        try:
            # Remove from memory cache
            if key in self.memory_cache:
                del self.memory_cache[key]

            # Remove from Redis
            success = await self.redis.delete_cache(key)

            if success:
                self.stats["deletes"] += 1

            return success

        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache.

        Args:
            key: Cache key to check

        Returns:
            True if key exists
        """
        # Check memory cache first
        if key in self.memory_cache and not self._is_expired(self.memory_cache[key]):
            return True

        # Check Redis
        try:
            return await self.redis.exists_cache(key)
        except Exception:
            return False

    async def get_or_set(
        self,
        key: str,
        default_func: callable,
        ttl: Optional[int] = None
    ) -> Any:
        """
        Get value from cache or set it using default function.

        Args:
            key: Cache key
            default_func: Function to generate value if not cached
            ttl: Time to live in seconds

        Returns:
            Cached or newly generated value
        """
        # Try to get from cache
        value = await self.get(key)
        if value is not None:
            return value

        # Generate new value
        try:
            if asyncio.iscoroutinefunction(default_func):
                value = await default_func()
            else:
                value = default_func()

            # Cache the value
            await self.set(key, value, ttl=ttl)
            return value

        except Exception as e:
            logger.error(f"Error generating cache value for key {key}: {e}")
            return None

    async def invalidate_pattern(self, pattern: str) -> int:
        """
        Invalidate all keys matching a pattern.

        Args:
            pattern: Pattern to match (e.g., "user:*:profile")

        Returns:
            Number of keys invalidated
        """
        try:
            # Clear memory cache entries matching pattern
            keys_to_delete = []
            for key in self.memory_cache:
                if self._matches_pattern(key, pattern):
                    keys_to_delete.append(key)

            for key in keys_to_delete:
                del self.memory_cache[key]

            # Clear Redis keys matching pattern
            redis_deleted = await self.redis.delete_pattern(pattern)

            self.stats["deletes"] += len(keys_to_delete) + redis_deleted
            return len(keys_to_delete) + redis_deleted

        except Exception as e:
            logger.error(f"Pattern invalidation error for {pattern}: {e}")
            return 0

    async def clear_all(self) -> bool:
        """
        Clear all cache entries.

        Returns:
            True if successful
        """
        try:
            # Clear memory cache
            self.memory_cache.clear()

            # Clear Redis cache
            success = await self.redis.clear_all()

            if success:
                logger.info("All cache cleared successfully")

            return success

        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache performance statistics.

        Returns:
            Dictionary with cache statistics
        """
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total_requests) if total_requests > 0 else 0

        return {
            "total_requests": total_requests,
            "hits": self.stats["hits"],
            "misses": self.stats["misses"],
            "hit_rate": round(hit_rate * 100, 2),
            "sets": self.stats["sets"],
            "deletes": self.stats["deletes"],
            "memory_cache_hits": self.stats["memory_cache_hits"],
            "redis_cache_hits": self.stats["redis_cache_hits"],
            "memory_cache_size": len(self.memory_cache),
            "memory_cache_max_size": self.memory_cache_size
        }

    def _set_memory_cache(self, key: str, value: Any, ttl: int):
        """Set value in memory cache with TTL."""
        self.memory_cache[key] = {
            "value": value,
            "expires_at": datetime.utcnow() + timedelta(seconds=ttl)
        }

        # Maintain cache size limit
        if len(self.memory_cache) > self.memory_cache_size:
            # Remove oldest entries (simple LRU approximation)
            oldest_key = min(
                self.memory_cache.keys(),
                key=lambda k: self.memory_cache[k]["expires_at"]
            )
            del self.memory_cache[oldest_key]

    def _is_expired(self, cache_entry: Dict[str, Any]) -> bool:
        """Check if memory cache entry is expired."""
        return datetime.utcnow() > cache_entry["expires_at"]

    def _get_default_ttl(self, key: str) -> int:
        """Get default TTL based on key pattern."""
        # Different TTLs for different types of data
        if key.startswith("user:"):
            return 3600  # 1 hour for user data
        elif key.startswith("document:"):
            return 1800  # 30 minutes for document data
        elif key.startswith("analysis:"):
            return 7200  # 2 hours for analysis results
        elif key.startswith("config:"):
            return 86400  # 24 hours for configuration
        elif key.startswith("ratelimit:"):
            return 3600  # 1 hour for rate limiting
        else:
            return 300  # 5 minutes default

    def _matches_pattern(self, key: str, pattern: str) -> bool:
        """Check if key matches a pattern with wildcards."""
        import fnmatch
        return fnmatch.fnmatch(key, pattern)

    def _cleanup_expired_memory_cache(self):
        """Remove expired entries from memory cache."""
        expired_keys = [
            key for key, entry in self.memory_cache.items()
            if self._is_expired(entry)
        ]

        for key in expired_keys:
            del self.memory_cache[key]

        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired memory cache entries")


# Cache key generators for consistent naming
class CacheKeys:
    """Cache key constants and generators."""

    @staticmethod
    def user_profile(user_id: str) -> str:
        return f"user:{user_id}:profile"

    @staticmethod
    def user_preferences(user_id: str) -> str:
        return f"user:{user_id}:preferences"

    @staticmethod
    def document_metadata(doc_id: str) -> str:
        return f"document:{doc_id}:metadata"

    @staticmethod
    def document_content(doc_id: str) -> str:
        return f"document:{doc_id}:content"

    @staticmethod
    def analysis_result(session_id: str) -> str:
        return f"analysis:{session_id}:result"

    @staticmethod
    def knowledge_search(query_hash: str) -> str:
        return f"knowledge:search:{query_hash}"

    @staticmethod
    def agent_response(agent_name: str, query_hash: str) -> str:
        return f"agent:{agent_name}:response:{query_hash}"

    @staticmethod
    def api_rate_limit(identifier: str) -> str:
        return f"ratelimit:api:{identifier}"

    @staticmethod
    def config_key(key: str) -> str:
        return f"config:{key}"

    @staticmethod
    def generate_query_hash(query: str, params: Optional[Dict[str, Any]] = None) -> str:
        """Generate a hash for query-based caching."""
        content = query
        if params:
            content += json.dumps(params, sort_keys=True)

        return hashlib.md5(content.encode()).hexdigest()[:16]


# Performance optimization decorators
def cached(ttl: Optional[int] = None, key_prefix: str = ""):
    """
    Decorator to cache function results.

    Args:
        ttl: Time to live in seconds
        key_prefix: Prefix for cache keys
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get cache service from context (would be injected)
            # For now, create a simple cache key
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"

            # This would use the actual cache service
            # cache_service = get_cache_service()
            # return await cache_service.get_or_set(cache_key, lambda: func(*args, **kwargs), ttl)

            # For now, just call the function
            return await func(*args, **kwargs)

        return wrapper
    return decorator


# Global cache service instance
_cache_service: Optional[CacheService] = None


def get_cache_service() -> CacheService:
    """Get the global cache service instance."""
    global _cache_service
    if _cache_service is None:
        _cache_service = CacheService()
    return _cache_service


def init_cache_service(redis_cache: Optional[RedisCache] = None) -> CacheService:
    """Initialize the global cache service."""
    global _cache_service
    _cache_service = CacheService(redis_cache)
    return _cache_service