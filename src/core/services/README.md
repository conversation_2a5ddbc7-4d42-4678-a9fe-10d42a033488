# AI Law Firm Performance Optimization & Caching

This directory contains performance optimization and caching services for the AI Law Firm system.

## 📊 Services Overview

### 1. Cache Service (`cache_service.py`)
**Purpose**: Multi-level caching with Redis backend for improved performance

**Features**:
- **Multi-level caching**: Memory cache + Redis for optimal performance
- **TTL-based expiration**: Automatic cache invalidation
- **Cache key generators**: Consistent naming conventions
- **Performance metrics**: Hit rates and cache statistics
- **Pattern invalidation**: Bulk cache clearing by patterns

**Usage**:
```python
from .cache_service import get_cache_service, CacheKeys

cache = get_cache_service()

# Set cache value
await cache.set("user:123:profile", user_data, ttl=3600)

# Get cache value
user_data = await cache.get("user:123:profile")

# Use predefined key generators
cache_key = CacheKeys.user_profile("123")
await cache.set(cache_key, profile_data)
```

### 2. Performance Service (`performance_service.py`)
**Purpose**: System performance monitoring and automatic optimization

**Features**:
- **Real-time metrics**: CPU, memory, response times
- **Performance thresholds**: Configurable limits with alerts
- **Automatic optimization**: Self-tuning based on load
- **Performance decorators**: Easy integration with existing code
- **Comprehensive reporting**: Detailed performance analysis

**Usage**:
```python
from .performance_service import get_performance_optimizer, measure_performance

optimizer = get_performance_optimizer()

# Measure function performance
@measure_performance("database_query")
async def get_user_data(user_id: str):
    return await db.get_user(user_id)

# Get performance report
report = await optimizer.get_performance_report()
```

## 🚀 Key Features

### Intelligent Caching Strategy
- **Memory-first**: Fast access for frequently used data
- **Redis-fallback**: Persistent caching for shared data
- **Smart TTL**: Different expiration times based on data type
- **Cache warming**: Pre-populate cache with frequently accessed data

### Performance Monitoring
- **System metrics**: CPU, memory, disk usage
- **Application metrics**: Response times, throughput, error rates
- **Database metrics**: Query times, connection pooling
- **AI metrics**: Model response times, token usage

### Automatic Optimization
- **Load-based tuning**: Adjust cache TTLs based on system load
- **Memory management**: Automatic cleanup of expired entries
- **Resource optimization**: Scale caching based on available resources

## 📈 Cache Key Patterns

### User Data
```python
CacheKeys.user_profile(user_id)        # "user:{id}:profile"
CacheKeys.user_preferences(user_id)    # "user:{id}:preferences"
```

### Document Data
```python
CacheKeys.document_metadata(doc_id)    # "document:{id}:metadata"
CacheKeys.document_content(doc_id)     # "document:{id}:content"
```

### Analysis Data
```python
CacheKeys.analysis_result(session_id)  # "analysis:{id}:result"
CacheKeys.agent_response(agent, hash)  # "agent:{name}:response:{hash}"
```

### System Data
```python
CacheKeys.config_key("max_file_size")  # "config:max_file_size"
CacheKeys.api_rate_limit(user_id)      # "ratelimit:api:{user_id}"
```

## ⚙️ Configuration

### Cache TTL Settings
```python
# Default TTLs by data type
USER_DATA_TTL = 3600      # 1 hour
DOCUMENT_DATA_TTL = 1800  # 30 minutes
ANALYSIS_DATA_TTL = 7200  # 2 hours
CONFIG_DATA_TTL = 86400   # 24 hours
```

### Performance Thresholds
```python
MAX_RESPONSE_TIME = 5000    # 5 seconds
MAX_MEMORY_USAGE = 80       # 80% of available memory
MIN_CACHE_HIT_RATE = 0.7    # 70% cache hit rate
MAX_DB_QUERY_TIME = 1000    # 1 second
```

## 🔧 Integration Examples

### API Endpoint with Caching
```python
@app.get("/api/v1/documents/{document_id}")
async def get_document(document_id: str):
    cache = get_cache_service()
    cache_key = CacheKeys.document_metadata(document_id)

    # Try cache first
    cached_doc = await cache.get(cache_key)
    if cached_doc:
        return cached_doc

    # Get from database
    document = await db.get_document(document_id)

    # Cache result
    await cache.set(cache_key, document, ttl=1800)
    return document
```

### Performance Monitoring
```python
@app.post("/api/v1/analysis")
@measure_performance("ai_analysis")
async def analyze_document(request: AnalysisRequest):
    optimizer = get_performance_optimizer()

    async with optimizer.measure_ai_request("openai", "gpt-4"):
        result = await ai_provider.analyze(request.content)
        return result
```

### Cache Invalidation
```python
# Invalidate user-related cache
await cache.invalidate_pattern(f"user:{user_id}:*")

# Invalidate document cache
await cache.invalidate_pattern(f"document:{doc_id}:*")

# Clear all cache
await cache.clear_all()
```

## 📊 Monitoring Endpoints

### Performance Report
```
GET /api/v1/performance/report
```
Returns comprehensive performance metrics and recommendations.

### Cache Statistics
```
GET /api/v1/performance/cache
```
Returns cache hit rates, memory usage, and performance statistics.

### Manual Optimization
```
POST /api/v1/performance/optimize?type=high_load
```
Triggers performance optimization based on current load.

## 🎯 Performance Benefits

### Expected Improvements
- **Response Time**: 60-80% faster for cached requests
- **Database Load**: 40-60% reduction in database queries
- **Memory Usage**: Optimized cache memory management
- **Scalability**: Better handling of concurrent requests

### Cache Hit Scenarios
- **User profiles**: High hit rate, long TTL
- **Document metadata**: Medium hit rate, medium TTL
- **Analysis results**: Variable hit rate, short TTL
- **Configuration**: Very high hit rate, long TTL

## 🔍 Troubleshooting

### Common Issues

#### Low Cache Hit Rate
```python
# Check cache statistics
stats = await cache.get_stats()
print(f"Hit rate: {stats['hit_rate']}%")

# Adjust TTLs for better hit rates
await cache_service.set(key, value, ttl=7200)  # Increase TTL
```

#### High Memory Usage
```python
# Check memory usage
report = await optimizer.get_performance_report()
memory_usage = report['summary']['memory_usage']['current']

if memory_usage > 80:
    # Trigger memory optimization
    await optimizer.optimize_for_high_load()
```

#### Slow Response Times
```python
# Check performance report
report = await optimizer.get_performance_report()
avg_response_time = report['summary']['response_time']['average']

if avg_response_time > 5000:  # 5 seconds
    # Check recommendations
    recommendations = report['recommendations']
    print("Optimization recommendations:", recommendations)
```

## 📈 Scaling Considerations

### Horizontal Scaling
- **Cache distribution**: Use Redis Cluster for multi-node caching
- **Load balancing**: Distribute requests across multiple instances
- **Database sharding**: Split data across multiple database instances

### Vertical Scaling
- **Memory optimization**: Increase cache memory limits
- **CPU optimization**: Optimize AI model inference
- **Storage optimization**: Use faster storage for cache persistence

### Monitoring at Scale
- **Metrics aggregation**: Collect metrics from multiple instances
- **Alerting**: Set up alerts for performance degradation
- **Auto-scaling**: Automatically adjust resources based on load

## 🔄 Best Practices

### Cache Management
1. **Use appropriate TTLs**: Different data types need different expiration times
2. **Monitor hit rates**: Regularly check cache effectiveness
3. **Implement cache warming**: Pre-populate cache with frequently accessed data
4. **Handle cache misses gracefully**: Always have fallback to source data

### Performance Monitoring
1. **Set meaningful thresholds**: Configure alerts for your specific use case
2. **Monitor trends**: Look at performance over time, not just snapshots
3. **Automate optimization**: Use automatic optimization when possible
4. **Document performance baselines**: Know what "normal" looks like

### Error Handling
1. **Cache failures shouldn't break the app**: Always have fallbacks
2. **Log performance issues**: Track when optimizations are triggered
3. **Monitor error rates**: High error rates may indicate performance issues
4. **Implement circuit breakers**: Prevent cascade failures

## 🚀 Future Enhancements

### Planned Features
- **Machine learning-based optimization**: AI-powered performance tuning
- **Predictive caching**: Cache based on usage patterns
- **Distributed tracing**: End-to-end request tracing
- **Advanced metrics**: Custom business metrics tracking

### Integration Opportunities
- **Kubernetes integration**: Automatic scaling based on metrics
- **Cloud optimization**: AWS/GCP/Azure specific optimizations
- **Edge caching**: CDN integration for global performance
- **Real-time analytics**: Live performance dashboards