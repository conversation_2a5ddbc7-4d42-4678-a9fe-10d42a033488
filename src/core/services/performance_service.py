"""
Performance Optimization Service for AI Law Firm

This module provides performance monitoring, optimization, and caching
strategies to improve application responsiveness and resource utilization.
"""

import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import psutil
import threading

from .cache_service import get_cache_service, CacheKeys
from ..config.settings import get_config

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """Container for performance metrics."""

    def __init__(self):
        self.response_times: List[float] = []
        self.memory_usage: List[float] = []
        self.cpu_usage: List[float] = []
        self.cache_hit_rates: List[float] = []
        self.database_query_times: List[float] = []
        self.ai_request_times: List[float] = []

    def add_response_time(self, time_ms: float):
        """Add response time measurement."""
        self.response_times.append(time_ms)
        self._maintain_size_limit(self.response_times)

    def add_memory_usage(self, usage_mb: float):
        """Add memory usage measurement."""
        self.memory_usage.append(usage_mb)
        self._maintain_size_limit(self.memory_usage)

    def add_cpu_usage(self, usage_percent: float):
        """Add CPU usage measurement."""
        self.cpu_usage.append(usage_percent)
        self._maintain_size_limit(self.cpu_usage)

    def add_cache_hit_rate(self, hit_rate: float):
        """Add cache hit rate measurement."""
        self.cache_hit_rates.append(hit_rate)
        self._maintain_size_limit(self.cache_hit_rates)

    def add_database_query_time(self, time_ms: float):
        """Add database query time measurement."""
        self.database_query_times.append(time_ms)
        self._maintain_size_limit(self.database_query_times)

    def add_ai_request_time(self, time_ms: float):
        """Add AI request time measurement."""
        self.ai_request_times.append(time_ms)
        self._maintain_size_limit(self.ai_request_times)

    def _maintain_size_limit(self, data_list: List, max_size: int = 1000):
        """Maintain size limit for data lists."""
        if len(data_list) > max_size:
            data_list[:] = data_list[-max_size:]

    def get_summary(self) -> Dict[str, Any]:
        """Get performance metrics summary."""
        def safe_avg(data: List) -> float:
            return sum(data) / len(data) if data else 0.0

        def safe_max(data: List) -> float:
            return max(data) if data else 0.0

        def safe_min(data: List) -> float:
            return min(data) if data else 0.0

        return {
            "response_time": {
                "average": safe_avg(self.response_times),
                "max": safe_max(self.response_times),
                "min": safe_min(self.response_times),
                "count": len(self.response_times)
            },
            "memory_usage": {
                "average": safe_avg(self.memory_usage),
                "max": safe_max(self.memory_usage),
                "current": self.memory_usage[-1] if self.memory_usage else 0
            },
            "cpu_usage": {
                "average": safe_avg(self.cpu_usage),
                "max": safe_max(self.cpu_usage),
                "current": self.cpu_usage[-1] if self.cpu_usage else 0
            },
            "cache_performance": {
                "average_hit_rate": safe_avg(self.cache_hit_rates),
                "current_hit_rate": self.cache_hit_rates[-1] if self.cache_hit_rates else 0
            },
            "database_performance": {
                "average_query_time": safe_avg(self.database_query_times),
                "max_query_time": safe_max(self.database_query_times)
            },
            "ai_performance": {
                "average_request_time": safe_avg(self.ai_request_times),
                "max_request_time": safe_max(self.ai_request_times)
            }
        }


class PerformanceOptimizer:
    """
    Performance optimization service with monitoring and caching.

    Features:
    - Response time monitoring
    - Memory and CPU usage tracking
    - Cache performance optimization
    - Database query optimization
    - AI request optimization
    - Automatic performance tuning
    """

    def __init__(self):
        self.config = get_config()
        self.cache_service = get_cache_service()
        self.metrics = PerformanceMetrics()

        # Performance thresholds
        self.thresholds = {
            "max_response_time": 5000,  # 5 seconds
            "max_memory_usage": 80,     # 80% of available memory
            "min_cache_hit_rate": 0.7,  # 70% cache hit rate
            "max_db_query_time": 1000,  # 1 second
            "max_ai_request_time": 30000  # 30 seconds
        }

        # Optimization settings
        self.cache_ttl_multipliers = {
            "high": 2.0,    # Increase TTL for high hit rate
            "medium": 1.0,  # Keep TTL same
            "low": 0.5      # Decrease TTL for low hit rate
        }

        # Background monitoring
        self.monitoring_active = False
        self.monitoring_thread: Optional[threading.Thread] = None

    async def start_monitoring(self):
        """Start background performance monitoring."""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        logger.info("Performance monitoring started")

    async def stop_monitoring(self):
        """Stop background performance monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("Performance monitoring stopped")

    def _monitoring_loop(self):
        """Background monitoring loop."""
        while self.monitoring_active:
            try:
                # Collect system metrics
                self._collect_system_metrics()

                # Analyze performance and apply optimizations
                asyncio.run(self._analyze_and_optimize())

                # Sleep for monitoring interval
                time.sleep(60)  # Monitor every minute

            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                time.sleep(30)  # Wait before retrying

    def _collect_system_metrics(self):
        """Collect current system metrics."""
        try:
            # Memory usage
            memory = psutil.virtual_memory()
            self.metrics.add_memory_usage(memory.percent)

            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics.add_cpu_usage(cpu_percent)

        except Exception as e:
            logger.error(f"System metrics collection error: {e}")

    async def _analyze_and_optimize(self):
        """Analyze performance and apply optimizations."""
        try:
            # Get current performance summary
            summary = self.metrics.get_summary()

            # Check cache performance
            cache_hit_rate = summary["cache_performance"]["current_hit_rate"]
            if cache_hit_rate < self.thresholds["min_cache_hit_rate"]:
                await self._optimize_cache_performance(cache_hit_rate)

            # Check memory usage
            memory_usage = summary["memory_usage"]["current"]
            if memory_usage > self.thresholds["max_memory_usage"]:
                await self._optimize_memory_usage(memory_usage)

            # Check response times
            avg_response_time = summary["response_time"]["average"]
            if avg_response_time > self.thresholds["max_response_time"]:
                await self._optimize_response_times(avg_response_time)

        except Exception as e:
            logger.error(f"Performance analysis error: {e}")

    async def _optimize_cache_performance(self, hit_rate: float):
        """Optimize cache performance based on hit rate."""
        logger.info(f"Optimizing cache performance. Current hit rate: {hit_rate:.2%}")

        # Adjust cache TTLs based on performance
        if hit_rate > 0.8:
            multiplier = self.cache_ttl_multipliers["high"]
            strategy = "increasing"
        elif hit_rate > 0.6:
            multiplier = self.cache_ttl_multipliers["medium"]
            strategy = "maintaining"
        else:
            multiplier = self.cache_ttl_multipliers["low"]
            strategy = "decreasing"

        # Apply TTL adjustments (this would modify cache service settings)
        logger.info(f"Cache optimization: {strategy} TTLs (multiplier: {multiplier})")

    async def _optimize_memory_usage(self, memory_percent: float):
        """Optimize memory usage."""
        logger.warning(f"High memory usage detected: {memory_percent:.1f}%")

        # Clear expired cache entries
        await self.cache_service._cleanup_expired_memory_cache()

        # Suggest garbage collection or memory optimization
        logger.info("Memory optimization: Cleared expired cache entries")

    async def _optimize_response_times(self, avg_response_time: float):
        """Optimize response times."""
        logger.warning(f"Slow response times detected: {avg_response_time:.0f}ms")

        # This could involve:
        # - Increasing cache TTLs
        # - Optimizing database queries
        # - Implementing response compression
        logger.info("Response time optimization: Analyzing bottlenecks")

    @asynccontextmanager
    async def measure_response_time(self, operation: str):
        """
        Context manager to measure response time for an operation.

        Args:
            operation: Name of the operation being measured
        """
        start_time = time.time()

        try:
            yield
        finally:
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            self.metrics.add_response_time(response_time)

            # Log slow operations
            if response_time > self.thresholds["max_response_time"]:
                logger.warning(f"Slow operation detected: {operation} took {response_time:.0f}ms")

    async def measure_database_query(self, query: str, params: Optional[List] = None):
        """
        Context manager to measure database query performance.

        Args:
            query: SQL query being executed
            params: Query parameters
        """
        @asynccontextmanager
        async def _measure():
            start_time = time.time()
            try:
                yield
            finally:
                query_time = (time.time() - start_time) * 1000
                self.metrics.add_database_query_time(query_time)

                if query_time > self.thresholds["max_db_query_time"]:
                    logger.warning(f"Slow database query: {query[:100]}... took {query_time:.0f}ms")

        return _measure()

    async def measure_ai_request(self, provider: str, model: str):
        """
        Context manager to measure AI request performance.

        Args:
            provider: AI provider name
            model: Model name
        """
        @asynccontextmanager
        async def _measure():
            start_time = time.time()
            try:
                yield
            finally:
                request_time = (time.time() - start_time) * 1000
                self.metrics.add_ai_request_time(request_time)

                if request_time > self.thresholds["max_ai_request_time"]:
                    logger.warning(f"Slow AI request: {provider}/{model} took {request_time:.0f}ms")

        return _measure()

    async def get_performance_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive performance report.

        Returns:
            Dictionary containing performance metrics and recommendations
        """
        summary = self.metrics.get_summary()
        cache_stats = self.cache_service.get_stats()

        # Generate recommendations
        recommendations = []

        if summary["cache_performance"]["average_hit_rate"] < 0.7:
            recommendations.append("Consider increasing cache TTL or implementing more aggressive caching")

        if summary["memory_usage"]["average"] > 75:
            recommendations.append("High memory usage detected - consider optimizing memory usage")

        if summary["response_time"]["average"] > 2000:
            recommendations.append("Slow response times detected - investigate bottlenecks")

        if summary["database_performance"]["average_query_time"] > 500:
            recommendations.append("Slow database queries detected - consider adding indexes or optimizing queries")

        return {
            "timestamp": datetime.utcnow().isoformat(),
            "summary": summary,
            "cache_stats": cache_stats,
            "recommendations": recommendations,
            "thresholds": self.thresholds
        }

    async def optimize_for_high_load(self):
        """
        Apply optimizations for high load scenarios.

        This method implements strategies for handling increased load:
        - Increase cache TTLs
        - Enable more aggressive caching
        - Optimize database connection pooling
        - Implement request queuing
        """
        logger.info("Applying high-load optimizations")

        # Increase cache TTLs
        await self._optimize_cache_performance(0.9)  # Simulate high hit rate

        # Clear less critical cache entries
        await self.cache_service.invalidate_pattern("temp:*")

        # Log optimization actions
        logger.info("High-load optimizations applied: Increased cache TTLs, cleared temporary data")

    async def optimize_for_low_load(self):
        """
        Apply optimizations for low load scenarios.

        This method implements strategies for low load:
        - Reduce cache memory usage
        - Clean up expired entries more aggressively
        - Scale down resources if applicable
        """
        logger.info("Applying low-load optimizations")

        # Aggressive cache cleanup
        await self.cache_service._cleanup_expired_memory_cache()

        # Reduce memory footprint
        logger.info("Low-load optimizations applied: Aggressive cache cleanup")


# Global performance optimizer instance
_performance_optimizer: Optional[PerformanceOptimizer] = None


def get_performance_optimizer() -> PerformanceOptimizer:
    """Get the global performance optimizer instance."""
    global _performance_optimizer
    if _performance_optimizer is None:
        _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer


def init_performance_optimizer() -> PerformanceOptimizer:
    """Initialize the global performance optimizer."""
    global _performance_optimizer
    _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer


# Performance monitoring decorators
def measure_performance(operation_name: str):
    """
    Decorator to measure performance of a function.

    Args:
        operation_name: Name of the operation for logging
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            optimizer = get_performance_optimizer()

            async with optimizer.measure_response_time(operation_name):
                return await func(*args, **kwargs)

        return wrapper
    return decorator


def measure_db_query(query_type: str = "generic"):
    """
    Decorator to measure database query performance.

    Args:
        query_type: Type of database query
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            optimizer = get_performance_optimizer()

            async with optimizer.measure_database_query(f"{query_type}: {func.__name__}"):
                return await func(*args, **kwargs)

        return wrapper
    return decorator


def measure_ai_request(provider: str, model: str):
    """
    Decorator to measure AI request performance.

    Args:
        provider: AI provider name
        model: Model name
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            optimizer = get_performance_optimizer()

            async with optimizer.measure_ai_request(provider, model):
                return await func(*args, **kwargs)

        return wrapper
    return decorator