"""
Type definitions for the Knowledge Base module.

This module defines the core data structures used throughout the knowledge
base system, ensuring type safety and consistency.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from enum import Enum


class DocumentStatus(str, Enum):
    """Document status in knowledge base."""
    PENDING = "pending"
    INDEXED = "indexed"
    PROCESSING = "processing"
    FAILED = "failed"
    DELETED = "deleted"


class SearchType(str, Enum):
    """Types of search operations."""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    FUZZY = "fuzzy"


class IndexStatus(str, Enum):
    """Vector index status."""
    BUILDING = "building"
    READY = "ready"
    UPDATING = "updating"
    FAILED = "failed"
    DELETED = "deleted"


@dataclass
class Document:
    """Represents a document in the knowledge base."""

    document_id: str
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Document properties
    title: Optional[str] = None
    source: Optional[str] = None
    author: Optional[str] = None
    document_type: Optional[str] = None

    # Processing metadata
    status: DocumentStatus = DocumentStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None
    indexed_at: Optional[datetime] = None

    # Content metadata
    word_count: int = 0
    character_count: int = 0
    language: str = "en"
    checksum: Optional[str] = None

    # Vector data
    embedding: Optional[List[float]] = None
    vector_id: Optional[str] = None

    # Relationships
    parent_document_id: Optional[str] = None
    child_document_ids: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)

    def __post_init__(self):
        """Validate document after initialization."""
        if not self.document_id:
            raise ValueError("Document ID is required")
        if not self.content:
            raise ValueError("Document content is required")

        # Auto-calculate counts if not provided
        if self.word_count == 0:
            self.word_count = len(self.content.split())
        if self.character_count == 0:
            self.character_count = len(self.content)

        # Generate checksum if not provided
        if not self.checksum:
            import hashlib
            self.checksum = hashlib.md5(self.content.encode('utf-8')).hexdigest()

    @property
    def is_indexed(self) -> bool:
        """Check if document is indexed."""
        return self.status == DocumentStatus.INDEXED and self.vector_id is not None

    @property
    def has_embedding(self) -> bool:
        """Check if document has embedding."""
        return self.embedding is not None and len(self.embedding) > 0

    def update_content(self, new_content: str):
        """Update document content and recalculate metadata."""
        self.content = new_content
        self.word_count = len(new_content.split())
        self.character_count = len(new_content)
        self.updated_at = datetime.utcnow()

        # Regenerate checksum
        import hashlib
        self.checksum = hashlib.md5(new_content.encode('utf-8')).hexdigest()

        # Reset indexing status
        self.status = DocumentStatus.PENDING
        self.indexed_at = None
        self.embedding = None
        self.vector_id = None

    def add_tag(self, tag: str):
        """Add a tag to the document."""
        if tag not in self.tags:
            self.tags.append(tag)

    def remove_tag(self, tag: str):
        """Remove a tag from the document."""
        if tag in self.tags:
            self.tags.remove(tag)

    def get_metadata_summary(self) -> Dict[str, Any]:
        """Get a summary of document metadata."""
        return {
            "document_id": self.document_id,
            "title": self.title,
            "source": self.source,
            "document_type": self.document_type,
            "status": self.status.value,
            "word_count": self.word_count,
            "created_at": self.created_at.isoformat(),
            "tags": self.tags.copy(),
            "is_indexed": self.is_indexed
        }


@dataclass
class SearchResult:
    """Represents a search result from the knowledge base."""

    document: Document
    score: float
    highlights: List[str] = field(default_factory=list)
    matched_terms: List[str] = field(default_factory=list)

    # Search metadata
    search_type: SearchType = SearchType.SEMANTIC
    search_query: Optional[str] = None
    search_timestamp: datetime = field(default_factory=datetime.utcnow)

    # Relevance metrics
    relevance_score: float = 0.0
    confidence_score: float = 0.0

    def __post_init__(self):
        """Validate search result after initialization."""
        if not isinstance(self.document, Document):
            raise ValueError("Document must be a Document instance")
        if not (0 <= self.score <= 1):
            raise ValueError("Score must be between 0 and 1")

        # Set default relevance score if not provided
        if self.relevance_score == 0.0:
            self.relevance_score = self.score

    @property
    def is_relevant(self, threshold: float = 0.5) -> bool:
        """Check if result is relevant based on threshold."""
        return self.score >= threshold

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the search result."""
        return {
            "document_id": self.document.document_id,
            "title": self.document.title,
            "score": self.score,
            "relevance_score": self.relevance_score,
            "highlights_count": len(self.highlights),
            "matched_terms_count": len(self.matched_terms),
            "search_type": self.search_type.value
        }


@dataclass
class KnowledgeQuery:
    """Represents a query to the knowledge base."""

    query: str
    search_type: SearchType = SearchType.SEMANTIC
    limit: int = 10
    threshold: float = 0.0

    # Query filters
    document_types: Optional[List[str]] = None
    sources: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None

    # Advanced options
    include_metadata: bool = True
    include_highlights: bool = True
    rerank_results: bool = False

    # Context
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context_documents: Optional[List[str]] = None

    def __post_init__(self):
        """Validate query after initialization."""
        if not self.query or not self.query.strip():
            raise ValueError("Query cannot be empty")
        if self.limit <= 0:
            raise ValueError("Limit must be positive")
        if not (0 <= self.threshold <= 1):
            raise ValueError("Threshold must be between 0 and 1")

    def get_filters(self) -> Dict[str, Any]:
        """Get query filters as a dictionary."""
        filters = {}

        if self.document_types:
            filters["document_type"] = {"$in": self.document_types}
        if self.sources:
            filters["source"] = {"$in": self.sources}
        if self.tags:
            filters["tags"] = {"$in": self.tags}
        if self.date_from or self.date_to:
            date_filter = {}
            if self.date_from:
                date_filter["$gte"] = self.date_from
            if self.date_to:
                date_filter["$lte"] = self.date_to
            filters["created_at"] = date_filter

        return filters

    def should_use_hybrid_search(self) -> bool:
        """Determine if hybrid search should be used."""
        return (
            self.search_type == SearchType.HYBRID or
            (len(self.query.split()) > 3 and self.search_type == SearchType.SEMANTIC)
        )


@dataclass
class IndexStatistics:
    """Statistics for vector index."""

    total_documents: int = 0
    indexed_documents: int = 0
    total_vectors: int = 0
    index_size_bytes: int = 0

    # Performance metrics
    average_query_time: float = 0.0
    queries_per_second: float = 0.0
    cache_hit_rate: float = 0.0

    # Index health
    status: IndexStatus = IndexStatus.READY
    last_updated: Optional[datetime] = None
    fragmentation_ratio: float = 0.0

    def __post_init__(self):
        """Validate statistics after initialization."""
        if self.total_documents < 0:
            raise ValueError("Total documents cannot be negative")
        if self.indexed_documents < 0:
            raise ValueError("Indexed documents cannot be negative")
        if self.indexed_documents > self.total_documents:
            raise ValueError("Indexed documents cannot exceed total documents")

    @property
    def indexing_progress(self) -> float:
        """Get indexing progress as a percentage."""
        if self.total_documents == 0:
            return 1.0
        return self.indexed_documents / self.total_documents

    @property
    def is_healthy(self) -> bool:
        """Check if index is healthy."""
        return (
            self.status == IndexStatus.READY and
            self.fragmentation_ratio < 0.5 and
            self.cache_hit_rate > 0.8
        )

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of index statistics."""
        return {
            "total_documents": self.total_documents,
            "indexed_documents": self.indexed_documents,
            "indexing_progress": f"{self.indexing_progress:.1%}",
            "total_vectors": self.total_vectors,
            "index_size_mb": self.index_size_bytes / (1024 * 1024),
            "status": self.status.value,
            "is_healthy": self.is_healthy,
            "average_query_time_ms": self.average_query_time * 1000,
            "queries_per_second": self.queries_per_second
        }


@dataclass
class SearchAnalytics:
    """Analytics for search operations."""

    query: str
    search_type: SearchType
    results_count: int
    search_time: float
    timestamp: datetime = field(default_factory=datetime.utcnow)

    # Performance metrics
    cache_hit: bool = False
    reranking_applied: bool = False
    filters_applied: List[str] = field(default_factory=list)

    # Result quality metrics
    average_score: float = 0.0
    top_score: float = 0.0
    score_distribution: Dict[str, int] = field(default_factory=dict)

    def __post_init__(self):
        """Validate analytics after initialization."""
        if self.search_time < 0:
            raise ValueError("Search time cannot be negative")
        if self.results_count < 0:
            raise ValueError("Results count cannot be negative")

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of search analytics."""
        return {
            "query": self.query,
            "search_type": self.search_type.value,
            "results_count": self.results_count,
            "search_time_ms": self.search_time * 1000,
            "cache_hit": self.cache_hit,
            "reranking_applied": self.reranking_applied,
            "average_score": self.average_score,
            "top_score": self.top_score,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class KnowledgeBaseConfig:
    """Configuration for knowledge base operations."""

    # Vector database settings
    collection_name: str = "legal_documents"
    vector_dimension: int = 1536  # OpenAI text-embedding-ada-002 dimension
    similarity_metric: str = "cosine"

    # Search settings
    default_limit: int = 10
    default_threshold: float = 0.0
    max_limit: int = 100

    # Indexing settings
    batch_size: int = 100
    indexing_threads: int = 4
    enable_incremental_indexing: bool = True

    # Caching settings
    enable_caching: bool = True
    cache_ttl_seconds: int = 3600  # 1 hour
    max_cache_size: int = 10000

    # Performance settings
    connection_pool_size: int = 10
    timeout_seconds: int = 30
    retry_attempts: int = 3

    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.vector_dimension <= 0:
            raise ValueError("Vector dimension must be positive")
        if self.batch_size <= 0:
            raise ValueError("Batch size must be positive")
        if self.default_limit <= 0:
            raise ValueError("Default limit must be positive")
        if self.max_limit < self.default_limit:
            raise ValueError("Max limit must be >= default limit")
        if self.cache_ttl_seconds <= 0:
            raise ValueError("Cache TTL must be positive")