"""
Base classes and interfaces for Knowledge Base implementations.

This module provides abstract base classes and interfaces that define the
standard contract for knowledge base operations, enabling pluggable
implementations for different vector databases and storage systems.
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Protocol
from dataclasses import dataclass
from datetime import datetime

from .types import (
    Document, SearchResult, KnowledgeQuery, IndexStatistics,
    DocumentStatus, SearchType, IndexStatus
)
from utils.logging import get_logger


class EmbeddingProviderProtocol(Protocol):
    """Protocol for embedding providers."""

    def encode(self, texts: List[str]) -> List[List[float]]:
        """Encode texts into embeddings."""
        ...

    def encode_single(self, text: str) -> List[float]:
        """Encode a single text into embedding."""
        ...


class VectorDatabase(ABC):
    """
    Abstract base class for vector database implementations.

    This class defines the standard interface that all vector database
    implementations must follow, enabling seamless switching between
    different vector database technologies.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self._connection = None
        self._is_connected = False

    @abstractmethod
    async def connect(self) -> bool:
        """Establish connection to the vector database."""
        pass

    @abstractmethod
    async def disconnect(self) -> bool:
        """Close connection to the vector database."""
        pass

    @abstractmethod
    async def is_connected(self) -> bool:
        """Check if database connection is active."""
        pass

    @abstractmethod
    async def create_collection(
        self,
        collection_name: str,
        vector_dimension: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Create a new vector collection."""
        pass

    @abstractmethod
    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a vector collection."""
        pass

    @abstractmethod
    async def collection_exists(self, collection_name: str) -> bool:
        """Check if collection exists."""
        pass

    @abstractmethod
    async def insert_vectors(
        self,
        collection_name: str,
        vectors: List[List[float]],
        payloads: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Insert vectors into the collection."""
        pass

    @abstractmethod
    async def search_vectors(
        self,
        collection_name: str,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.0,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        pass

    @abstractmethod
    async def delete_vectors(
        self,
        collection_name: str,
        vector_ids: List[str]
    ) -> bool:
        """Delete vectors from collection."""
        pass

    @abstractmethod
    async def update_vectors(
        self,
        collection_name: str,
        vector_ids: List[str],
        vectors: List[List[float]],
        payloads: List[Dict[str, Any]]
    ) -> bool:
        """Update existing vectors."""
        pass

    @abstractmethod
    async def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """Get collection statistics."""
        pass

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on vector database."""
        try:
            start_time = time.time()
            is_connected = await self.is_connected()
            response_time = time.time() - start_time

            if is_connected:
                # Try a simple operation
                collections = await self.list_collections()
                return {
                    "healthy": True,
                    "response_time": response_time,
                    "collections_count": len(collections),
                    "status": "connected"
                }
            else:
                return {
                    "healthy": False,
                    "error": "Not connected to database",
                    "status": "disconnected"
                }

        except Exception as e:
            self.logger.error(f"Health check failed: {str(e)}")
            return {
                "healthy": False,
                "error": str(e),
                "status": "error"
            }

    async def list_collections(self) -> List[str]:
        """List all collections (base implementation)."""
        # Default implementation - override in subclasses
        return []

    async def optimize_collection(self, collection_name: str) -> bool:
        """Optimize collection (base implementation)."""
        # Default implementation - override in subclasses
        return True


class KnowledgeBase:
    """
    Unified knowledge base interface that combines document storage,
    vector operations, and search capabilities.

    This class orchestrates the interaction between document storage,
    vector databases, and search engines to provide a complete
    knowledge management solution.
    """

    def __init__(
        self,
        vector_db: VectorDatabase,
        embedding_provider: EmbeddingProviderProtocol,
        config: Dict[str, Any]
    ):
        self.vector_db = vector_db
        self.embedding_provider = embedding_provider
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Internal state
        self._documents: Dict[str, Document] = {}
        self._index_stats = IndexStatistics()
        self._is_initialized = False

    async def initialize(self) -> bool:
        """Initialize the knowledge base."""
        try:
            self.logger.info("Initializing Knowledge Base...")

            # Connect to vector database
            if hasattr(self.vector_db, 'connect'):
                # For vector databases that use connect()
                if not await self.vector_db.connect():
                    self.logger.error("Failed to connect to vector database")
                    return False
            elif hasattr(self.vector_db, 'initialize'):
                # For repositories that use initialize()
                if not await self.vector_db.initialize():
                    self.logger.error("Failed to initialize vector database")
                    return False
            else:
                self.logger.error("Vector database has no connection method")
                return False

            # Create collection if it doesn't exist
            collection_name = self.config.get("collection_name", "legal_documents")
            vector_dimension = self.config.get("vector_dimension", 1536)

            if not await self.vector_db.collection_exists(collection_name):
                if not await self.vector_db.create_collection(
                    collection_name, vector_dimension
                ):
                    self.logger.error(f"Failed to create collection: {collection_name}")
                    return False

            self._is_initialized = True
            self.logger.info("Knowledge Base initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Knowledge Base initialization failed: {str(e)}")
            return False

    async def shutdown(self) -> bool:
        """Shutdown the knowledge base."""
        try:
            self.logger.info("Shutting down Knowledge Base...")

            # Disconnect from vector database
            await self.vector_db.disconnect()

            # Clear internal state
            self._documents.clear()
            self._is_initialized = False

            self.logger.info("Knowledge Base shutdown complete")
            return True

        except Exception as e:
            self.logger.error(f"Knowledge Base shutdown failed: {str(e)}")
            return False

    async def store_document(self, document: Document) -> bool:
        """Store a document in the knowledge base."""
        if not self._is_initialized:
            raise RuntimeError("Knowledge Base not initialized")

        try:
            # Generate embedding if not provided
            if not document.has_embedding:
                document.embedding = self.embedding_provider.encode_single(document.content)

            # Prepare payload
            payload = {
                "document_id": document.document_id,
                "content": document.content,
                "metadata": document.metadata,
                "title": document.title,
                "source": document.source,
                "author": document.author,
                "document_type": document.document_type,
                "created_at": document.created_at.isoformat(),
                "word_count": document.word_count,
                "character_count": document.character_count,
                "tags": document.tags
            }

            # Insert into vector database
            collection_name = self.config.get("collection_name", "legal_documents")
            vector_ids = await self.vector_db.insert_vectors(
                collection_name=collection_name,
                vectors=[document.embedding],
                payloads=[payload],
                ids=[document.document_id]
            )

            if vector_ids:
                document.vector_id = vector_ids[0]
                document.status = DocumentStatus.INDEXED
                document.indexed_at = datetime.utcnow()

                # Store in local cache
                self._documents[document.document_id] = document

                # Update statistics
                self._index_stats.total_documents += 1
                self._index_stats.indexed_documents += 1
                self._index_stats.total_vectors += 1

                self.logger.info(f"Document stored: {document.document_id}")
                return True
            else:
                document.status = DocumentStatus.FAILED
                self.logger.error(f"Failed to store document: {document.document_id}")
                return False

        except Exception as e:
            document.status = DocumentStatus.FAILED
            self.logger.error(f"Error storing document {document.document_id}: {str(e)}")
            return False

    async def retrieve_document(self, document_id: str) -> Optional[Document]:
        """Retrieve a document from the knowledge base."""
        if not self._is_initialized:
            raise RuntimeError("Knowledge Base not initialized")

        # Check local cache first
        if document_id in self._documents:
            return self._documents[document_id]

        try:
            # Search vector database
            collection_name = self.config.get("collection_name", "legal_documents")
            results = await self.vector_db.search_vectors(
                collection_name=collection_name,
                query_vector=[],  # Empty vector for metadata-only search
                limit=1,
                filters={"document_id": document_id}
            )

            if results:
                # Reconstruct document from payload
                payload = results[0].get("payload", {})
                document = Document(
                    document_id=payload.get("document_id", document_id),
                    content=payload.get("content", ""),
                    metadata=payload.get("metadata", {}),
                    title=payload.get("title"),
                    source=payload.get("source"),
                    author=payload.get("author"),
                    document_type=payload.get("document_type"),
                    word_count=payload.get("word_count", 0),
                    character_count=payload.get("character_count", 0),
                    tags=payload.get("tags", [])
                )

                # Cache locally
                self._documents[document_id] = document
                return document

        except Exception as e:
            self.logger.error(f"Error retrieving document {document_id}: {str(e)}")

        return None

    async def search_documents(
        self,
        query: KnowledgeQuery
    ) -> List[SearchResult]:
        """Search documents using the knowledge query."""
        if not self._is_initialized:
            raise RuntimeError("Knowledge Base not initialized")

        try:
            start_time = time.time()

            # Generate query embedding
            if query.search_type in [SearchType.SEMANTIC, SearchType.HYBRID]:
                query_embedding = self.embedding_provider.encode_single(query.query)
            else:
                query_embedding = []  # Keyword search doesn't need embedding

            # Perform vector search
            collection_name = self.config.get("collection_name", "legal_documents")
            search_results = await self.vector_db.search_vectors(
                collection_name=collection_name,
                query_vector=query_embedding,
                limit=query.limit,
                score_threshold=query.threshold,
                filters=query.get_filters()
            )

            # Convert to SearchResult objects
            results = []
            for search_result in search_results:
                payload = search_result.get("payload", {})
                score = search_result.get("score", 0.0)

                # Reconstruct document
                document = Document(
                    document_id=payload.get("document_id", ""),
                    content=payload.get("content", ""),
                    metadata=payload.get("metadata", {}),
                    title=payload.get("title"),
                    source=payload.get("source"),
                    author=payload.get("author"),
                    document_type=payload.get("document_type"),
                    word_count=payload.get("word_count", 0),
                    character_count=payload.get("character_count", 0),
                    tags=payload.get("tags", [])
                )

                # Create search result
                result = SearchResult(
                    document=document,
                    score=score,
                    search_type=query.search_type,
                    search_query=query.query
                )

                results.append(result)

            search_time = time.time() - start_time
            self.logger.info(f"Search completed in {search_time:.3f}s, found {len(results)} results")

            return results

        except Exception as e:
            self.logger.error(f"Search failed: {str(e)}")
            return []

    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from the knowledge base."""
        if not self._is_initialized:
            raise RuntimeError("Knowledge Base not initialized")

        try:
            # Get document to find vector_id
            document = await self.retrieve_document(document_id)
            if not document or not document.vector_id:
                self.logger.warning(f"Document not found or not indexed: {document_id}")
                return False

            # Delete from vector database
            collection_name = self.config.get("collection_name", "legal_documents")
            success = await self.vector_db.delete_vectors(
                collection_name=collection_name,
                vector_ids=[document.vector_id]
            )

            if success:
                # Remove from local cache
                if document_id in self._documents:
                    del self._documents[document_id]

                # Update statistics
                self._index_stats.total_documents = max(0, self._index_stats.total_documents - 1)
                self._index_stats.indexed_documents = max(0, self._index_stats.indexed_documents - 1)
                self._index_stats.total_vectors = max(0, self._index_stats.total_vectors - 1)

                self.logger.info(f"Document deleted: {document_id}")
                return True
            else:
                self.logger.error(f"Failed to delete document: {document_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error deleting document {document_id}: {str(e)}")
            return False

    async def get_statistics(self) -> IndexStatistics:
        """Get knowledge base statistics."""
        if not self._is_initialized:
            raise RuntimeError("Knowledge Base not initialized")

        try:
            # Get vector database statistics
            collection_name = self.config.get("collection_name", "legal_documents")
            db_stats = await self.vector_db.get_collection_stats(collection_name)

            # Update local statistics
            self._index_stats.total_documents = db_stats.get("total_documents", 0)
            self._index_stats.indexed_documents = db_stats.get("indexed_documents", 0)
            self._index_stats.total_vectors = db_stats.get("total_vectors", 0)
            self._index_stats.index_size_bytes = db_stats.get("index_size_bytes", 0)
            self._index_stats.last_updated = datetime.utcnow()

            return self._index_stats

        except Exception as e:
            self.logger.error(f"Error getting statistics: {str(e)}")
            return self._index_stats

    async def list_documents(self, skip: int = 0, limit: int = 50) -> List[Document]:
        """List documents with pagination."""
        if not self._is_initialized:
            raise RuntimeError("Knowledge Base not initialized")

        try:
            # Get all documents from vector database using scroll method
            collection_name = self.config.get("collection_name", "legal_documents")

            # Use scroll to get all points (this is the proper way to iterate through all documents)
            if hasattr(self.vector_db, 'client') and self.vector_db.client:
                # Use Qdrant client's scroll method directly
                scroll_result = self.vector_db.client.scroll(
                    collection_name=collection_name,
                    limit=10000,  # Large limit to get all documents
                    with_payload=True,
                    with_vectors=False
                )

                # Convert scroll results to Document objects
                documents = []
                for point in scroll_result[0]:  # scroll_result[0] contains the points
                    payload = point.payload

                    # Create Document object from payload
                    document = Document(
                        document_id=payload.get("document_id", ""),
                        content=payload.get("content", ""),
                        metadata=payload.get("metadata", {}),
                        title=payload.get("title"),
                        source=payload.get("source"),
                        author=payload.get("author"),
                        document_type=payload.get("document_type"),
                        word_count=payload.get("word_count", 0),
                        character_count=payload.get("character_count", 0),
                        tags=payload.get("tags", [])
                    )

                    # Set creation date from payload if available
                    if payload.get("created_at"):
                        try:
                            document.created_at = datetime.fromisoformat(payload["created_at"])
                        except:
                            document.created_at = datetime.utcnow()

                    documents.append(document)

                # Sort by creation date (newest first)
                documents.sort(key=lambda x: x.created_at, reverse=True)

                # Apply pagination
                start_idx = skip
                end_idx = skip + limit

                return documents[start_idx:end_idx]
            else:
                # Fallback to local cache if vector_db client is not available
                all_docs = list(self._documents.values())
                all_docs.sort(key=lambda x: x.created_at, reverse=True)
                start_idx = skip
                end_idx = skip + limit
                return all_docs[start_idx:end_idx]

        except Exception as e:
            self.logger.error(f"Error listing documents: {str(e)}")
            return []

    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        try:
            # Check vector database
            db_health = await self.vector_db.health_check()

            # Check embedding provider
            embedding_health = await self._check_embedding_provider()

            # Overall health
            is_healthy = db_health.get("healthy", False) and embedding_health.get("healthy", False)

            return {
                "healthy": is_healthy,
                "components": {
                    "vector_database": db_health,
                    "embedding_provider": embedding_health
                },
                "documents_count": len(self._documents),
                "is_initialized": self._is_initialized
            }

        except Exception as e:
            self.logger.error(f"Health check failed: {str(e)}")
            return {
                "healthy": False,
                "error": str(e),
                "is_initialized": self._is_initialized
            }

    async def _check_embedding_provider(self) -> Dict[str, Any]:
        """Check embedding provider health."""
        try:
            # Test embedding generation
            test_embedding = self.embedding_provider.encode_single("test")
            return {
                "healthy": True,
                "embedding_dimension": len(test_embedding) if test_embedding else 0
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }

    async def optimize(self) -> bool:
        """Optimize knowledge base performance."""
        if not self._is_initialized:
            raise RuntimeError("Knowledge Base not initialized")

        try:
            self.logger.info("Starting knowledge base optimization...")

            collection_name = self.config.get("collection_name", "legal_documents")

            # Optimize vector database
            db_optimized = await self.vector_db.optimize_collection(collection_name)

            # Clear old cache entries if needed
            cache_size = len(self._documents)
            max_cache_size = self.config.get("max_cache_size", 10000)

            if cache_size > max_cache_size:
                # Remove oldest entries (simple LRU)
                entries_to_remove = cache_size - max_cache_size
                documents_list = list(self._documents.items())
                documents_list.sort(key=lambda x: x[1].updated_at or x[1].created_at)

                for i in range(entries_to_remove):
                    doc_id, _ = documents_list[i]
                    del self._documents[doc_id]

            self.logger.info("Knowledge base optimization completed")
            return db_optimized

        except Exception as e:
            self.logger.error(f"Optimization failed: {str(e)}")
            return False