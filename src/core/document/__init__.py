"""
Document processing module for AI Law Firm.

This module provides comprehensive document processing capabilities including:
- PDF text extraction and parsing
- Document chunking and segmentation
- Metadata extraction
- Document validation and sanitization
- Multi-format support (PDF, DOCX, TXT)
"""

from .processor import DocumentProcessor
from .chunker import <PERSON>umentChunker, TextChunker, SemanticChunker
from .validator import DocumentValidator
from .types import ProcessedDocument, DocumentChunk, DocumentMetadata

__all__ = [
    "DocumentProcessor",
    "DocumentChunker",
    "TextChunker",
    "SemanticChunker",
    "DocumentValidator",
    "ProcessedDocument",
    "DocumentChunk",
    "DocumentMetadata",
]