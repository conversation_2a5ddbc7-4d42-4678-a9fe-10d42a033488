"""
Document processor for AI Law Firm.

This module provides comprehensive document processing capabilities including:
- Multi-format document parsing (PDF, DOCX, TXT)
- Text extraction and cleaning
- Metadata extraction
- Document validation
- Error handling and recovery
"""

import os
import time
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

from .types import (
    DocumentType, DocumentStatus, ProcessedDocument, DocumentMetadata,
    DocumentProcessingRequest, DocumentProcessingResult
)
from core.config.settings import DocumentConfig
from utils.logging import get_logger
from utils.exceptions import DocumentProcessingError, ValidationError


class DocumentProcessor:
    """
    Main document processor for the AI Law Firm system.

    Handles document parsing, text extraction, metadata extraction,
    and validation for multiple document formats.
    """

    def __init__(self, config: DocumentConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Initialize format handlers
        self.format_handlers = {
            DocumentType.PDF: self._process_pdf,
            DocumentType.DOCX: self._process_docx,
            DocumentType.TXT: self._process_txt,
            DocumentType.HTML: self._process_html,
            DocumentType.MARKDOWN: self._process_markdown,
            DocumentType.CSV: self._process_csv,
            DocumentType.XLS: self._process_excel,
            DocumentType.XLSX: self._process_excel,
            DocumentType.EML: self._process_eml,
            DocumentType.ZIP: self._process_zip,
        }

    def process_document(
        self,
        request: DocumentProcessingRequest
    ) -> DocumentProcessingResult:
        """
        Process a document according to the provided request.

        Args:
            request: Document processing request with configuration

        Returns:
            DocumentProcessingResult: Processing result with document or errors
        """
        start_time = time.time()
        result = DocumentProcessingResult(request=request)

        try:
            # Validate input file
            self._validate_input_file(request.file_path)

            # Determine document type
            document_type = request.document_type or self._detect_document_type(request.file_path)

            # Extract basic metadata
            metadata = self._extract_basic_metadata(request.file_path, document_type)

            # Apply metadata overrides
            if request.metadata_overrides:
                self._apply_metadata_overrides(metadata, request.metadata_overrides)

            # Process document content
            content = self._process_document_content(request.file_path, document_type)

            # Create processed document
            document_id = str(uuid.uuid4())
            document = ProcessedDocument(
                document_id=document_id,
                metadata=metadata,
                content=content,
                status=DocumentStatus.PROCESSING
            )

            # Extract additional metadata if requested
            if request.extract_metadata:
                self._extract_advanced_metadata(document)

            # Validate document if requested
            if request.validate_content:
                self._validate_document_content(document)

            # Mark as completed
            document.mark_completed()
            result.document = document
            result.success = True

        except Exception as e:
            error_msg = f"Document processing failed: {str(e)}"
            self.logger.error(error_msg, extra={
                "file_path": request.file_path,
                "error_type": type(e).__name__,
                "processing_time": time.time() - start_time
            })
            result.add_error(error_msg)

        finally:
            result.processing_time = time.time() - start_time

        return result

    def _validate_input_file(self, file_path: str) -> None:
        """Validate that the input file exists and is accessible."""
        if not file_path:
            raise ValidationError("File path is required")

        path = Path(file_path)
        if not path.exists():
            raise ValidationError(f"File does not exist: {file_path}")

        if not path.is_file():
            raise ValidationError(f"Path is not a file: {file_path}")

        # Check file size
        file_size = path.stat().st_size
        if file_size > self.config.max_size_mb * 1024 * 1024:
            raise ValidationError(
                f"File size ({file_size} bytes) exceeds maximum allowed size "
                f"({self.config.max_size_mb}MB)"
            )

        # Check file extension
        extension = path.suffix.lower().lstrip('.')
        if extension not in self.config.allowed_extensions:
            raise ValidationError(
                f"File extension '{extension}' is not supported. "
                f"Allowed extensions: {self.config.allowed_extensions}"
            )

    def _detect_document_type(self, file_path: str) -> DocumentType:
        """Detect document type from file extension."""
        path = Path(file_path)
        extension = path.suffix.lower().lstrip('.')

        extension_map = {
            'pdf': DocumentType.PDF,
            'docx': DocumentType.DOCX,
            'txt': DocumentType.TXT,
            'html': DocumentType.HTML,
            'htm': DocumentType.HTML,
            'md': DocumentType.MARKDOWN,
            'markdown': DocumentType.MARKDOWN,
            'csv': DocumentType.CSV,
            'xls': DocumentType.XLS,
            'xlsx': DocumentType.XLSX,
            'eml': DocumentType.EML,
            'zip': DocumentType.ZIP,
        }

        if extension not in extension_map:
            raise ValidationError(f"Unsupported file extension: {extension}")

        return extension_map[extension]

    def _extract_basic_metadata(
        self,
        file_path: str,
        document_type: DocumentType
    ) -> DocumentMetadata:
        """Extract basic metadata from file."""
        path = Path(file_path)
        stat = path.stat()

        metadata = DocumentMetadata(
            filename=path.name,
            file_path=str(path.absolute()),
            document_type=document_type,
            file_size_bytes=stat.st_size,
            creation_date=datetime.fromtimestamp(stat.st_ctime),
            modification_date=datetime.fromtimestamp(stat.st_mtime),
        )

        return metadata

    def _apply_metadata_overrides(
        self,
        metadata: DocumentMetadata,
        overrides: Dict[str, Any]
    ) -> None:
        """Apply metadata overrides to the document metadata."""
        for key, value in overrides.items():
            if hasattr(metadata, key):
                setattr(metadata, key, value)
            else:
                metadata.custom_fields[key] = value

    def _process_document_content(
        self,
        file_path: str,
        document_type: DocumentType
    ) -> str:
        """Process document content based on type."""
        if document_type not in self.format_handlers:
            raise DocumentProcessingError(f"No handler for document type: {document_type}")

        handler = self.format_handlers[document_type]
        return handler(file_path)

    def _process_pdf(self, file_path: str) -> str:
        """Process PDF document."""
        try:
            # Try PyPDF2 first (more reliable for text extraction)
            import PyPDF2

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""

                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"

                return text.strip()

        except ImportError:
            # Fallback to pdfplumber if PyPDF2 is not available
            try:
                import pdfplumber

                with pdfplumber.open(file_path) as pdf:
                    text = ""
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"

                    return text.strip()

            except ImportError:
                raise DocumentProcessingError(
                    "PDF processing requires PyPDF2 or pdfplumber. "
                    "Install with: pip install PyPDF2 or pip install pdfplumber"
                )
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process PDF: {str(e)}")

    def _process_docx(self, file_path: str) -> str:
        """Process DOCX document."""
        try:
            from docx import Document

            doc = Document(file_path)
            text = ""

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            # Also extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + "\n"

            return text.strip()

        except ImportError:
            raise DocumentProcessingError(
                "DOCX processing requires python-docx. "
                "Install with: pip install python-docx"
            )
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process DOCX: {str(e)}")

    def _process_txt(self, file_path: str) -> str:
        """Process plain text document."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except UnicodeDecodeError:
            # Try with different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        return file.read().strip()
                except UnicodeDecodeError:
                    continue

            raise DocumentProcessingError("Unable to decode text file with supported encodings")
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process text file: {str(e)}")

    def _process_html(self, file_path: str) -> str:
        """Process HTML document."""
        try:
            from bs4 import BeautifulSoup

            with open(file_path, 'r', encoding='utf-8') as file:
                soup = BeautifulSoup(file.read(), 'html.parser')

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Extract text
            text = soup.get_text(separator='\n')
            return text.strip()

        except ImportError:
            raise DocumentProcessingError(
                "HTML processing requires beautifulsoup4. "
                "Install with: pip install beautifulsoup4"
            )
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process HTML: {str(e)}")

    def _process_markdown(self, file_path: str) -> str:
        """Process Markdown document."""
        try:
            import markdown

            with open(file_path, 'r', encoding='utf-8') as file:
                md_content = file.read()

            # Convert markdown to HTML, then extract text
            html = markdown.markdown(md_content)

            # Simple HTML text extraction (fallback if beautifulsoup not available)
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html, 'html.parser')
                text = soup.get_text(separator='\n')
            except ImportError:
                # Basic HTML tag removal
                import re
                text = re.sub(r'<[^>]+>', '', html)
                text = re.sub(r'\n+', '\n', text)

            return text.strip()

        except ImportError:
            # Fallback to basic text reading
            return self._process_txt(file_path)
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process Markdown: {str(e)}")

    def _process_csv(self, file_path: str) -> str:
        """Process CSV document."""
        try:
            import csv
            import io

            text_content = []

            # Try to detect encoding and read CSV
            encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

            for encoding in encodings_to_try:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        # Read first few lines to understand structure
                        sample = file.read(1024)
                        file.seek(0)

                        # Try to detect delimiter
                        sniffer = csv.Sniffer()
                        delimiter = sniffer.sniff(sample, delimiters=',;\t|').delimiter

                        reader = csv.reader(file, delimiter=delimiter)

                        # Convert CSV to readable text format
                        headers = next(reader, None)
                        if headers:
                            text_content.append("CSV Headers: " + ", ".join(headers))

                        for i, row in enumerate(reader):
                            if i >= 100:  # Limit to first 100 rows for performance
                                text_content.append("... (truncated)")
                                break
                            text_content.append("Row " + str(i+1) + ": " + ", ".join(row))

                        break  # Successfully read with this encoding

                except (UnicodeDecodeError, csv.Error):
                    continue

            if not text_content:
                raise DocumentProcessingError("Unable to read CSV file with supported encodings")

            return "\n".join(text_content)

        except ImportError:
            raise DocumentProcessingError("CSV processing requires Python standard library csv module")
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process CSV: {str(e)}")

    def _process_excel(self, file_path: str) -> str:
        """Process Excel document (XLS/XLSX)."""
        try:
            import pandas as pd

            # Read all sheets
            excel_data = pd.read_excel(file_path, sheet_name=None)

            text_content = []

            for sheet_name, df in excel_data.items():
                text_content.append(f"Sheet: {sheet_name}")
                text_content.append("Columns: " + ", ".join(df.columns.tolist()))

                # Convert first 100 rows to text
                for i, (_, row) in enumerate(df.head(100).iterrows()):
                    row_text = ", ".join([f"{col}: {val}" for col, val in row.items() if pd.notna(val)])
                    text_content.append(f"Row {i+1}: {row_text}")

                if len(df) > 100:
                    text_content.append(f"... ({len(df) - 100} more rows)")

                text_content.append("")  # Empty line between sheets

            return "\n".join(text_content)

        except ImportError:
            raise DocumentProcessingError(
                "Excel processing requires pandas. Install with: pip install pandas openpyxl xlrd"
            )
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process Excel file: {str(e)}")

    def _process_eml(self, file_path: str) -> str:
        """Process EML (email) document."""
        try:
            from email import message_from_file
            from email.header import decode_header
            import email.utils

            text_content = []

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                msg = message_from_file(file)

                # Extract email headers
                subject = self._decode_email_header(msg.get('Subject', ''))
                sender = self._decode_email_header(msg.get('From', ''))
                recipient = self._decode_email_header(msg.get('To', ''))
                date = msg.get('Date', '')

                text_content.extend([
                    f"Subject: {subject}",
                    f"From: {sender}",
                    f"To: {recipient}",
                    f"Date: {date}",
                    ""
                ])

                # Extract email body
                if msg.is_multipart():
                    for part in msg.walk():
                        if part.get_content_type() == "text/plain":
                            payload = part.get_payload(decode=True)
                            if payload:
                                try:
                                    body = payload.decode('utf-8', errors='ignore')
                                    text_content.extend(["Body:", body])
                                    break
                                except UnicodeDecodeError:
                                    continue
                else:
                    payload = msg.get_payload(decode=True)
                    if payload:
                        try:
                            body = payload.decode('utf-8', errors='ignore')
                            text_content.extend(["Body:", body])
                        except UnicodeDecodeError:
                            text_content.append("Body: [Unable to decode email body]")

            return "\n".join(text_content)

        except ImportError:
            raise DocumentProcessingError("EML processing requires Python standard library email module")
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process EML file: {str(e)}")

    def _process_zip(self, file_path: str) -> str:
        """Process ZIP file by extracting and processing contained files."""
        try:
            import zipfile
            import tempfile
            import os

            text_content = []
            extracted_files = []

            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                # Get list of files in ZIP
                file_list = zip_ref.namelist()
                text_content.append(f"ZIP Archive Contents ({len(file_list)} files):")
                text_content.append(", ".join(file_list[:10]))  # Show first 10 files

                if len(file_list) > 10:
                    text_content.append(f"... and {len(file_list) - 10} more files")
                text_content.append("")

                # Extract and process text-based files
                with tempfile.TemporaryDirectory() as temp_dir:
                    # Extract all files
                    zip_ref.extractall(temp_dir)

                    # Process supported file types
                    supported_extensions = {'.txt', '.csv', '.md', '.html', '.htm'}

                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_ext = os.path.splitext(file)[1].lower()

                            if file_ext in supported_extensions:
                                file_path_full = os.path.join(root, file)
                                rel_path = os.path.relpath(file_path_full, temp_dir)

                                try:
                                    # Read file content
                                    with open(file_path_full, 'r', encoding='utf-8', errors='ignore') as f:
                                        content = f.read()

                                    # Add to extracted content (limit size)
                                    if len(content) > 5000:  # Limit individual file content
                                        content = content[:5000] + "... (truncated)"

                                    text_content.extend([
                                        f"--- File: {rel_path} ---",
                                        content,
                                        ""
                                    ])

                                    extracted_files.append(rel_path)

                                    # Limit total extracted content
                                    if len(text_content) > 50:  # Rough limit
                                        text_content.append("... (additional files truncated)")
                                        break

                                except Exception as e:
                                    text_content.append(f"--- File: {rel_path} ---")
                                    text_content.append(f"[Error reading file: {e}]")
                                    text_content.append("")

                        if len(text_content) > 50:
                            break

            if not extracted_files:
                text_content.append("No supported text files found in ZIP archive.")

            return "\n".join(text_content)

        except ImportError:
            raise DocumentProcessingError("ZIP processing requires Python standard library zipfile module")
        except Exception as e:
            raise DocumentProcessingError(f"Failed to process ZIP file: {str(e)}")

    def _decode_email_header(self, header_value: str) -> str:
        """Decode email header with proper encoding handling."""
        if not header_value:
            return ""

        try:
            decoded_parts = decode_header(header_value)
            decoded_string = ""

            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding, errors='ignore')
                    else:
                        decoded_string += part.decode('utf-8', errors='ignore')
                else:
                    decoded_string += str(part)

            return decoded_string
        except Exception:
            return str(header_value)

    def _extract_advanced_metadata(self, document: ProcessedDocument) -> None:
        """Extract advanced metadata from document content."""
        content = document.content

        # Count pages (rough estimate for text documents)
        if document.metadata.document_type == DocumentType.PDF:
            # For PDFs, we could get page count from the PDF reader
            # For now, estimate based on content length
            document.metadata.page_count = max(1, len(content) // 3000)  # Rough estimate

        # Count words and characters
        document.metadata.word_count = len(content.split())
        document.metadata.character_count = len(content)

        # Extract potential title (first non-empty line under certain length)
        lines = content.split('\n')
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if 10 <= len(line) <= 100 and not line.isupper():
                document.metadata.title = line
                break

        # Basic document categorization
        content_lower = content.lower()
        if any(term in content_lower for term in ['agreement', 'contract', 'parties', 'whereas']):
            document.metadata.document_category = 'contract'
        elif any(term in content_lower for term in ['complaint', 'defendant', 'plaintiff', 'court']):
            document.metadata.document_category = 'legal_brief'
        elif any(term in content_lower for term in ['statute', 'section', 'chapter', 'code']):
            document.metadata.document_category = 'legislation'

    def _validate_document_content(self, document: ProcessedDocument) -> None:
        """Validate document content quality."""
        content = document.content

        # Check minimum content length
        if len(content.strip()) < 100:
            document.add_processing_error("Document content is too short")
            return

        # Check for excessive special characters
        special_chars = sum(1 for c in content if not c.isalnum() and not c.isspace())
        special_ratio = special_chars / len(content) if content else 0

        if special_ratio > 0.5:
            document.add_processing_error("Document contains excessive special characters")

        # Check for readable text
        words = content.split()
        if len(words) > 0:
            avg_word_length = sum(len(word) for word in words) / len(words)
            if avg_word_length > 50:  # Likely not readable text
                document.add_processing_error("Document content appears to be unreadable")

        # Update quality score based on validation
        if not document.processing_errors:
            document.quality_score = 0.9  # High quality if no errors
        else:
            document.quality_score = max(0.1, 0.9 - (len(document.processing_errors) * 0.2))

    def get_supported_formats(self) -> List[str]:
        """Get list of supported document formats."""
        return [doc_type.value for doc_type in self.format_handlers.keys()]

    def get_format_requirements(self, document_type: DocumentType) -> Dict[str, Any]:
        """Get requirements for processing a specific document type."""
        base_requirements = {
            "max_size_mb": self.config.max_size_mb,
            "supported": document_type in self.format_handlers,
        }

        # Add format-specific requirements
        if document_type == DocumentType.PDF:
            base_requirements.update({
                "libraries": ["PyPDF2", "pdfplumber"],
                "features": ["text_extraction", "metadata_extraction"]
            })
        elif document_type == DocumentType.DOCX:
            base_requirements.update({
                "libraries": ["python-docx"],
                "features": ["text_extraction", "table_extraction"]
            })

        return base_requirements