"""
Type definitions for document processing in AI Law Firm.

This module defines the core data structures used throughout the document
processing pipeline, ensuring type safety and consistency.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum


class DocumentType(str, Enum):
    """Supported document types."""
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    HTML = "html"
    MARKDOWN = "markdown"
    CSV = "csv"
    XLS = "xls"
    XLSX = "xlsx"
    EML = "eml"
    ZIP = "zip"


class DocumentStatus(str, Enum):
    """Document processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    VALIDATED = "validated"


@dataclass
class DocumentMetadata:
    """Metadata extracted from a document."""

    filename: str
    file_path: Optional[str] = None
    document_type: DocumentType = DocumentType.PDF
    file_size_bytes: int = 0
    page_count: int = 0
    word_count: int = 0
    character_count: int = 0

    # Document properties
    title: Optional[str] = None
    author: Optional[str] = None
    creation_date: Optional[datetime] = None
    modification_date: Optional[datetime] = None

    # Legal document specific metadata
    document_category: Optional[str] = None  # contract, brief, statute, etc.
    jurisdiction: Optional[str] = None
    case_number: Optional[str] = None
    parties: List[str] = field(default_factory=list)

    # Processing metadata
    processing_date: Optional[datetime] = None
    processing_duration: float = 0.0
    processing_errors: List[str] = field(default_factory=list)

    # Custom metadata
    custom_fields: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate metadata after initialization."""
        if not self.filename:
            raise ValueError("Filename is required")
        if self.file_size_bytes < 0:
            raise ValueError("File size cannot be negative")
        if self.page_count < 0:
            raise ValueError("Page count cannot be negative")


@dataclass
class DocumentChunk:
    """Represents a chunk of document content."""

    chunk_id: str
    document_id: str
    content: str
    chunk_index: int
    start_position: int = 0
    end_position: int = 0

    # Chunk metadata
    word_count: int = 0
    character_count: int = 0
    page_number: Optional[int] = None
    section_title: Optional[str] = None

    # Embedding and vector data
    embedding: Optional[List[float]] = None
    vector_id: Optional[str] = None

    # Chunk relationships
    parent_chunk_id: Optional[str] = None
    child_chunk_ids: List[str] = field(default_factory=list)

    # Processing metadata
    processing_date: datetime = field(default_factory=datetime.utcnow)
    confidence_score: float = 1.0

    # Custom metadata
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate chunk after initialization."""
        if not self.chunk_id:
            raise ValueError("Chunk ID is required")
        if not self.document_id:
            raise ValueError("Document ID is required")
        if not self.content:
            raise ValueError("Content is required")
        if self.chunk_index < 0:
            raise ValueError("Chunk index cannot be negative")
        if not (0 <= self.confidence_score <= 1):
            raise ValueError("Confidence score must be between 0 and 1")

        # Auto-calculate counts if not provided
        if self.word_count == 0:
            self.word_count = len(self.content.split())
        if self.character_count == 0:
            self.character_count = len(self.content)


@dataclass
class ProcessedDocument:
    """Represents a fully processed document."""

    document_id: str
    metadata: DocumentMetadata
    content: str
    chunks: List[DocumentChunk] = field(default_factory=list)

    # Processing status
    status: DocumentStatus = DocumentStatus.PENDING
    processing_errors: List[str] = field(default_factory=list)

    # Quality metrics
    quality_score: float = 0.0
    readability_score: Optional[float] = None
    complexity_score: Optional[float] = None

    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    validated_at: Optional[datetime] = None

    # Relationships
    parent_document_id: Optional[str] = None
    child_document_ids: List[str] = field(default_factory=list)

    # Custom data
    custom_data: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate processed document after initialization."""
        if not self.document_id:
            raise ValueError("Document ID is required")
        if not self.content and not self.chunks:
            raise ValueError("Either content or chunks must be provided")
        if not (0 <= self.quality_score <= 1):
            raise ValueError("Quality score must be between 0 and 1")

    @property
    def is_processed(self) -> bool:
        """Check if document has been processed."""
        return self.status in [DocumentStatus.COMPLETED, DocumentStatus.VALIDATED]

    @property
    def has_errors(self) -> bool:
        """Check if document has processing errors."""
        return len(self.processing_errors) > 0

    @property
    def total_chunks(self) -> int:
        """Get total number of chunks."""
        return len(self.chunks)

    @property
    def total_words(self) -> int:
        """Get total word count."""
        if self.chunks:
            return sum(chunk.word_count for chunk in self.chunks)
        return len(self.content.split())

    def get_chunk_by_id(self, chunk_id: str) -> Optional[DocumentChunk]:
        """Get a chunk by its ID."""
        for chunk in self.chunks:
            if chunk.chunk_id == chunk_id:
                return chunk
        return None

    def get_chunks_by_page(self, page_number: int) -> List[DocumentChunk]:
        """Get all chunks from a specific page."""
        return [
            chunk for chunk in self.chunks
            if chunk.page_number == page_number
        ]

    def add_processing_error(self, error: str):
        """Add a processing error."""
        self.processing_errors.append(error)
        if self.status == DocumentStatus.PROCESSING:
            self.status = DocumentStatus.FAILED

    def mark_completed(self):
        """Mark document as completed."""
        self.status = DocumentStatus.COMPLETED
        self.processed_at = datetime.utcnow()

    def mark_validated(self):
        """Mark document as validated."""
        self.status = DocumentStatus.VALIDATED
        self.validated_at = datetime.utcnow()


@dataclass
class DocumentProcessingRequest:
    """Request for document processing."""

    file_path: str
    document_type: Optional[DocumentType] = None
    processing_options: Dict[str, Any] = field(default_factory=dict)
    metadata_overrides: Dict[str, Any] = field(default_factory=dict)

    # Processing configuration
    extract_metadata: bool = True
    generate_chunks: bool = True
    chunk_size: int = 1000
    chunk_overlap: int = 200

    # Quality checks
    validate_content: bool = True
    check_readability: bool = False

    def __post_init__(self):
        """Validate request after initialization."""
        if not self.file_path:
            raise ValueError("File path is required")
        if self.chunk_size <= 0:
            raise ValueError("Chunk size must be positive")
        if self.chunk_overlap < 0:
            raise ValueError("Chunk overlap cannot be negative")
        if self.chunk_overlap >= self.chunk_size:
            raise ValueError("Chunk overlap must be less than chunk size")


@dataclass
class DocumentProcessingResult:
    """Result of document processing."""

    request: DocumentProcessingRequest
    document: Optional[ProcessedDocument] = None
    success: bool = False
    processing_time: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    # Performance metrics
    chunks_created: int = 0
    tokens_processed: int = 0

    def __post_init__(self):
        """Validate result after initialization."""
        if self.processing_time < 0:
            raise ValueError("Processing time cannot be negative")

    @property
    def has_warnings(self) -> bool:
        """Check if result has warnings."""
        return len(self.warnings) > 0

    @property
    def has_errors(self) -> bool:
        """Check if result has errors."""
        return len(self.errors) > 0

    def add_error(self, error: str):
        """Add an error to the result."""
        self.errors.append(error)
        self.success = False

    def add_warning(self, warning: str):
        """Add a warning to the result."""
        self.warnings.append(warning)