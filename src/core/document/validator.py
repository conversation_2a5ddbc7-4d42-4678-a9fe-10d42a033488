"""
Document validation for AI Law Firm.

This module provides comprehensive validation for documents including:
- Content quality checks
- Format validation
- Security scanning
- Legal document structure validation
"""

import re
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

from .types import ProcessedDocument, DocumentMetadata, DocumentType
from core.config.settings import DocumentConfig
from utils.logging import get_logger
from utils.exceptions import ValidationError


class DocumentValidator:
    """
    Comprehensive document validator for the AI Law Firm system.

    Performs multiple validation checks on documents to ensure they meet
    quality, security, and legal standards before processing.
    """

    def __init__(self, config: DocumentConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Initialize validation rules
        self.content_validators = {
            'min_length': self._validate_minimum_length,
            'max_length': self._validate_maximum_length,
            'character_encoding': self._validate_character_encoding,
            'content_quality': self._validate_content_quality,
            'security': self._validate_security,
        }

        self.legal_validators = {
            'structure': self._validate_legal_structure,
            'parties': self._validate_parties_identification,
            'dates': self._validate_date_formats,
            'references': self._validate_legal_references,
        }

    def validate_document(self, document: ProcessedDocument) -> Dict[str, Any]:
        """
        Perform comprehensive validation on a document.

        Args:
            document: The processed document to validate

        Returns:
            Dict containing validation results and any issues found
        """
        validation_results = {
            'is_valid': True,
            'overall_score': 1.0,
            'checks_passed': 0,
            'total_checks': 0,
            'issues': [],
            'warnings': [],
            'recommendations': [],
            'validation_metadata': {}
        }

        try:
            # Basic content validation
            content_results = self._validate_content(document)
            validation_results.update(content_results)

            # Legal document validation
            if self._is_legal_document(document):
                legal_results = self._validate_legal_content(document)
                self._merge_validation_results(validation_results, legal_results)

            # Format-specific validation
            format_results = self._validate_format_specific(document)
            self._merge_validation_results(validation_results, format_results)

            # Calculate overall score
            validation_results['overall_score'] = self._calculate_overall_score(validation_results)

            # Update document quality score
            document.quality_score = validation_results['overall_score']

            # Add validation metadata
            validation_results['validation_metadata'] = {
                'validation_timestamp': None,  # Will be set by caller
                'validator_version': '1.0.0',
                'checks_performed': list(self.content_validators.keys()) +
                                  (list(self.legal_validators.keys()) if self._is_legal_document(document) else [])
            }

        except Exception as e:
            validation_results['is_valid'] = False
            validation_results['issues'].append(f"Validation failed: {str(e)}")
            validation_results['overall_score'] = 0.0

        return validation_results

    def _validate_content(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Perform basic content validation."""
        results = {
            'is_valid': True,
            'checks_passed': 0,
            'total_checks': len(self.content_validators),
            'issues': [],
            'warnings': []
        }

        for check_name, validator_func in self.content_validators.items():
            try:
                check_result = validator_func(document)
                if not check_result['passed']:
                    results['is_valid'] = False
                    results['issues'].extend(check_result.get('issues', []))
                else:
                    results['checks_passed'] += 1

                results['warnings'].extend(check_result.get('warnings', []))

            except Exception as e:
                results['is_valid'] = False
                results['issues'].append(f"{check_name} validation error: {str(e)}")

        return results

    def _validate_minimum_length(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate minimum content length."""
        min_length = 100  # Minimum characters
        content_length = len(document.content.strip())

        if content_length < min_length:
            return {
                'passed': False,
                'issues': [f"Document content too short: {content_length} characters (minimum: {min_length})"]
            }

        return {'passed': True}

    def _validate_maximum_length(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate maximum content length."""
        max_length = self.config.max_size_mb * 1024 * 1024  # Convert MB to characters (rough estimate)
        content_length = len(document.content)

        if content_length > max_length:
            return {
                'passed': False,
                'issues': [f"Document content too long: {content_length} characters (maximum: {max_length})"]
            }

        return {'passed': True}

    def _validate_character_encoding(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate character encoding and detect potential issues."""
        content = document.content
        issues = []
        warnings = []

        # Check for null bytes
        if '\x00' in content:
            issues.append("Document contains null bytes")

        # Check for unusual character ratios
        total_chars = len(content)
        if total_chars > 0:
            # Count different character types
            ascii_chars = sum(1 for c in content if ord(c) < 128)
            non_ascii_chars = total_chars - ascii_chars
            special_chars = sum(1 for c in content if not c.isalnum() and not c.isspace())

            ascii_ratio = ascii_chars / total_chars
            special_ratio = special_chars / total_chars

            if ascii_ratio < 0.7:
                warnings.append(f"Low ASCII character ratio: {ascii_ratio:.2%}")

            if special_ratio > 0.3:
                warnings.append(f"High special character ratio: {special_ratio:.2%}")

        # Check for encoding issues
        try:
            content.encode('utf-8')
        except UnicodeEncodeError:
            issues.append("Document contains characters that cannot be encoded as UTF-8")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'warnings': warnings
        }

    def _validate_content_quality(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate content quality and readability."""
        content = document.content
        issues = []
        warnings = []

        # Check for excessive whitespace
        whitespace_ratio = sum(1 for c in content if c.isspace()) / len(content) if content else 0
        if whitespace_ratio > 0.5:
            warnings.append(f"High whitespace ratio: {whitespace_ratio:.2%}")

        # Check word length distribution
        words = content.split()
        if words:
            avg_word_length = sum(len(word) for word in words) / len(words)
            if avg_word_length > 20:
                warnings.append(f"Unusually long average word length: {avg_word_length:.1f} characters")

            # Check for very short or very long words
            short_words = sum(1 for word in words if len(word) == 1)
            long_words = sum(1 for word in words if len(word) > 50)

            if short_words / len(words) > 0.3:
                warnings.append("High proportion of single-character words")

            if long_words > len(words) * 0.01:  # More than 1% very long words
                warnings.append("Contains unusually long words")

        # Check for repetitive content
        if len(content) > 1000:
            # Simple repetition check - look for repeated phrases
            sentences = re.split(r'[.!?]+', content)
            if len(sentences) > 10:
                unique_sentences = set(sentence.strip().lower() for sentence in sentences if sentence.strip())
                repetition_ratio = 1 - (len(unique_sentences) / len(sentences))
                if repetition_ratio > 0.5:
                    warnings.append(f"High content repetition: {repetition_ratio:.2%}")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'warnings': warnings
        }

    def _validate_security(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Perform security validation on document content."""
        content = document.content.lower()
        issues = []

        # Check for potentially sensitive patterns
        sensitive_patterns = [
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN pattern
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card pattern
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Email pattern
            r'\b\d{10}\b',  # 10-digit numbers (could be phone numbers)
        ]

        for pattern in sensitive_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues.append(f"Document contains {len(matches)} potential sensitive data patterns")

        # Check for embedded scripts or code
        script_indicators = ['<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=']
        for indicator in script_indicators:
            if indicator in content:
                issues.append(f"Document contains script indicators: {indicator}")

        return {
            'passed': len(issues) == 0,
            'issues': issues
        }

    def _is_legal_document(self, document: ProcessedDocument) -> bool:
        """Determine if document appears to be a legal document."""
        content_lower = document.content.lower()

        legal_indicators = [
            'agreement', 'contract', 'whereas', 'party', 'parties',
            'hereby', 'hereinafter', 'witnesseth', 'executed',
            'court', 'judge', 'plaintiff', 'defendant', 'complaint',
            'statute', 'section', 'article', 'clause', 'provision'
        ]

        indicator_count = sum(1 for indicator in legal_indicators if indicator in content_lower)
        legal_ratio = indicator_count / len(legal_indicators)

        return legal_ratio > 0.3  # More than 30% legal indicators

    def _validate_legal_content(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Perform legal document specific validation."""
        results = {
            'is_valid': True,
            'checks_passed': 0,
            'total_checks': len(self.legal_validators),
            'issues': [],
            'warnings': []
        }

        for check_name, validator_func in self.legal_validators.items():
            try:
                check_result = validator_func(document)
                if not check_result['passed']:
                    results['is_valid'] = False
                    results['issues'].extend(check_result.get('issues', []))
                else:
                    results['checks_passed'] += 1

                results['warnings'].extend(check_result.get('warnings', []))

            except Exception as e:
                results['is_valid'] = False
                results['issues'].append(f"{check_name} validation error: {str(e)}")

        return results

    def _validate_legal_structure(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate legal document structure."""
        content = document.content
        issues = []
        warnings = []

        # Check for basic legal document structure
        structure_indicators = {
            'parties': ['party', 'parties', 'between', 'hereinafter'],
            'recitals': ['whereas', 'recitals', 'background'],
            'terms': ['terms', 'conditions', 'agreements', 'obligations'],
            'signatures': ['signature', 'signed', 'executed', 'date']
        }

        found_sections = 0
        for section_name, indicators in structure_indicators.items():
            if any(indicator in content.lower() for indicator in indicators):
                found_sections += 1
            else:
                warnings.append(f"Missing {section_name} section indicators")

        if found_sections < 2:
            issues.append("Document lacks basic legal structure")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'warnings': warnings
        }

    def _validate_parties_identification(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate identification of parties in legal documents."""
        content = document.content
        issues = []

        # Look for party identification patterns
        party_patterns = [
            r'(?:the\s+)?"([^"]+)"(?:\s*,?\s*(?:hereinafter\s+)?(?:referred\s+to\s+as\s+)?["\']([^"\']+)["\'])?',
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)(?:\s*,?\s*(?:hereinafter\s+)?(?:referred\s+to\s+as\s+)?["\']([^"\']+)["\'])?',
        ]

        parties_found = []
        for pattern in party_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            parties_found.extend(matches)

        if len(parties_found) < 2:
            issues.append("Unable to identify at least two parties in the document")

        return {
            'passed': len(issues) == 0,
            'issues': issues
        }

    def _validate_date_formats(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate date formats in legal documents."""
        content = document.content
        warnings = []

        # Common date patterns
        date_patterns = [
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # MM/DD/YYYY or DD/MM/YYYY
            r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',    # YYYY/MM/DD
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b',
            r'\b\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December),?\s+\d{4}\b'
        ]

        dates_found = []
        for pattern in date_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            dates_found.extend(matches)

        if len(dates_found) == 0:
            warnings.append("No dates found in document")

        return {
            'passed': True,  # Date validation is not critical for document acceptance
            'warnings': warnings
        }

    def _validate_legal_references(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Validate legal references and citations."""
        content = document.content
        warnings = []

        # Legal reference patterns
        reference_patterns = [
            r'\b\d+\s+U\.S\.C\.\s+§\s+\d+\b',  # US Code references
            r'\b\d+\s+Cal\.\s+App\.\s+\d+\b',  # California appellate references
            r'\b\d+\s+Cal\.\s+\d+\b',          # California supreme court references
            r'\b\d+\s+U\.S\.\s+\d+\b',         # US Supreme Court references
        ]

        references_found = []
        for pattern in reference_patterns:
            matches = re.findall(pattern, content)
            references_found.extend(matches)

        if len(references_found) == 0:
            warnings.append("No legal references or citations found")

        return {
            'passed': True,  # Legal references are not required for all documents
            'warnings': warnings
        }

    def _validate_format_specific(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Perform format-specific validation."""
        doc_type = document.metadata.document_type

        if doc_type == DocumentType.PDF:
            return self._validate_pdf_specific(document)
        elif doc_type == DocumentType.DOCX:
            return self._validate_docx_specific(document)
        elif doc_type == DocumentType.HTML:
            return self._validate_html_specific(document)
        else:
            return {'passed': True, 'issues': [], 'warnings': []}

    def _validate_pdf_specific(self, document: ProcessedDocument) -> Dict[str, Any]:
        """PDF-specific validation."""
        issues = []

        # Check for scanned document indicators
        content = document.content
        if len(content.strip()) < 500:  # Very little text content
            issues.append("Document appears to be scanned or image-based PDF with minimal text")

        # Check for OCR artifacts
        ocr_artifacts = ['\n\n\n', '\t\t\t', '|||||']
        for artifact in ocr_artifacts:
            if artifact in content:
                issues.append("Document contains potential OCR artifacts")

        return {
            'passed': len(issues) == 0,
            'issues': issues
        }

    def _validate_docx_specific(self, document: ProcessedDocument) -> Dict[str, Any]:
        """DOCX-specific validation."""
        # For now, basic validation - could be expanded
        return {'passed': True, 'issues': [], 'warnings': []}

    def _validate_html_specific(self, document: ProcessedDocument) -> Dict[str, Any]:
        """HTML-specific validation."""
        content = document.content
        issues = []

        # Check for excessive HTML tags
        tag_count = len(re.findall(r'<[^>]+>', content))
        text_length = len(re.sub(r'<[^>]+>', '', content))

        if text_length > 0:
            tag_ratio = tag_count / text_length
            if tag_ratio > 0.1:  # More than 10% tags
                issues.append(f"High HTML tag ratio: {tag_ratio:.2%}")

        return {
            'passed': len(issues) == 0,
            'issues': issues
        }

    def _merge_validation_results(self, main_results: Dict[str, Any], new_results: Dict[str, Any]) -> None:
        """Merge validation results from different checks."""
        main_results['is_valid'] = main_results['is_valid'] and new_results.get('is_valid', True)
        main_results['checks_passed'] += new_results.get('checks_passed', 0)
        main_results['total_checks'] += new_results.get('total_checks', 0)
        main_results['issues'].extend(new_results.get('issues', []))
        main_results['warnings'].extend(new_results.get('warnings', []))

    def _calculate_overall_score(self, results: Dict[str, Any]) -> float:
        """Calculate overall validation score."""
        if results['total_checks'] == 0:
            return 1.0

        base_score = results['checks_passed'] / results['total_checks']

        # Penalty for issues
        issue_penalty = len(results['issues']) * 0.1
        warning_penalty = len(results['warnings']) * 0.02

        final_score = max(0.0, base_score - issue_penalty - warning_penalty)
        return min(1.0, final_score)

    def get_validation_summary(self, validation_results: Dict[str, Any]) -> str:
        """Generate a human-readable validation summary."""
        summary_lines = [
            f"Validation Summary:",
            f"Overall Score: {validation_results['overall_score']:.2f}",
            f"Status: {'Valid' if validation_results['is_valid'] else 'Invalid'}",
            f"Checks Passed: {validation_results['checks_passed']}/{validation_results['total_checks']}"
        ]

        if validation_results['issues']:
            summary_lines.append(f"Issues Found: {len(validation_results['issues'])}")
            for issue in validation_results['issues'][:5]:  # Show first 5 issues
                summary_lines.append(f"  - {issue}")

        if validation_results['warnings']:
            summary_lines.append(f"Warnings: {len(validation_results['warnings'])}")
            for warning in validation_results['warnings'][:3]:  # Show first 3 warnings
                summary_lines.append(f"  - {warning}")

        return "\n".join(summary_lines)