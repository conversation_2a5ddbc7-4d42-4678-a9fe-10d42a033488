"""
Document chunking strategies for AI Law Firm.

This module provides various strategies for chunking documents into smaller,
manageable pieces suitable for AI processing and vector embeddings.
"""

import re
import uuid
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from .types import DocumentChunk, ProcessedDocument
from utils.logging import get_logger


@dataclass
class ChunkingConfig:
    """Configuration for document chunking."""
    chunk_size: int = 1000
    chunk_overlap: int = 200
    preserve_paragraphs: bool = True
    preserve_sentences: bool = True
    min_chunk_size: int = 100
    max_chunk_size: int = 2000


class DocumentChunker(ABC):
    """
    Abstract base class for document chunking strategies.

    Different chunking strategies can be implemented to optimize for different
    use cases (e.g., semantic chunking, fixed-size chunking, etc.).
    """

    def __init__(self, config: ChunkingConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    @abstractmethod
    def chunk_document(self, document: ProcessedDocument) -> List[DocumentChunk]:
        """
        Chunk a document into smaller pieces.

        Args:
            document: The processed document to chunk

        Returns:
            List of document chunks
        """
        pass

    def _create_chunk(
        self,
        document_id: str,
        content: str,
        chunk_index: int,
        start_position: int = 0,
        end_position: int = 0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> DocumentChunk:
        """Create a document chunk with standard metadata."""
        return DocumentChunk(
            chunk_id=str(uuid.uuid4()),
            document_id=document_id,
            content=content.strip(),
            chunk_index=chunk_index,
            start_position=start_position,
            end_position=end_position,
            word_count=len(content.split()),
            character_count=len(content),
            metadata=metadata or {}
        )


class TextChunker(DocumentChunker):
    """
    Simple text-based chunking strategy.

    Splits documents into chunks of approximately equal size based on character count,
    with configurable overlap to maintain context.
    """

    def chunk_document(self, document: ProcessedDocument) -> List[DocumentChunk]:
        """
        Chunk document using fixed-size text chunks with overlap.

        Args:
            document: The processed document to chunk

        Returns:
            List of document chunks
        """
        content = document.content
        if not content:
            return []

        chunks = []
        content_length = len(content)
        chunk_index = 0
        position = 0

        while position < content_length:
            # Calculate chunk boundaries
            end_position = min(position + self.config.chunk_size, content_length)

            # If preserving paragraphs, try to find paragraph boundary
            if self.config.preserve_paragraphs:
                end_position = self._find_paragraph_boundary(content, position, end_position)

            # If preserving sentences, try to find sentence boundary
            elif self.config.preserve_sentences:
                end_position = self._find_sentence_boundary(content, position, end_position)

            # Extract chunk content
            chunk_content = content[position:end_position]

            # Skip empty chunks
            if not chunk_content.strip():
                position = end_position
                continue

            # Create chunk
            chunk = self._create_chunk(
                document_id=document.document_id,
                content=chunk_content,
                chunk_index=chunk_index,
                start_position=position,
                end_position=end_position,
                metadata={
                    "chunking_strategy": "text",
                    "chunk_size": self.config.chunk_size,
                    "overlap": self.config.chunk_overlap
                }
            )
            chunks.append(chunk)

            # Move position with overlap
            position = end_position - self.config.chunk_overlap
            if position <= chunk_index * self.config.chunk_size:
                # Prevent infinite loop
                position = end_position

            chunk_index += 1

        self.logger.info(f"Created {len(chunks)} text chunks for document {document.document_id}")
        return chunks

    def _find_paragraph_boundary(self, content: str, start: int, end: int) -> int:
        """Find the nearest paragraph boundary within the chunk."""
        # Look for double newlines (paragraph breaks)
        paragraph_pattern = r'\n\s*\n'
        search_end = min(end + 500, len(content))  # Look ahead up to 500 chars

        matches = list(re.finditer(paragraph_pattern, content[start:search_end]))

        if matches:
            # Find the last paragraph break before the desired end
            for match in reversed(matches):
                boundary = start + match.end()
                if boundary <= end + 100:  # Allow some flexibility
                    return boundary

        return end

    def _find_sentence_boundary(self, content: str, start: int, end: int) -> int:
        """Find the nearest sentence boundary within the chunk."""
        # Look for sentence endings
        sentence_pattern = r'[.!?]+\s+'
        search_end = min(end + 200, len(content))  # Look ahead up to 200 chars

        matches = list(re.finditer(sentence_pattern, content[start:search_end]))

        if matches:
            # Find the last sentence break before the desired end
            for match in reversed(matches):
                boundary = start + match.end()
                if boundary <= end + 50:  # Allow some flexibility
                    return boundary

        return end


class SemanticChunker(DocumentChunker):
    """
    Semantic chunking strategy using natural language understanding.

    Attempts to create chunks that preserve semantic meaning by analyzing
    document structure and content relationships.
    """

    def __init__(self, config: ChunkingConfig):
        super().__init__(config)
        self.section_patterns = [
            r'^#{1,6}\s+.+$',  # Markdown headers
            r'^\d+\.?\s+[A-Z]',  # Numbered sections
            r'^[A-Z][A-Z\s]{2,}$',  # All caps section headers
            r'^Article\s+\d+',  # Legal article references
            r'^Section\s+\d+',  # Legal section references
        ]

    def chunk_document(self, document: ProcessedDocument) -> List[DocumentChunk]:
        """
        Chunk document using semantic analysis.

        Args:
            document: The processed document to chunk

        Returns:
            List of document chunks
        """
        content = document.content
        if not content:
            return []

        # First, try to identify document sections
        sections = self._identify_sections(content)

        if sections:
            # Use section-based chunking
            return self._chunk_by_sections(document, sections)
        else:
            # Fall back to paragraph-based chunking
            return self._chunk_by_paragraphs(document)

    def _identify_sections(self, content: str) -> List[Tuple[str, int, int]]:
        """Identify document sections using pattern matching."""
        sections = []
        lines = content.split('\n')

        current_section = None
        current_start = 0

        for i, line in enumerate(lines):
            line_start = sum(len(lines[j]) + 1 for j in range(i))  # Character position

            # Check if line matches section pattern
            if self._is_section_header(line.strip()):
                # Save previous section if exists
                if current_section:
                    sections.append((current_section, current_start, line_start - 1))

                # Start new section
                current_section = line.strip()
                current_start = line_start
            elif current_section and line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                # Continue accumulating content for current section
                pass

        # Add final section
        if current_section:
            sections.append((current_section, current_start, len(content)))

        return sections

    def _is_section_header(self, line: str) -> bool:
        """Check if a line appears to be a section header."""
        if len(line) < 5 or len(line) > 100:
            return False

        # Check against section patterns
        for pattern in self.section_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                return True

        # Additional heuristics
        if line.isupper() and len(line.split()) <= 10:
            return True

        if line.startswith(('Chapter', 'Part', 'Division', 'Article', 'Section')):
            return True

        return False

    def _chunk_by_sections(self, document: ProcessedDocument, sections: List[Tuple[str, int, int]]) -> List[DocumentChunk]:
        """Chunk document by identified sections."""
        chunks = []

        for i, (section_title, start_pos, end_pos) in enumerate(sections):
            section_content = document.content[start_pos:end_pos]

            # If section is too large, further subdivide
            if len(section_content) > self.config.max_chunk_size:
                sub_chunks = self._subdivide_large_section(document, section_content, start_pos, i)
                chunks.extend(sub_chunks)
            else:
                chunk = self._create_chunk(
                    document_id=document.document_id,
                    content=section_content,
                    chunk_index=i,
                    start_position=start_pos,
                    end_position=end_pos,
                    metadata={
                        "chunking_strategy": "semantic_sections",
                        "section_title": section_title,
                        "is_section_header": True
                    }
                )
                chunks.append(chunk)

        self.logger.info(f"Created {len(chunks)} semantic chunks for document {document.document_id}")
        return chunks

    def _chunk_by_paragraphs(self, document: ProcessedDocument) -> List[DocumentChunk]:
        """Fallback chunking by paragraphs."""
        content = document.content
        paragraphs = re.split(r'\n\s*\n', content)

        chunks = []
        current_chunk = ""
        current_start = 0
        chunk_index = 0

        for i, paragraph in enumerate(paragraphs):
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # Calculate paragraph position
            if i == 0:
                para_start = 0
            else:
                para_start = content.find(paragraph, current_start)

            # Check if adding this paragraph would exceed chunk size
            if len(current_chunk + paragraph) > self.config.chunk_size and current_chunk:
                # Create chunk from current content
                chunk = self._create_chunk(
                    document_id=document.document_id,
                    content=current_chunk,
                    chunk_index=chunk_index,
                    start_position=current_start,
                    end_position=para_start,
                    metadata={
                        "chunking_strategy": "semantic_paragraphs",
                        "is_section_header": False
                    }
                )
                chunks.append(chunk)

                # Start new chunk
                current_chunk = paragraph
                current_start = para_start
                chunk_index += 1
            else:
                # Add paragraph to current chunk
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
                    current_start = para_start

        # Add final chunk
        if current_chunk:
            chunk = self._create_chunk(
                document_id=document.document_id,
                content=current_chunk,
                chunk_index=chunk_index,
                start_position=current_start,
                end_position=len(content),
                metadata={
                    "chunking_strategy": "semantic_paragraphs",
                    "is_section_header": False
                }
            )
            chunks.append(chunk)

        return chunks

    def _subdivide_large_section(
        self,
        document: ProcessedDocument,
        section_content: str,
        base_position: int,
        section_index: int
    ) -> List[DocumentChunk]:
        """Subdivide a large section into smaller chunks."""
        # Use text chunker for subdivision
        text_chunker = TextChunker(self.config)

        # Create temporary document for chunking
        temp_doc = ProcessedDocument(
            document_id=document.document_id,
            metadata=document.metadata,
            content=section_content
        )

        sub_chunks = text_chunker.chunk_document(temp_doc)

        # Adjust positions and metadata
        for i, chunk in enumerate(sub_chunks):
            chunk.start_position += base_position
            chunk.end_position += base_position
            chunk.chunk_index = section_index * 100 + i  # Ensure unique indices
            chunk.metadata["parent_section_index"] = section_index
            chunk.metadata["chunking_strategy"] = "semantic_subdivided"

        return sub_chunks


class LegalDocumentChunker(DocumentChunker):
    """
    Specialized chunker for legal documents.

    Uses legal document structure (articles, sections, clauses) to create
    semantically meaningful chunks.
    """

    def __init__(self, config: ChunkingConfig):
        super().__init__(config)
        self.legal_patterns = {
            "article": r'^Article\s+\d+',
            "section": r'^Section\s+\d+',
            "clause": r'^Clause\s+\d+',
            "subsection": r'^\(\d+\)',
            "paragraph": r'^\([a-z]\)',
        }

    def chunk_document(self, document: ProcessedDocument) -> List[DocumentChunk]:
        """
        Chunk legal document using legal structure.

        Args:
            document: The processed legal document to chunk

        Returns:
            List of document chunks
        """
        content = document.content

        # Try legal structure-based chunking first
        legal_sections = self._identify_legal_sections(content)

        if legal_sections:
            return self._chunk_legal_sections(document, legal_sections)
        else:
            # Fall back to semantic chunking
            semantic_chunker = SemanticChunker(self.config)
            return semantic_chunker.chunk_document(document)

    def _identify_legal_sections(self, content: str) -> List[Tuple[str, int, int]]:
        """Identify legal document sections."""
        sections = []
        lines = content.split('\n')

        current_section = None
        current_start = 0

        for i, line in enumerate(lines):
            line_start = sum(len(lines[j]) + 1 for j in range(i))

            # Check if line matches legal pattern
            section_match = self._match_legal_pattern(line.strip())
            if section_match:
                # Save previous section if exists
                if current_section:
                    sections.append((current_section, current_start, line_start - 1))

                # Start new section
                current_section = line.strip()
                current_start = line_start

        # Add final section
        if current_section:
            sections.append((current_section, current_start, len(content)))

        return sections

    def _match_legal_pattern(self, line: str) -> Optional[str]:
        """Match line against legal patterns."""
        for pattern_name, pattern in self.legal_patterns.items():
            if re.match(pattern, line, re.IGNORECASE):
                return pattern_name
        return None

    def _chunk_legal_sections(self, document: ProcessedDocument, sections: List[Tuple[str, int, int]]) -> List[DocumentChunk]:
        """Chunk document by legal sections."""
        chunks = []

        for i, (section_title, start_pos, end_pos) in enumerate(sections):
            section_content = document.content[start_pos:end_pos]

            # Create chunk for legal section
            chunk = self._create_chunk(
                document_id=document.document_id,
                content=section_content,
                chunk_index=i,
                start_position=start_pos,
                end_position=end_pos,
                metadata={
                    "chunking_strategy": "legal_structure",
                    "section_title": section_title,
                    "section_type": self._match_legal_pattern(section_title) or "unknown",
                    "is_legal_document": True
                }
            )
            chunks.append(chunk)

        self.logger.info(f"Created {len(chunks)} legal structure chunks for document {document.document_id}")
        return chunks