#!/usr/bin/env python3
"""
Advanced Document Search for AI Law Firm.

This module provides comprehensive search capabilities across:
- PostgreSQL document metadata
- MongoDB document content
- Redis cached search terms
- Qdrant vector similarity search
"""

import asyncio
import re
from typing import Dict, Any, List, Optional, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum

import asyncpg
import redis
from motor.motor_asyncio import AsyncIOMotorClient

from utils.logging import get_logger


class SearchType(Enum):
    """Types of search operations."""
    METADATA = "metadata"          # Search document metadata in PostgreSQL
    CONTENT = "content"            # Search document content in MongoDB
    SEMANTIC = "semantic"          # Vector similarity search in Qdrant
    HYBRID = "hybrid"              # Combined search across all sources


class SearchFilter(Enum):
    """Available search filters."""
    DATE_RANGE = "date_range"
    DOCUMENT_TYPE = "document_type"
    FILE_SIZE = "file_size"
    USER_ID = "user_id"
    ANALYSIS_TYPE = "analysis_type"


@dataclass
class SearchQuery:
    """Represents a search query with filters."""
    query: str
    search_type: SearchType = SearchType.HYBRID
    filters: Optional[Dict[str, Any]] = None
    limit: int = 20
    offset: int = 0
    sort_by: str = "relevance"
    sort_order: str = "desc"


@dataclass
class SearchResult:
    """Represents a single search result."""
    document_id: str
    filename: str
    relevance_score: float
    matched_content: str
    metadata: Dict[str, Any]
    source: str  # postgres, mongodb, qdrant
    highlights: List[str]


@dataclass
class SearchResponse:
    """Complete search response."""
    query: str
    total_results: int
    results: List[SearchResult]
    search_time_ms: float
    facets: Dict[str, Any]
    suggestions: List[str]


class AdvancedDocumentSearch:
    """
    Advanced document search engine for AI Law Firm.

    Features:
    - Multi-source search (PostgreSQL, MongoDB, Qdrant)
    - Advanced filtering and faceting
    - Search result highlighting
    - Query suggestions and spell correction
    - Performance monitoring and caching
    """

    def __init__(self, db_config: Dict[str, Any]):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.db_config = db_config

        # Database connections
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None
        self.mongo_db = None

        # Search cache
        self.search_cache_ttl = 300  # 5 minutes

    async def initialize(self):
        """Initialize database connections."""
        try:
            # PostgreSQL connection
            self.pg_pool = await asyncpg.create_pool(
                host=self.db_config.get('postgres_host', 'localhost'),
                port=self.db_config.get('postgres_port', 54320),
                database=self.db_config.get('postgres_db', 'ai_law_firm'),
                user=self.db_config.get('postgres_user', 'ai_law_user'),
                password=self.db_config.get('postgres_password', 'ai_law_password_2024'),
                min_size=1,
                max_size=10
            )

            # Redis connection
            self.redis_client = redis.Redis(
                host=self.db_config.get('redis_host', 'localhost'),
                port=self.db_config.get('redis_port', 63790),
                password=self.db_config.get('redis_password', 'ai_law_redis_password_2024'),
                decode_responses=True
            )

            # MongoDB connection
            mongo_uri = f"mongodb://{self.db_config.get('mongo_host', 'localhost')}:{self.db_config.get('mongo_port', 27019)}"
            self.mongo_client = AsyncIOMotorClient(mongo_uri)
            self.mongo_db = self.mongo_client.ai_law_firm

            self.logger.info("Advanced Document Search initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize search: {str(e)}")
            return False

    async def search(self, search_query: SearchQuery) -> SearchResponse:
        """
        Perform advanced document search.

        Args:
            search_query: SearchQuery object with search parameters

        Returns:
            SearchResponse with results and metadata
        """
        start_time = datetime.utcnow()

        try:
            # Check cache first
            cache_key = self._generate_cache_key(search_query)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                self.logger.info(f"Search cache hit for query: {search_query.query}")
                return cached_result

            # Perform search based on type
            if search_query.search_type == SearchType.METADATA:
                results = await self._search_metadata(search_query)
            elif search_query.search_type == SearchType.CONTENT:
                results = await self._search_content(search_query)
            elif search_query.search_type == SearchType.SEMANTIC:
                results = await self._search_semantic(search_query)
            else:  # HYBRID
                results = await self._search_hybrid(search_query)

            # Apply filters
            if search_query.filters:
                results = self._apply_filters(results, search_query.filters)

            # Sort results
            results = self._sort_results(results, search_query.sort_by, search_query.sort_order)

            # Apply pagination
            total_results = len(results)
            paginated_results = results[search_query.offset:search_query.offset + search_query.limit]

            # Generate facets
            facets = await self._generate_facets(results)

            # Generate suggestions
            suggestions = await self._generate_suggestions(search_query.query)

            # Calculate search time
            search_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            response = SearchResponse(
                query=search_query.query,
                total_results=total_results,
                results=paginated_results,
                search_time_ms=search_time,
                facets=facets,
                suggestions=suggestions
            )

            # Cache result
            self._cache_result(cache_key, response)

            # Update search analytics
            await self._update_search_analytics(search_query, response)

            return response

        except Exception as e:
            self.logger.error(f"Search error: {str(e)}")
            return SearchResponse(
                query=search_query.query,
                total_results=0,
                results=[],
                search_time_ms=0,
                facets={},
                suggestions=[]
            )

    async def _search_metadata(self, query: SearchQuery) -> List[SearchResult]:
        """Search document metadata in PostgreSQL."""
        try:
            async with self.pg_pool.acquire() as conn:
                # Build search query
                search_terms = query.query.lower().split()
                where_conditions = []
                params = []

                for i, term in enumerate(search_terms):
                    where_conditions.append(f"""
                        (LOWER(filename) LIKE ${i+1} OR
                         metadata->>'document_type' ILIKE ${i+1} OR
                         metadata->>'parties' ILIKE ${i+1})
                    """)
                    params.append(f"%{term}%")

                where_clause = " AND ".join(where_conditions) if where_conditions else "TRUE"

                # Execute search
                rows = await conn.fetch(f"""
                    SELECT
                        document_id,
                        filename,
                        content_type,
                        file_size,
                        upload_timestamp,
                        metadata
                    FROM documents
                    WHERE {where_clause}
                    ORDER BY upload_timestamp DESC
                    LIMIT 1000
                """, *params)

                results = []
                for row in rows:
                    # Parse metadata JSON if it's a string
                    metadata = row['metadata']
                    if isinstance(metadata, str):
                        import json
                        try:
                            metadata = json.loads(metadata)
                        except:
                            metadata = {}
                    elif metadata is None:
                        metadata = {}

                    # Calculate relevance score
                    relevance = self._calculate_relevance_score(query.query, row, metadata)

                    # Extract highlights
                    highlights = self._extract_highlights(query.query, row, metadata)

                    result = SearchResult(
                        document_id=str(row['document_id']),
                        filename=row['filename'],
                        relevance_score=relevance,
                        matched_content=f"Type: {metadata.get('document_type', 'Unknown')}",
                        metadata=metadata,
                        source="postgres",
                        highlights=highlights
                    )
                    results.append(result)

                return results

        except Exception as e:
            self.logger.error(f"Metadata search error: {str(e)}")
            return []

    async def _search_content(self, query: SearchQuery) -> List[SearchResult]:
        """Search document content in MongoDB."""
        try:
            # Build MongoDB text search query
            search_terms = query.query.split()
            mongo_query = {
                "$or": [
                    {"content": {"$regex": term, "$options": "i"}}
                    for term in search_terms
                ]
            }

            # Execute search
            cursor = self.mongo_db.documents.find(mongo_query).limit(1000)
            documents = await cursor.to_list(length=None)

            results = []
            for doc in documents:
                # Calculate relevance score
                relevance = self._calculate_content_relevance(query.query, doc)

                # Extract content highlights
                highlights = self._extract_content_highlights(query.query, doc.get('content', ''))

                result = SearchResult(
                    document_id=doc.get('document_id', str(doc.get('_id', ''))),
                    filename=doc.get('filename', 'Unknown'),
                    relevance_score=relevance,
                    matched_content=doc.get('content', '')[:200] + "..." if len(doc.get('content', '')) > 200 else doc.get('content', ''),
                    metadata=doc.get('metadata', {}),
                    source="mongodb",
                    highlights=highlights
                )
                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"Content search error: {str(e)}")
            return []

    async def _search_semantic(self, query: SearchQuery) -> List[SearchResult]:
        """Perform semantic search using Qdrant."""
        # Placeholder for Qdrant integration
        # This would use vector embeddings for semantic similarity
        self.logger.info("Semantic search not yet implemented - would use Qdrant")
        return []

    async def _search_hybrid(self, query: SearchQuery) -> List[SearchResult]:
        """Perform hybrid search across all sources."""
        # Execute all search types concurrently
        tasks = [
            self._search_metadata(query),
            self._search_content(query),
            self._search_semantic(query)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Combine and deduplicate results
        all_results = []
        seen_ids = set()

        for result_set in results:
            if isinstance(result_set, Exception):
                continue

            for result in result_set:
                if result.document_id not in seen_ids:
                    all_results.append(result)
                    seen_ids.add(result.document_id)

        # Re-rank combined results
        all_results.sort(key=lambda x: x.relevance_score, reverse=True)

        return all_results

    def _apply_filters(self, results: List[SearchResult], filters: Dict[str, Any]) -> List[SearchResult]:
        """Apply search filters to results."""
        filtered_results = []

        for result in results:
            include = True

            # Date range filter
            if 'date_from' in filters:
                if result.metadata.get('upload_timestamp'):
                    upload_date = datetime.fromisoformat(result.metadata['upload_timestamp'].replace('Z', '+00:00'))
                    if upload_date < filters['date_from']:
                        include = False

            if 'date_to' in filters:
                if result.metadata.get('upload_timestamp'):
                    upload_date = datetime.fromisoformat(result.metadata['upload_timestamp'].replace('Z', '+00:00'))
                    if upload_date > filters['date_to']:
                        include = False

            # Document type filter
            if 'document_type' in filters:
                if result.metadata.get('document_type') != filters['document_type']:
                    include = False

            # File size filter
            if 'min_size' in filters:
                # This would require file size info from metadata
                pass

            if include:
                filtered_results.append(result)

        return filtered_results

    def _sort_results(self, results: List[SearchResult], sort_by: str, sort_order: str) -> List[SearchResult]:
        """Sort search results."""
        reverse = sort_order == "desc"

        if sort_by == "relevance":
            results.sort(key=lambda x: x.relevance_score, reverse=reverse)
        elif sort_by == "date":
            results.sort(key=lambda x: x.metadata.get('upload_timestamp', ''), reverse=reverse)
        elif sort_by == "filename":
            results.sort(key=lambda x: x.filename, reverse=reverse)

        return results

    async def _generate_facets(self, results: List[SearchResult]) -> Dict[str, Any]:
        """Generate search facets for filtering."""
        facets = {
            "document_types": {},
            "date_ranges": {},
            "file_sizes": {}
        }

        for result in results:
            # Document type facet
            doc_type = result.metadata.get('document_type', 'Unknown')
            facets["document_types"][doc_type] = facets["document_types"].get(doc_type, 0) + 1

            # Date range facet
            if result.metadata.get('upload_timestamp'):
                try:
                    upload_date = datetime.fromisoformat(result.metadata['upload_timestamp'].replace('Z', '+00:00'))
                    month_key = upload_date.strftime("%Y-%m")
                    facets["date_ranges"][month_key] = facets["date_ranges"].get(month_key, 0) + 1
                except:
                    pass

        return facets

    async def _generate_suggestions(self, query: str) -> List[str]:
        """Generate search suggestions."""
        suggestions = []

        # Get popular search terms from Redis
        try:
            popular_terms = self.redis_client.zrevrange("search:terms", 0, 4, withscores=True)
            suggestions.extend([term for term, score in popular_terms if score > 1])
        except:
            pass

        # Add spell correction suggestions (simplified)
        if len(query.split()) == 1:
            # Simple spell correction logic could go here
            pass

        return suggestions[:5]  # Limit to 5 suggestions

    def _calculate_relevance_score(self, query: str, row, metadata: Dict[str, Any]) -> float:
        """Calculate relevance score for metadata search."""
        score = 0.0
        query_lower = query.lower()

        # Filename match
        if query_lower in row['filename'].lower():
            score += 1.0

        # Document type match
        doc_type = metadata.get('document_type', '').lower()
        if query_lower in doc_type:
            score += 0.8

        # Parties match
        parties = metadata.get('parties', [])
        for party in parties:
            if query_lower in party.lower():
                score += 0.6
                break

        return score

    def _calculate_content_relevance(self, query: str, doc: Dict[str, Any]) -> float:
        """Calculate relevance score for content search."""
        content = doc.get('content', '').lower()
        query_lower = query.lower()

        # Count occurrences of search terms
        term_count = sum(content.count(term.lower()) for term in query_lower.split())

        # Base score on term frequency
        score = min(term_count * 0.1, 1.0)

        # Boost for exact phrase matches
        if query_lower in content:
            score += 0.5

        return min(score, 1.0)

    def _extract_highlights(self, query: str, row, metadata: Dict[str, Any]) -> List[str]:
        """Extract highlighted text snippets."""
        highlights = []
        query_terms = query.lower().split()

        # Check filename
        filename_lower = row['filename'].lower()
        for term in query_terms:
            if term in filename_lower:
                highlights.append(f"Filename: {row['filename']}")
                break

        # Check metadata
        for key, value in metadata.items():
            if isinstance(value, str):
                value_lower = value.lower()
                for term in query_terms:
                    if term in value_lower:
                        highlights.append(f"{key.title()}: {value}")
                        break
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, str):
                        item_lower = item.lower()
                        for term in query_terms:
                            if term in item_lower:
                                highlights.append(f"{key.title()}: {item}")
                                break

        return highlights[:3]  # Limit highlights

    def _extract_content_highlights(self, query: str, content: str) -> List[str]:
        """Extract content highlights with context."""
        highlights = []
        query_terms = query.lower().split()
        content_lower = content.lower()

        for term in query_terms:
            # Find term in content
            term_index = content_lower.find(term)
            if term_index != -1:
                # Extract context around the term
                start = max(0, term_index - 50)
                end = min(len(content), term_index + len(term) + 50)
                highlight = content[start:end]
                if start > 0:
                    highlight = "..." + highlight
                if end < len(content):
                    highlight = highlight + "..."
                highlights.append(highlight)
                break

        return highlights[:3]

    def _generate_cache_key(self, query: SearchQuery) -> str:
        """Generate cache key for search query."""
        key_parts = [
            query.query,
            query.search_type.value,
            str(query.limit),
            str(query.offset),
            query.sort_by,
            query.sort_order
        ]

        if query.filters:
            key_parts.append(str(sorted(query.filters.items())))

        return f"search:{hash(''.join(key_parts))}"

    def _get_cached_result(self, cache_key: str) -> Optional[SearchResponse]:
        """Get cached search result."""
        try:
            cached = self.redis_client.get(cache_key)
            if cached:
                # Parse cached result (simplified)
                return None  # Would need proper deserialization
        except:
            pass
        return None

    def _cache_result(self, cache_key: str, response: SearchResponse):
        """Cache search result."""
        try:
            # Simplified caching - in production would serialize properly
            self.redis_client.setex(cache_key, self.search_cache_ttl, "cached")
        except:
            pass

    async def _update_search_analytics(self, query: SearchQuery, response: SearchResponse):
        """Update search analytics."""
        try:
            # Update search query count
            self.redis_client.incr("search:queries:total")

            # Update search terms popularity
            for term in query.query.split():
                self.redis_client.zincrby("search:terms", 1, term.lower())

            # Update search response times
            search_time_ms = response.search_time_ms
            self.redis_client.lpush("search:response_times", search_time_ms)
            self.redis_client.ltrim("search:response_times", 0, 99)  # Keep last 100

        except Exception as e:
            self.logger.error(f"Error updating search analytics: {str(e)}")

    async def close(self):
        """Close all database connections."""
        if self.pg_pool:
            await self.pg_pool.close()
        if self.redis_client:
            self.redis_client.close()
        if self.mongo_client:
            self.mongo_client.close()

        self.logger.info("Advanced Document Search connections closed")


# Convenience functions

async def search_documents(
    query: str,
    search_type: SearchType = SearchType.HYBRID,
    db_config: Dict[str, Any] = None,
    **kwargs
) -> SearchResponse:
    """Convenience function for document search."""
    if db_config is None:
        db_config = {
            'postgres_host': 'localhost',
            'postgres_port': 54320,
            'postgres_db': 'ai_law_firm',
            'postgres_user': 'ai_law_user',
            'postgres_password': 'ai_law_password_2024',
            'redis_host': 'localhost',
            'redis_port': 63790,
            'redis_password': 'ai_law_redis_password_2024',
            'mongo_host': 'localhost',
            'mongo_port': 27019
        }

    search_query = SearchQuery(
        query=query,
        search_type=search_type,
        **kwargs
    )

    search_engine = AdvancedDocumentSearch(db_config)
    if await search_engine.initialize():
        result = await search_engine.search(search_query)
        await search_engine.close()
        return result
    else:
        return SearchResponse(
            query=query,
            total_results=0,
            results=[],
            search_time_ms=0,
            facets={},
            suggestions=[]
        )


# AdvancedSearch class for backward compatibility
class AdvancedSearch:
    """
    AdvancedSearch class that wraps AdvancedDocumentSearch for backward compatibility.
    """

    def __init__(self, db_config: Dict[str, Any]):
        self.search_engine = AdvancedDocumentSearch(db_config)

    async def initialize(self):
        """Initialize the search engine."""
        return await self.search_engine.initialize()

    async def search(self, query: str, **kwargs):
        """Perform search."""
        from .advanced_search import SearchQuery, SearchType
        search_query = SearchQuery(query=query, **kwargs)
        return await self.search_engine.search(search_query)

    async def close(self):
        """Close connections."""
        await self.search_engine.close()


# Export key classes and functions
__all__ = [
    "AdvancedDocumentSearch",
    "AdvancedSearch",  # Added for backward compatibility
    "SearchQuery",
    "SearchResult",
    "SearchResponse",
    "SearchType",
    "SearchFilter",
    "search_documents"
]