"""
Legal Agent Router - Intelligent Routing System for 45 Specialized Legal Professionals.

This module provides intelligent routing of legal queries and documents to the most
appropriate specialized legal agents based on domain detection, agent availability,
expertise matching, and workload balancing.

Key Features:
- Dynamic agent selection based on document content
- Intelligent routing based on chat questions
- Load balancing across 45 specialized agents
- Expertise matching and availability scoring
- Real-time agent performance tracking
- Fallback and escalation mechanisms
"""
import asyncio
import time
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from .legal_domain_detector import LegalDomainDetector, LegalDomain, LegalAgentProfile, DomainDetectionResult
from .base import Agent, AgentResponse, AgentContext
from utils.logging import get_logger
from utils.exceptions import AgentNotFoundError, RoutingError


class RoutingStrategy(str, Enum):
    """Routing strategy enumeration."""
    LOAD_BALANCED = "load_balanced"
    EXPERTISE_MATCHED = "expertise_matched"
    AVAILABILITY_FIRST = "availability_first"
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"


class AgentLoadStatus(str, Enum):
    """Agent load status enumeration."""
    IDLE = "idle"
    LIGHT_LOAD = "light_load"
    MODERATE_LOAD = "moderate_load"
    HEAVY_LOAD = "heavy_load"
    OVERLOADED = "overloaded"


@dataclass
class AgentLoadMetrics:
    """Load metrics for an agent."""
    agent_id: str
    active_tasks: int = 0
    queue_length: int = 0
    average_response_time: float = 0.0
    success_rate: float = 1.0
    last_task_time: Optional[datetime] = None
    load_status: AgentLoadStatus = AgentLoadStatus.IDLE

    @property
    def load_score(self) -> float:
        """Calculate load score (0.0 = idle, 1.0 = overloaded)."""
        # Base score from active tasks and queue
        base_score = min(1.0, (self.active_tasks + self.queue_length * 0.5) / 5.0)

        # Adjust based on success rate (lower success = higher load)
        success_penalty = (1.0 - self.success_rate) * 0.3

        # Adjust based on response time (slower = higher load)
        time_penalty = min(0.2, self.average_response_time / 300.0)  # 5min threshold

        return min(1.0, base_score + success_penalty + time_penalty)


@dataclass
class RoutingDecision:
    """Decision made by the router."""
    query: str
    selected_agents: List[str]
    primary_agent: str
    routing_strategy: RoutingStrategy
    domain_detection: DomainDetectionResult
    confidence_score: float
    reasoning: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    estimated_completion_time: Optional[float] = None


@dataclass
class AgentPerformanceMetrics:
    """Performance metrics for agent evaluation."""
    agent_id: str
    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    average_response_time: float = 0.0
    average_quality_score: float = 0.0
    specialization_match_rate: float = 0.0
    client_satisfaction_score: float = 0.0
    last_updated: datetime = field(default_factory=datetime.utcnow)


class LegalAgentRouter:
    """
    Intelligent router for 45 specialized legal professionals.

    Features:
    - Domain-based agent selection
    - Load balancing and availability management
    - Performance tracking and optimization
    - Fallback and escalation mechanisms
    - Real-time routing decisions
    """

    def __init__(self, domain_detector: Optional[LegalDomainDetector] = None):
        self.domain_detector = domain_detector or LegalDomainDetector()
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Agent management
        self.agent_load_metrics: Dict[str, AgentLoadMetrics] = {}
        self.agent_performance: Dict[str, AgentPerformanceMetrics] = {}
        self.active_routes: Dict[str, RoutingDecision] = {}

        # Routing configuration
        self.routing_strategy = RoutingStrategy.EXPERTISE_MATCHED
        self.max_agents_per_query = 3
        self.load_threshold = 0.8  # 80% load before considering overloaded
        self.performance_update_interval = 300  # 5 minutes

        # Initialize agent metrics
        self._initialize_agent_metrics()

    def _initialize_agent_metrics(self):
        """Initialize load and performance metrics for all agents."""
        for agent in self.domain_detector.legal_agents:
            self.agent_load_metrics[agent.agent_id] = AgentLoadMetrics(agent_id=agent.agent_id)
            self.agent_performance[agent.agent_id] = AgentPerformanceMetrics(agent_id=agent.agent_id)

    async def route_query(
        self,
        query: str,
        context: Optional[AgentContext] = None,
        routing_strategy: Optional[RoutingStrategy] = None
    ) -> RoutingDecision:
        """
        Route a legal query to the most appropriate agents.

        Args:
            query: The legal query to route
            context: Optional context information
            routing_strategy: Optional routing strategy override

        Returns:
            RoutingDecision with selected agents and reasoning
        """
        start_time = time.time()

        try:
            # Detect legal domain
            domain_result = self.domain_detector.detect_domain(query, context)

            # Apply routing strategy
            strategy = routing_strategy or self.routing_strategy
            selected_agents = await self._select_agents(domain_result, strategy, context)

            if not selected_agents:
                raise AgentNotFoundError(f"No suitable agents found for query: {query[:100]}...")

            # Create routing decision
            decision = RoutingDecision(
                query=query,
                selected_agents=selected_agents,
                primary_agent=selected_agents[0],
                routing_strategy=strategy,
                domain_detection=domain_result,
                confidence_score=domain_result.confidence_score,
                reasoning=self._generate_routing_reasoning(domain_result, selected_agents, strategy),
                estimated_completion_time=self._estimate_completion_time(selected_agents)
            )

            # Record the routing decision
            route_id = f"route_{int(time.time())}_{hash(query) % 10000}"
            self.active_routes[route_id] = decision

            # Update agent loads
            for agent_id in selected_agents:
                if agent_id in self.agent_load_metrics:
                    self.agent_load_metrics[agent_id].queue_length += 1

            routing_time = time.time() - start_time
            self.logger.info(
                f"Query routed in {routing_time:.2f}s",
                extra={
                    "route_id": route_id,
                    "primary_domain": domain_result.primary_domain.value,
                    "selected_agents": len(selected_agents),
                    "confidence": domain_result.confidence_score
                }
            )

            return decision

        except Exception as e:
            routing_time = time.time() - start_time
            self.logger.error(f"Routing failed after {routing_time:.2f}s: {str(e)}")
            raise RoutingError(f"Failed to route query: {str(e)}")

    async def route_document(
        self,
        document_content: str,
        document_metadata: Optional[Dict] = None,
        routing_strategy: Optional[RoutingStrategy] = None
    ) -> RoutingDecision:
        """
        Route a legal document to the most appropriate agents.

        Args:
            document_content: The document content to analyze
            document_metadata: Optional document metadata
            routing_strategy: Optional routing strategy override

        Returns:
            RoutingDecision with selected agents and reasoning
        """
        # Create context from document metadata
        context = AgentContext(
            document_ids=["document_001"],  # Would be actual document ID
            metadata=document_metadata or {}
        )

        # Use document content as query for domain detection
        query = f"Analyze this legal document: {document_content[:1000]}..."

        return await self.route_query(query, context, routing_strategy)

    async def _select_agents(
        self,
        domain_result: DomainDetectionResult,
        strategy: RoutingStrategy,
        context: Optional[AgentContext] = None
    ) -> List[str]:
        """Select agents based on domain detection and routing strategy."""
        if strategy == RoutingStrategy.EXPERTISE_MATCHED:
            return self._select_by_expertise(domain_result)
        elif strategy == RoutingStrategy.LOAD_BALANCED:
            return self._select_by_load_balance(domain_result)
        elif strategy == RoutingStrategy.AVAILABILITY_FIRST:
            return self._select_by_availability(domain_result)
        elif strategy == RoutingStrategy.ROUND_ROBIN:
            return self._select_round_robin(domain_result)
        elif strategy == RoutingStrategy.LEAST_LOADED:
            return self._select_least_loaded(domain_result)
        else:
            return self._select_by_expertise(domain_result)  # Default fallback

    def _select_by_expertise(self, domain_result: DomainDetectionResult) -> List[str]:
        """Select agents based on expertise matching."""
        selected_agents = []

        # Primary domain agents
        primary_agents = self.domain_detector.get_agents_by_domain(domain_result.primary_domain)
        primary_agents.sort(key=lambda x: x.expertise_level == "expert", reverse=True)
        selected_agents.extend([agent.agent_id for agent in primary_agents[:2]])

        # Secondary domain agents
        for domain in domain_result.secondary_domains[:2]:  # Limit to 2 secondary domains
            secondary_agents = self.domain_detector.get_agents_by_domain(domain)
            if secondary_agents:
                # Take the top agent from each secondary domain
                top_agent = max(secondary_agents, key=lambda x: x.expertise_level == "expert")
                if top_agent.agent_id not in selected_agents:
                    selected_agents.append(top_agent.agent_id)

        # Limit to max agents per query
        return selected_agents[:self.max_agents_per_query]

    def _select_by_load_balance(self, domain_result: DomainDetectionResult) -> List[str]:
        """Select agents with load balancing consideration."""
        candidates = []

        # Get all available agents for the primary domain
        primary_agents = self.domain_detector.get_available_agents(domain_result.primary_domain)

        for agent in primary_agents:
            load_score = self.agent_load_metrics[agent.agent_id].load_score
            if load_score < self.load_threshold:
                candidates.append((agent, load_score))

        # Sort by load score (lowest first)
        candidates.sort(key=lambda x: x[1])

        # Take top candidates
        selected = [agent.agent_id for agent, _ in candidates[:self.max_agents_per_query]]

        # If not enough candidates, add from secondary domains
        if len(selected) < self.max_agents_per_query:
            for domain in domain_result.secondary_domains:
                secondary_agents = self.domain_detector.get_available_agents(domain)
                for agent in secondary_agents:
                    if agent.agent_id not in selected:
                        load_score = self.agent_load_metrics[agent.agent_id].load_score
                        if load_score < self.load_threshold:
                            selected.append(agent.agent_id)
                            if len(selected) >= self.max_agents_per_query:
                                break
                if len(selected) >= self.max_agents_per_query:
                    break

        return selected

    def _select_by_availability(self, domain_result: DomainDetectionResult) -> List[str]:
        """Select agents based on availability."""
        available_agents = self.domain_detector.get_available_agents(domain_result.primary_domain)
        available_agents.sort(key=lambda x: x.availability_score, reverse=True)

        selected = [agent.agent_id for agent in available_agents[:self.max_agents_per_query]]

        # Fill with secondary domain agents if needed
        if len(selected) < self.max_agents_per_query:
            for domain in domain_result.secondary_domains:
                secondary_available = self.domain_detector.get_available_agents(domain)
                for agent in secondary_available:
                    if agent.agent_id not in selected:
                        selected.append(agent.agent_id)
                        if len(selected) >= self.max_agents_per_query:
                            break
                if len(selected) >= self.max_agents_per_query:
                    break

        return selected

    def _select_round_robin(self, domain_result: DomainDetectionResult) -> List[str]:
        """Select agents using round-robin distribution."""
        # This would require tracking last assigned agent per domain
        # For simplicity, using availability-based selection
        return self._select_by_availability(domain_result)

    def _select_least_loaded(self, domain_result: DomainDetectionResult) -> List[str]:
        """Select the least loaded agents."""
        return self._select_by_load_balance(domain_result)

    def _generate_routing_reasoning(
        self,
        domain_result: DomainDetectionResult,
        selected_agents: List[str],
        strategy: RoutingStrategy
    ) -> str:
        """Generate reasoning for the routing decision."""
        reasoning_parts = []

        # Domain detection reasoning
        reasoning_parts.append(
            f"Detected primary domain: {domain_result.primary_domain.value} "
            f"(confidence: {domain_result.confidence_score:.2f})"
        )

        if domain_result.secondary_domains:
            secondary_names = [d.value for d in domain_result.secondary_domains]
            reasoning_parts.append(f"Secondary domains: {', '.join(secondary_names)}")

        # Strategy reasoning
        strategy_reasons = {
            RoutingStrategy.EXPERTISE_MATCHED: "Selected agents with highest expertise match",
            RoutingStrategy.LOAD_BALANCED: "Balanced load across available agents",
            RoutingStrategy.AVAILABILITY_FIRST: "Prioritized agent availability",
            RoutingStrategy.ROUND_ROBIN: "Distributed using round-robin assignment",
            RoutingStrategy.LEAST_LOADED: "Selected least loaded agents"
        }

        reasoning_parts.append(strategy_reasons.get(strategy, "Applied custom routing strategy"))

        # Agent selection reasoning
        agent_names = []
        for agent_id in selected_agents:
            agent = self.domain_detector.get_agent_by_id(agent_id)
            if agent:
                agent_names.append(f"{agent.name} ({agent.specialization})")

        reasoning_parts.append(f"Selected agents: {', '.join(agent_names)}")

        return ". ".join(reasoning_parts)

    def _estimate_completion_time(self, agent_ids: List[str]) -> float:
        """Estimate completion time based on agent performance."""
        if not agent_ids:
            return 0.0

        total_time = 0.0
        for agent_id in agent_ids:
            if agent_id in self.agent_performance:
                perf = self.agent_performance[agent_id]
                # Estimate based on average response time + queue time
                load_metric = self.agent_load_metrics.get(agent_id)
                queue_factor = load_metric.queue_length * 60 if load_metric else 0  # 1 min per queued task
                total_time += perf.average_response_time + queue_factor

        # Average across all agents
        return total_time / len(agent_ids) if agent_ids else 0.0

    def update_agent_performance(
        self,
        agent_id: str,
        task_success: bool,
        response_time: float,
        quality_score: Optional[float] = None
    ):
        """Update agent performance metrics."""
        if agent_id not in self.agent_performance:
            self.agent_performance[agent_id] = AgentPerformanceMetrics(agent_id=agent_id)

        perf = self.agent_performance[agent_id]
        perf.total_tasks += 1

        if task_success:
            perf.successful_tasks += 1
        else:
            perf.failed_tasks += 1

        # Update average response time
        if perf.total_tasks == 1:
            perf.average_response_time = response_time
        else:
            perf.average_response_time = (
                (perf.average_response_time * (perf.total_tasks - 1)) + response_time
            ) / perf.total_tasks

        # Update quality score if provided
        if quality_score is not None:
            if perf.total_tasks == 1:
                perf.average_quality_score = quality_score
            else:
                perf.average_quality_score = (
                    (perf.average_quality_score * (perf.total_tasks - 1)) + quality_score
                ) / perf.total_tasks

        perf.last_updated = datetime.utcnow()

        # Update load metrics
        if agent_id in self.agent_load_metrics:
            load_metric = self.agent_load_metrics[agent_id]
            load_metric.active_tasks = max(0, load_metric.active_tasks - 1)
            load_metric.average_response_time = perf.average_response_time
            load_metric.success_rate = perf.successful_tasks / perf.total_tasks if perf.total_tasks > 0 else 1.0

    def update_agent_load(self, agent_id: str, task_started: bool = True):
        """Update agent load when task starts or completes."""
        if agent_id not in self.agent_load_metrics:
            self.agent_load_metrics[agent_id] = AgentLoadMetrics(agent_id=agent_id)

        load_metric = self.agent_load_metrics[agent_id]

        if task_started:
            load_metric.active_tasks += 1
            load_metric.queue_length = max(0, load_metric.queue_length - 1)
        else:
            load_metric.active_tasks = max(0, load_metric.active_tasks - 1)

        load_metric.last_task_time = datetime.utcnow()

        # Update load status
        load_score = load_metric.load_score
        if load_score < 0.3:
            load_metric.load_status = AgentLoadStatus.IDLE
        elif load_score < 0.6:
            load_metric.load_status = AgentLoadStatus.LIGHT_LOAD
        elif load_score < 0.8:
            load_metric.load_status = AgentLoadStatus.MODERATE_LOAD
        elif load_score < 0.95:
            load_metric.load_status = AgentLoadStatus.HEAVY_LOAD
        else:
            load_metric.load_status = AgentLoadStatus.OVERLOADED

    def get_router_status(self) -> Dict:
        """Get comprehensive router status."""
        total_agents = len(self.domain_detector.legal_agents)
        active_routes = len(self.active_routes)

        # Calculate load distribution
        load_distribution = {}
        for agent_id, load_metric in self.agent_load_metrics.items():
            status = load_metric.load_status.value
            load_distribution[status] = load_distribution.get(status, 0) + 1

        # Get domain statistics
        domain_stats = self.domain_detector.get_domain_statistics()

        return {
            "total_agents": total_agents,
            "active_routes": active_routes,
            "routing_strategy": self.routing_strategy.value,
            "load_distribution": load_distribution,
            "domain_statistics": domain_stats,
            "average_load_score": sum(
                metric.load_score for metric in self.agent_load_metrics.values()
            ) / len(self.agent_load_metrics) if self.agent_load_metrics else 0.0,
            "total_queued_tasks": sum(
                metric.queue_length for metric in self.agent_load_metrics.values()
            ),
            "last_updated": datetime.utcnow().isoformat()
        }

    def set_routing_strategy(self, strategy: RoutingStrategy):
        """Update the routing strategy."""
        self.routing_strategy = strategy
        self.logger.info(f"Routing strategy updated to: {strategy.value}")

    def get_agent_recommendations(
        self,
        domain: LegalDomain,
        min_expertise: str = "junior",
        max_load: float = 0.8
    ) -> List[LegalAgentProfile]:
        """Get recommended agents for a specific domain with constraints."""
        agents = self.domain_detector.get_agents_by_domain(domain)

        # Filter by expertise level
        expertise_hierarchy = {"junior": 0, "senior": 1, "expert": 2, "principal": 3}
        min_level = expertise_hierarchy.get(min_expertise, 0)

        filtered_agents = [
            agent for agent in agents
            if expertise_hierarchy.get(agent.expertise_level, 0) >= min_level
        ]

        # Filter by load
        recommended = []
        for agent in filtered_agents:
            load_score = self.agent_load_metrics[agent.agent_id].load_score
            if load_score <= max_load:
                recommended.append(agent)

        # Sort by expertise and availability
        recommended.sort(key=lambda x: (
            expertise_hierarchy.get(x.expertise_level, 0),
            self.agent_load_metrics[x.agent_id].load_score
        ), reverse=True)

        return recommended[:5]  # Top 5 recommendations

    def escalate_route(self, route_id: str, escalation_reason: str) -> Optional[RoutingDecision]:
        """Escalate a routing decision to more senior agents."""
        if route_id not in self.active_routes:
            return None

        original_decision = self.active_routes[route_id]

        # Find more senior agents for the same domains
        escalated_agents = []

        for domain in [original_decision.domain_detection.primary_domain] + original_decision.domain_detection.secondary_domains:
            senior_agents = self.get_agent_recommendations(
                domain=domain,
                min_expertise="senior",
                max_load=0.9
            )

            # Add agents not in original selection
            for agent in senior_agents:
                if agent.agent_id not in original_decision.selected_agents:
                    escalated_agents.append(agent.agent_id)
                    if len(escalated_agents) >= 2:  # Add up to 2 escalated agents
                        break

        if escalated_agents:
            # Create escalated decision
            escalated_decision = RoutingDecision(
                query=original_decision.query,
                selected_agents=original_decision.selected_agents + escalated_agents,
                primary_agent=escalated_agents[0],
                routing_strategy=original_decision.routing_strategy,
                domain_detection=original_decision.domain_detection,
                confidence_score=original_decision.confidence_score,
                reasoning=f"{original_decision.reasoning}. Escalated due to: {escalation_reason}",
                estimated_completion_time=self._estimate_completion_time(escalated_agents)
            )

            # Update the route
            self.active_routes[route_id] = escalated_decision

            self.logger.info(f"Route {route_id} escalated with {len(escalated_agents)} additional agents")
            return escalated_decision

        return None

    def cleanup_completed_routes(self, max_age_hours: int = 24):
        """Clean up old completed routes."""
        cutoff_time = datetime.utcnow().timestamp() - (max_age_hours * 3600)

        routes_to_remove = []
        for route_id, decision in self.active_routes.items():
            if decision.timestamp.timestamp() < cutoff_time:
                routes_to_remove.append(route_id)

        for route_id in routes_to_remove:
            del self.active_routes[route_id]

        if routes_to_remove:
            self.logger.info(f"Cleaned up {len(routes_to_remove)} old routes")