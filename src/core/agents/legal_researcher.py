"""
Legal Researcher Agent for AI Law Firm.

This agent specializes in legal research, case law analysis, and
statutory interpretation to support legal analysis and decision-making.
"""

import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base import Agent, AgentRole, AgentCapability, AgentResponse, AgentContext
from infrastructure.ai_providers.base import AIProvider
from utils.logging import get_logger


class LegalResearcher(Agent):
    """
    Specialized legal research agent.

    Capabilities:
    - Case law research and analysis
    - Statutory interpretation
    - Legal precedent identification
    - Citation validation
    - Research synthesis and summarization
    """

    def __init__(
        self,
        name: str,
        ai_provider: AIProvider,
        knowledge_base: Optional[Any] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        capabilities = [
            AgentCapability.LEGAL_RESEARCH,
            AgentCapability.DOCUMENT_ANALYSIS,
            AgentCapability.RISK_ASSESSMENT
        ]

        super().__init__(
            name=name,
            role=AgentRole.LEGAL_RESEARCHER,
            capabilities=capabilities,
            ai_provider=ai_provider,
            knowledge_base=knowledge_base,
            config=config
        )

        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.research_templates = self._initialize_research_templates()

    async def initialize(self) -> bool:
        """Initialize the legal researcher agent."""
        try:
            self.logger.info(f"Initializing Legal Researcher: {self.name}")

            # Validate AI provider capabilities
            if not hasattr(self.ai_provider, 'generate_response'):
                self.logger.error("AI provider does not support text generation")
                return False

            # Initialize research databases and tools
            self.research_tools = self._initialize_research_tools()

            self.is_initialized = True
            self.logger.info(f"Legal Researcher {self.name} initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Legal Researcher: {str(e)}")
            return False

    async def process_query(
        self,
        query: str,
        context: Optional[AgentContext] = None
    ) -> AgentResponse:
        """Process a legal research query."""
        self.update_activity()

        try:
            # Determine research type and strategy
            research_type = self._classify_research_query(query)
            research_strategy = self._select_research_strategy(research_type, context)

            # Perform research
            research_results = await self._perform_research(query, research_type, research_strategy, context)

            # Synthesize findings
            synthesis = await self._synthesize_research_findings(research_results, query)

            # Generate response
            response_content = self._format_research_response(synthesis, research_results)

            # Calculate confidence and metadata
            confidence_score = self._calculate_research_confidence(research_results)
            metadata = self._generate_research_metadata(research_results, research_type)

            return AgentResponse(
                content=response_content,
                confidence_score=confidence_score,
                metadata=metadata,
                sources=research_results.get('sources', []),
                processing_time=0.0,  # Will be set by caller
                model_used=getattr(self.ai_provider, 'model', 'unknown')
            )

        except Exception as e:
            self.logger.error(f"Research query processing failed: {str(e)}")
            return AgentResponse(
                content=f"I apologize, but I encountered an error while processing your research query: {str(e)}",
                confidence_score=0.0,
                metadata={"error": str(e), "error_type": type(e).__name__},
                sources=[],
                processing_time=0.0
            )

    def can_handle_query(self, query: str, context: Optional[AgentContext] = None) -> bool:
        """Determine if this agent can handle the given query."""
        research_indicators = [
            'research', 'case law', 'precedent', 'statute', 'citation',
            'legal authority', 'court decision', 'judicial opinion',
            'find cases', 'legal analysis', 'interpret', 'statutory'
        ]

        query_lower = query.lower()
        return any(indicator in query_lower for indicator in research_indicators)

    def _initialize_research_templates(self) -> Dict[str, str]:
        """Initialize research query templates."""
        return {
            'case_law': """
            Conduct comprehensive case law research on: {query}

            Focus Areas:
            1. Relevant court decisions and precedents
            2. Key legal principles established
            3. Current status of cited cases
            4. Conflicting or distinguishing opinions
            5. Geographic jurisdiction considerations

            Provide:
            - Case citations with full references
            - Summary of holdings and reasoning
            - Relevance assessment
            - Additional research suggestions
            """,

            'statutory': """
            Analyze statutory law regarding: {query}

            Include:
            1. Relevant statutes and codes
            2. Current amendments and effective dates
            3. Regulatory interpretations
            4. Related administrative rules
            5. Potential conflicts or overlaps

            Structure response with:
            - Full statutory citations
            - Text of relevant provisions
            - Official interpretations
            - Practical implications
            """,

            'general_research': """
            Perform legal research on: {query}

            Methodology:
            1. Identify primary legal sources
            2. Review secondary authorities
            3. Analyze current developments
            4. Assess practical implications
            5. Identify knowledge gaps

            Deliver comprehensive analysis with citations and recommendations.
            """
        }

    def _initialize_research_tools(self) -> Dict[str, Any]:
        """Initialize research tools and databases."""
        return {
            'citation_parser': self._parse_legal_citations,
            'case_finder': self._find_related_cases,
            'statute_retriever': self._retrieve_statutes,
            'authority_validator': self._validate_authority
        }

    def _classify_research_query(self, query: str) -> str:
        """Classify the type of research query."""
        query_lower = query.lower()

        if any(term in query_lower for term in ['case', 'precedent', 'court', 'opinion', 'judgment']):
            return 'case_law'
        elif any(term in query_lower for term in ['statute', 'code', 'section', 'chapter', 'act']):
            return 'statutory'
        elif any(term in query_lower for term in ['interpret', 'meaning', 'definition', 'scope']):
            return 'interpretive'
        else:
            return 'general_research'

    def _select_research_strategy(self, research_type: str, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Select appropriate research strategy based on query type and context."""
        base_strategy = {
            'depth': 'comprehensive',
            'sources': ['primary', 'secondary'],
            'methodology': 'systematic',
            'validation': True
        }

        # Adjust strategy based on research type
        if research_type == 'case_law':
            base_strategy.update({
                'focus': 'judicial_precedent',
                'geographic_scope': 'national',
                'temporal_scope': 'current_plus_history'
            })
        elif research_type == 'statutory':
            base_strategy.update({
                'focus': 'legislative_text',
                'geographic_scope': 'jurisdiction_specific',
                'temporal_scope': 'current'
            })

        # Adjust based on context
        if context and context.metadata:
            if context.metadata.get('urgency') == 'high':
                base_strategy['depth'] = 'focused'
            if context.metadata.get('jurisdiction'):
                base_strategy['geographic_scope'] = context.metadata['jurisdiction']

        return base_strategy

    async def _perform_research(
        self,
        query: str,
        research_type: str,
        strategy: Dict[str, Any],
        context: Optional[AgentContext]
    ) -> Dict[str, Any]:
        """Perform the actual research using AI and knowledge base."""
        # Prepare research prompt
        template = self.research_templates.get(research_type, self.research_templates['general_research'])
        research_prompt = template.format(query=query)

        # Add context information
        if context:
            research_prompt += f"\n\nContext: {context.query or 'General legal research'}"
            if context.document_ids:
                research_prompt += f"\nRelated Documents: {', '.join(context.document_ids)}"

        # Execute research using AI provider
        research_response = await self.ai_provider.generate_response(
            prompt=research_prompt,
            context=None,  # Could include relevant documents
            temperature=0.1,  # Low temperature for factual research
            max_tokens=2000
        )

        # Extract sources and citations
        sources = self._extract_sources_from_response(research_response.content)

        # Validate findings if requested
        if strategy.get('validation', True):
            validation_results = await self._validate_research_findings(research_response.content, sources)
        else:
            validation_results = {}

        return {
            'query': query,
            'research_type': research_type,
            'strategy': strategy,
            'findings': research_response.content,
            'sources': sources,
            'validation': validation_results,
            'confidence_indicators': self._assess_research_quality(research_response.content, sources)
        }

    async def _synthesize_research_findings(
        self,
        research_results: Dict[str, Any],
        original_query: str
    ) -> Dict[str, Any]:
        """Synthesize research findings into coherent analysis."""
        synthesis_prompt = f"""
        Synthesize the following legal research findings into a comprehensive analysis:

        Original Query: {original_query}
        Research Findings: {research_results['findings']}

        Please provide:
        1. Executive summary of key findings
        2. Detailed analysis organized by topic
        3. Strength of authority assessment
        4. Practical implications
        5. Recommendations for further research if needed

        Structure the response professionally and cite sources appropriately.
        """

        synthesis_response = await self.ai_provider.generate_response(
            prompt=synthesis_prompt,
            temperature=0.2,
            max_tokens=1500
        )

        return {
            'synthesis': synthesis_response.content,
            'key_findings': self._extract_key_findings(synthesis_response.content),
            'recommendations': self._extract_recommendations(synthesis_response.content)
        }

    def _format_research_response(
        self,
        synthesis: Dict[str, Any],
        research_results: Dict[str, Any]
    ) -> str:
        """Format the final research response."""
        response_parts = []

        # Add synthesis
        response_parts.append(synthesis['synthesis'])

        # Add source summary
        if research_results.get('sources'):
            response_parts.append("\n## Sources Consulted")
            for i, source in enumerate(research_results['sources'][:10], 1):  # Limit to top 10
                response_parts.append(f"{i}. {source.get('title', 'Untitled Source')}")

        # Add methodology note
        response_parts.append(f"\n## Research Methodology")
        response_parts.append(f"Research Type: {research_results.get('research_type', 'General').replace('_', ' ').title()}")
        response_parts.append(f"Sources Analyzed: {len(research_results.get('sources', []))}")
        response_parts.append(f"Research Strategy: {research_results.get('strategy', {}).get('depth', 'Comprehensive')}")

        return "\n\n".join(response_parts)

    def _calculate_research_confidence(self, research_results: Dict[str, Any]) -> float:
        """Calculate confidence score for research results."""
        base_confidence = 0.7  # Base confidence for legal research

        # Factors affecting confidence
        factors = {
            'source_count': len(research_results.get('sources', [])),
            'validation_passed': bool(research_results.get('validation')),
            'quality_indicators': research_results.get('confidence_indicators', {})
        }

        # Adjust confidence based on factors
        if factors['source_count'] > 5:
            base_confidence += 0.1
        elif factors['source_count'] == 0:
            base_confidence -= 0.3

        if factors['validation_passed']:
            base_confidence += 0.1

        # Cap between 0 and 1
        return max(0.0, min(1.0, base_confidence))

    def _generate_research_metadata(
        self,
        research_results: Dict[str, Any],
        research_type: str
    ) -> Dict[str, Any]:
        """Generate metadata for research response."""
        return {
            'research_type': research_type,
            'sources_count': len(research_results.get('sources', [])),
            'research_strategy': research_results.get('strategy', {}),
            'validation_performed': bool(research_results.get('validation')),
            'key_findings_count': len(research_results.get('key_findings', [])),
            'processing_timestamp': datetime.utcnow().isoformat(),
            'agent_specialization': 'legal_research'
        }

    def _extract_sources_from_response(self, content: str) -> List[Dict[str, Any]]:
        """Extract legal sources and citations from response content."""
        sources = []

        # Extract case citations (basic pattern)
        case_pattern = r'(\d+)\s+([A-Za-z]+)\s+(?:App\.?\s+)?(\d+)'
        case_matches = re.findall(case_pattern, content)

        for match in case_matches:
            volume, reporter, page = match
            citation = f"{volume} {reporter} {page}"
            sources.append({
                'type': 'case_citation',
                'citation': citation,
                'title': f"Case: {citation}",
                'authority_type': 'judicial'
            })

        # Extract statutory citations
        statute_pattern = r'(\d+)\s+U\.S\.C\.?\s+§?\s*(\d+)'
        statute_matches = re.findall(statute_pattern, content)

        for match in statute_matches:
            title, section = match
            citation = f"{title} U.S.C. § {section}"
            sources.append({
                'type': 'statutory_citation',
                'citation': citation,
                'title': f"Statute: {citation}",
                'authority_type': 'legislative'
            })

        return sources

    async def _validate_research_findings(
        self,
        findings: str,
        sources: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Validate research findings and sources."""
        validation_prompt = f"""
        Validate the following legal research findings for accuracy and reliability:

        Findings: {findings[:1000]}...  # Truncate for validation

        Sources: {[s.get('citation', '') for s in sources]}

        Assess:
        1. Citation accuracy
        2. Source reliability
        3. Logical consistency
        4. Current validity
        5. Completeness of analysis

        Provide validation score (0-1) and specific issues if any.
        """

        validation_response = await self.ai_provider.generate_response(
            prompt=validation_prompt,
            temperature=0.0,  # Deterministic validation
            max_tokens=500
        )

        return {
            'validation_response': validation_response.content,
            'is_validated': True,
            'validation_timestamp': datetime.utcnow().isoformat()
        }

    def _assess_research_quality(self, content: str, sources: List[Dict[str, Any]]) -> Dict[str, float]:
        """Assess the quality of research results."""
        quality_indicators = {}

        # Source diversity
        source_types = set(s.get('type', '') for s in sources)
        quality_indicators['source_diversity'] = min(1.0, len(source_types) / 3.0)

        # Citation density
        citation_count = len(re.findall(r'\d+\s+[A-Za-z]+\s+\d+', content))
        quality_indicators['citation_density'] = min(1.0, citation_count / 10.0)

        # Content length (proxy for thoroughness)
        content_length = len(content.split())
        quality_indicators['content_comprehensiveness'] = min(1.0, content_length / 1000.0)

        return quality_indicators

    def _extract_key_findings(self, synthesis: str) -> List[str]:
        """Extract key findings from synthesis."""
        # Simple extraction - could be enhanced with NLP
        lines = synthesis.split('\n')
        findings = []

        for line in lines:
            line = line.strip()
            if line and len(line) > 20 and not line.startswith('#'):
                findings.append(line)

        return findings[:5]  # Return top 5 findings

    def _extract_recommendations(self, synthesis: str) -> List[str]:
        """Extract recommendations from synthesis."""
        # Look for recommendation patterns
        recommendation_patterns = [
            r'recommend',
            r'suggest',
            r'consider',
            r'advice',
            r'further research'
        ]

        recommendations = []
        lines = synthesis.split('\n')

        for line in lines:
            line_lower = line.lower()
            if any(pattern in line_lower for pattern in recommendation_patterns):
                recommendations.append(line.strip())

        return recommendations[:3]  # Return top 3 recommendations

    # Research tool implementations
    def _parse_legal_citations(self, text: str) -> List[Dict[str, Any]]:
        """Parse legal citations from text."""
        # Implementation would use citation parsing libraries
        return []

    def _find_related_cases(self, query: str) -> List[Dict[str, Any]]:
        """Find related cases (would integrate with legal databases)."""
        return []

    def _retrieve_statutes(self, citation: str) -> Optional[Dict[str, Any]]:
        """Retrieve statute text (would integrate with legislative databases)."""
        return None

    def _validate_authority(self, citation: str) -> bool:
        """Validate legal authority citation."""
        # Basic validation - would be enhanced with actual validation services
        return bool(re.match(r'\d+\s+[A-Za-z]+\s+\d+', citation))