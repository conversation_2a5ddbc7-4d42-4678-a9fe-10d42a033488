"""
Intelligent Model Selection System for AI Law Firm Agents.

This module provides sophisticated model selection capabilities for each of the 45
specialized legal agents, allowing them to dynamically choose the optimal AI model
based on task requirements, cost constraints, speed needs, and quality priorities.

Model Selection Strategies:
- Best Answer: Prioritizes quality for complex legal analysis
- Least Cost: Minimizes API costs while maintaining acceptable quality
- Speed: Fastest response for time-sensitive queries
- Balanced: Optimal quality-cost-speed trade-off
- Adaptive: Learns from performance and user feedback

Supported Models:
- OpenAI: GPT-4o, GPT-4o-mini, GPT-4, GPT-3.5-turbo
- Anthropic: Claude-3-opus, Claude-3-sonnet, Claude-3-haiku
- Local: Ollama models (Llama, Mistral, etc.)
- Specialized: Legal-specific fine-tuned models
"""
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from core.agents.base import ModelSelectionStrategy
from utils.logging import get_logger


class ModelProvider(str, Enum):
    """Available AI model providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"
    HUGGINGFACE = "huggingface"
    SPECIALIZED = "specialized"


class ModelCapability(str, Enum):
    """Model capabilities and specializations."""
    GENERAL_LEGAL = "general_legal"
    CONTRACT_ANALYSIS = "contract_analysis"
    CASE_LAW_RESEARCH = "case_law_research"
    REGULATORY_COMPLIANCE = "regulatory_compliance"
    LITIGATION_SUPPORT = "litigation_support"
    CORPORATE_TRANSACTIONS = "corporate_transactions"
    INTELLECTUAL_PROPERTY = "intellectual_property"
    EMPLOYMENT_LAW = "employment_law"
    TAX_LAW = "tax_law"
    FAMILY_LAW = "family_law"
    CRIMINAL_LAW = "criminal_law"


@dataclass
class ModelProfile:
    """Profile of an AI model with performance characteristics."""
    provider: ModelProvider
    model_name: str
    display_name: str
    capabilities: List[ModelCapability]
    context_window: int
    average_response_time: float  # seconds
    cost_per_1k_tokens: float     # USD
    quality_score: float          # 0.0 to 1.0
    specialization_score: float   # 0.0 to 1.0 (domain expertise)
    reliability_score: float      # 0.0 to 1.0
    last_updated: datetime = field(default_factory=datetime.utcnow)

    @property
    def efficiency_score(self) -> float:
        """Calculate efficiency score (quality per cost)."""
        if self.cost_per_1k_tokens == 0:
            return self.quality_score  # Free models get quality score
        return self.quality_score / self.cost_per_1k_tokens

    @property
    def speed_score(self) -> float:
        """Calculate speed score (inverse of response time)."""
        return 1.0 / max(self.average_response_time, 0.1)

    @property
    def overall_score(self) -> float:
        """Calculate overall model score."""
        return (
            self.quality_score * 0.4 +
            self.efficiency_score * 0.3 +
            self.speed_score * 0.2 +
            self.reliability_score * 0.1
        )


@dataclass
class ModelSelectionCriteria:
    """Criteria for model selection."""
    strategy: ModelSelectionStrategy
    required_capabilities: List[ModelCapability] = field(default_factory=list)
    max_cost_per_1k: Optional[float] = None
    max_response_time: Optional[float] = None
    min_quality_score: Optional[float] = None
    preferred_providers: List[ModelProvider] = field(default_factory=list)
    task_complexity: str = "medium"  # simple, medium, complex
    time_sensitivity: str = "normal"  # low, normal, high
    cost_sensitivity: str = "normal"  # low, normal, high


@dataclass
class ModelSelectionResult:
    """Result of model selection process."""
    selected_model: ModelProfile
    selection_reasoning: str
    expected_cost: float
    expected_time: float
    confidence_score: float
    alternative_models: List[ModelProfile] = field(default_factory=list)
    selection_timestamp: datetime = field(default_factory=datetime.utcnow)


class IntelligentModelSelector:
    """
    Intelligent model selector that optimizes model choice based on multiple factors.

    Features:
    - Dynamic model selection based on strategy (Best Answer, Least Cost, Speed)
    - Real-time performance tracking and learning
    - Cost optimization with quality maintenance
    - Speed optimization for time-sensitive tasks
    - Adaptive learning from agent performance
    - Multi-provider support with failover
    """

    def __init__(self):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.models = self._initialize_model_profiles()
        self.performance_history = {}
        self.selection_history = []

    def _initialize_model_profiles(self) -> Dict[str, ModelProfile]:
        """Initialize comprehensive model profiles with current performance data."""
        return {
            # OpenAI Models
            "gpt-5-nano": ModelProfile(
                provider=ModelProvider.OPENAI,
                model_name="gpt-5-nano",
                display_name="GPT-5 Nano (Ultra-Efficient)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.REGULATORY_COMPLIANCE
                ],
                context_window=128000,
                average_response_time=1.2,
                cost_per_1k_tokens=0.00005,
                quality_score=0.82,
                specialization_score=0.78,
                reliability_score=0.94
            ),
            "gpt-5-mini": ModelProfile(
                provider=ModelProvider.OPENAI,
                model_name="gpt-5-mini",
                display_name="GPT-5 Mini (Next-Gen Efficient)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.CASE_LAW_RESEARCH,
                    ModelCapability.REGULATORY_COMPLIANCE
                ],
                context_window=256000,
                average_response_time=1.5,
                cost_per_1k_tokens=0.0001,
                quality_score=0.90,
                specialization_score=0.87,
                reliability_score=0.96
            ),
            "gpt-5": ModelProfile(
                provider=ModelProvider.OPENAI,
                model_name="gpt-5",
                display_name="GPT-5 (Flagship)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.CASE_LAW_RESEARCH,
                    ModelCapability.LITIGATION_SUPPORT,
                    ModelCapability.CORPORATE_TRANSACTIONS,
                    ModelCapability.INTELLECTUAL_PROPERTY,
                    ModelCapability.EMPLOYMENT_LAW
                ],
                context_window=512000,
                average_response_time=2.8,
                cost_per_1k_tokens=0.002,
                quality_score=0.97,
                specialization_score=0.93,
                reliability_score=0.98
            ),
            "gpt-4o": ModelProfile(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4o",
                display_name="GPT-4o (Latest)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.CASE_LAW_RESEARCH,
                    ModelCapability.LITIGATION_SUPPORT,
                    ModelCapability.CORPORATE_TRANSACTIONS
                ],
                context_window=128000,
                average_response_time=3.2,
                cost_per_1k_tokens=0.005,
                quality_score=0.95,
                specialization_score=0.90,
                reliability_score=0.98
            ),
            "gpt-4o-mini": ModelProfile(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4o-mini",
                display_name="GPT-4o Mini (Fast)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.REGULATORY_COMPLIANCE
                ],
                context_window=128000,
                average_response_time=1.8,
                cost_per_1k_tokens=0.00015,
                quality_score=0.88,
                specialization_score=0.85,
                reliability_score=0.96
            ),
            "gpt-4": ModelProfile(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4",
                display_name="GPT-4 (High Quality)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.LITIGATION_SUPPORT,
                    ModelCapability.CORPORATE_TRANSACTIONS,
                    ModelCapability.INTELLECTUAL_PROPERTY
                ],
                context_window=8192,
                average_response_time=4.5,
                cost_per_1k_tokens=0.0125,
                quality_score=0.97,
                specialization_score=0.95,
                reliability_score=0.99
            ),

            # Anthropic Models
            "claude-3-opus": ModelProfile(
                provider=ModelProvider.ANTHROPIC,
                model_name="claude-3-opus-20240229",
                display_name="Claude 3 Opus (Best Quality)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.CASE_LAW_RESEARCH,
                    ModelCapability.LITIGATION_SUPPORT,
                    ModelCapability.EMPLOYMENT_LAW,
                    ModelCapability.INTELLECTUAL_PROPERTY
                ],
                context_window=200000,
                average_response_time=4.8,
                cost_per_1k_tokens=0.015,
                quality_score=0.96,
                specialization_score=0.92,
                reliability_score=0.97
            ),
            "claude-3-sonnet": ModelProfile(
                provider=ModelProvider.ANTHROPIC,
                model_name="claude-3-sonnet-20240229",
                display_name="Claude 3 Sonnet (Balanced)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.REGULATORY_COMPLIANCE,
                    ModelCapability.CORPORATE_TRANSACTIONS
                ],
                context_window=200000,
                average_response_time=3.1,
                cost_per_1k_tokens=0.008,
                quality_score=0.93,
                specialization_score=0.88,
                reliability_score=0.98
            ),
            "claude-3-haiku": ModelProfile(
                provider=ModelProvider.ANTHROPIC,
                model_name="claude-3-haiku-20240307",
                display_name="Claude 3 Haiku (Fast)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.REGULATORY_COMPLIANCE
                ],
                context_window=200000,
                average_response_time=2.2,
                cost_per_1k_tokens=0.00025,
                quality_score=0.85,
                specialization_score=0.80,
                reliability_score=0.95
            ),

            # Local Ollama Models (Free)
            "llama3.2:3b": ModelProfile(
                provider=ModelProvider.OLLAMA,
                model_name="llama3.2:3b",
                display_name="Llama 3.2 3B (Local)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS
                ],
                context_window=32768,
                average_response_time=8.5,
                cost_per_1k_tokens=0.0,  # Free
                quality_score=0.75,
                specialization_score=0.70,
                reliability_score=0.90
            ),
            "mistral:7b": ModelProfile(
                provider=ModelProvider.OLLAMA,
                model_name="mistral:7b",
                display_name="Mistral 7B (Local)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CASE_LAW_RESEARCH
                ],
                context_window=32768,
                average_response_time=6.2,
                cost_per_1k_tokens=0.0,  # Free
                quality_score=0.78,
                specialization_score=0.75,
                reliability_score=0.92
            ),

            # Specialized Legal Models (Hypothetical)
            "legal-gpt-4": ModelProfile(
                provider=ModelProvider.SPECIALIZED,
                model_name="legal-gpt-4",
                display_name="Legal GPT-4 (Fine-tuned)",
                capabilities=[
                    ModelCapability.GENERAL_LEGAL,
                    ModelCapability.CONTRACT_ANALYSIS,
                    ModelCapability.CASE_LAW_RESEARCH,
                    ModelCapability.LITIGATION_SUPPORT,
                    ModelCapability.CORPORATE_TRANSACTIONS,
                    ModelCapability.INTELLECTUAL_PROPERTY,
                    ModelCapability.EMPLOYMENT_LAW,
                    ModelCapability.TAX_LAW,
                    ModelCapability.FAMILY_LAW,
                    ModelCapability.CRIMINAL_LAW
                ],
                context_window=64000,
                average_response_time=5.2,
                cost_per_1k_tokens=0.020,
                quality_score=0.98,
                specialization_score=0.98,
                reliability_score=0.99
            )
        }

    def select_model(
        self,
        criteria: ModelSelectionCriteria,
        agent_id: Optional[str] = None,
        task_context: Optional[Dict[str, Any]] = None
    ) -> ModelSelectionResult:
        """
        Select the optimal model based on selection criteria and context.

        Args:
            criteria: Selection criteria and constraints
            agent_id: Optional agent ID for personalized selection
            task_context: Optional task-specific context

        Returns:
            ModelSelectionResult with selected model and reasoning
        """
        start_time = time.time()

        # Filter models based on criteria
        candidate_models = self._filter_models(criteria)

        if not candidate_models:
            # Fallback to basic models if no candidates meet criteria
            candidate_models = [
                self.models["gpt-5-mini"],
                self.models["gpt-4o-mini"],
                self.models["claude-3-haiku"],
                self.models["llama3.2:3b"]
            ]

        # Apply selection strategy
        if criteria.strategy == ModelSelectionStrategy.BEST_ANSWER:
            selected_model = self._select_best_answer(candidate_models, criteria)
        elif criteria.strategy == ModelSelectionStrategy.LEAST_COST:
            selected_model = self._select_least_cost(candidate_models, criteria)
        elif criteria.strategy == ModelSelectionStrategy.SPEED:
            selected_model = self._select_fastest(candidate_models, criteria)
        elif criteria.strategy == ModelSelectionStrategy.BALANCED:
            selected_model = self._select_balanced(candidate_models, criteria)
        elif criteria.strategy == ModelSelectionStrategy.ADAPTIVE:
            selected_model = self._select_adaptive(candidate_models, criteria, agent_id, task_context)
        else:
            selected_model = self._select_balanced(candidate_models, criteria)

        # Generate alternative models
        alternative_models = [
            model for model in candidate_models
            if model.model_name != selected_model.model_name
        ][:3]  # Top 3 alternatives

        # Calculate expected metrics
        expected_cost = self._estimate_cost(selected_model, criteria)
        expected_time = self._estimate_response_time(selected_model, criteria)
        confidence_score = self._calculate_confidence(selected_model, criteria)

        # Generate selection reasoning
        reasoning = self._generate_selection_reasoning(
            selected_model, criteria, expected_cost, expected_time
        )

        result = ModelSelectionResult(
            selected_model=selected_model,
            selection_reasoning=reasoning,
            expected_cost=expected_cost,
            expected_time=expected_time,
            confidence_score=confidence_score,
            alternative_models=alternative_models
        )

        # Record selection for learning
        self._record_selection(result, criteria, agent_id, task_context)

        selection_time = time.time() - start_time
        self.logger.info(
            f"Model selected in {selection_time:.3f}s",
            extra={
                "selected_model": selected_model.model_name,
                "strategy": criteria.strategy.value,
                "expected_cost": expected_cost,
                "expected_time": expected_time,
                "confidence": confidence_score
            }
        )

        return result

    def _filter_models(self, criteria: ModelSelectionCriteria) -> List[ModelProfile]:
        """Filter models based on selection criteria."""
        filtered = []

        for model in self.models.values():
            # Check required capabilities
            if criteria.required_capabilities:
                if not all(cap in model.capabilities for cap in criteria.required_capabilities):
                    continue

            # Check cost constraints
            if criteria.max_cost_per_1k and model.cost_per_1k_tokens > criteria.max_cost_per_1k:
                continue

            # Check response time constraints
            if criteria.max_response_time and model.average_response_time > criteria.max_response_time:
                continue

            # Check quality constraints
            if criteria.min_quality_score and model.quality_score < criteria.min_quality_score:
                continue

            # Check preferred providers
            if criteria.preferred_providers and model.provider not in criteria.preferred_providers:
                continue

            filtered.append(model)

        return filtered

    def _select_best_answer(
        self,
        models: List[ModelProfile],
        criteria: ModelSelectionCriteria
    ) -> ModelProfile:
        """Select model for best answer quality."""
        # Prioritize quality score, then specialization
        return max(models, key=lambda m: (
            m.quality_score * 0.6 +
            m.specialization_score * 0.3 +
            m.reliability_score * 0.1
        ))

    def _select_least_cost(
        self,
        models: List[ModelProfile],
        criteria: ModelSelectionCriteria
    ) -> ModelProfile:
        """Select model for least cost while maintaining quality."""
        # Find models with acceptable quality (above 0.7)
        quality_models = [m for m in models if m.quality_score >= 0.7]

        if quality_models:
            # Select cheapest among quality models
            return min(quality_models, key=lambda m: m.cost_per_1k_tokens)
        else:
            # Fallback to cheapest overall
            return min(models, key=lambda m: m.cost_per_1k_tokens)

    def _select_fastest(
        self,
        models: List[ModelProfile],
        criteria: ModelSelectionCriteria
    ) -> ModelProfile:
        """Select fastest model while maintaining minimum quality."""
        # Filter for minimum quality
        min_quality = criteria.min_quality_score or 0.6
        quality_models = [m for m in models if m.quality_score >= min_quality]

        if quality_models:
            return min(quality_models, key=lambda m: m.average_response_time)
        else:
            return min(models, key=lambda m: m.average_response_time)

    def _select_balanced(
        self,
        models: List[ModelProfile],
        criteria: ModelSelectionCriteria
    ) -> ModelProfile:
        """Select model with balanced quality, cost, and speed."""
        return max(models, key=lambda m: m.overall_score)

    def _select_adaptive(
        self,
        models: List[ModelProfile],
        criteria: ModelSelectionCriteria,
        agent_id: Optional[str],
        task_context: Optional[Dict[str, Any]]
    ) -> ModelProfile:
        """Select model using adaptive learning from past performance."""
        # Use historical performance data if available
        if agent_id and agent_id in self.performance_history:
            agent_history = self.performance_history[agent_id]

            # Find best performing model for similar tasks
            if task_context and "task_type" in task_context:
                task_type = task_context["task_type"]
                if task_type in agent_history:
                    best_model = agent_history[task_type]["best_model"]
                    if best_model in self.models:
                        return self.models[best_model]

        # Fallback to balanced selection
        return self._select_balanced(models, criteria)

    def _estimate_cost(self, model: ModelProfile, criteria: ModelSelectionCriteria) -> float:
        """Estimate cost for the task."""
        # Base estimation on task complexity
        token_multipliers = {
            "simple": 500,
            "medium": 1500,
            "complex": 3000
        }

        estimated_tokens = token_multipliers.get(criteria.task_complexity, 1500)
        return (estimated_tokens / 1000) * model.cost_per_1k_tokens

    def _estimate_response_time(self, model: ModelProfile, criteria: ModelSelectionCriteria) -> float:
        """Estimate response time."""
        # Adjust for task complexity
        complexity_multipliers = {
            "simple": 0.8,
            "medium": 1.0,
            "complex": 1.5
        }

        base_time = model.average_response_time
        complexity_multiplier = complexity_multipliers.get(criteria.task_complexity, 1.0)

        return base_time * complexity_multiplier

    def _calculate_confidence(self, model: ModelProfile, criteria: ModelSelectionCriteria) -> float:
        """Calculate confidence score for the selection."""
        # Base confidence on model reliability and capability match
        capability_match = len([
            cap for cap in criteria.required_capabilities
            if cap in model.capabilities
        ]) / len(criteria.required_capabilities) if criteria.required_capabilities else 1.0

        return min(1.0, (model.reliability_score + capability_match) / 2)

    def _generate_selection_reasoning(
        self,
        model: ModelProfile,
        criteria: ModelSelectionCriteria,
        expected_cost: float,
        expected_time: float
    ) -> str:
        """Generate reasoning for model selection."""
        reasons = []

        if criteria.strategy == ModelSelectionStrategy.BEST_ANSWER:
            reasons.append(f"Selected {model.display_name} for highest quality score ({model.quality_score:.2f})")
        elif criteria.strategy == ModelSelectionStrategy.LEAST_COST:
            reasons.append(f"Selected {model.display_name} for lowest cost (${model.cost_per_1k_tokens:.4f}/1K tokens)")
        elif criteria.strategy == ModelSelectionStrategy.SPEED:
            reasons.append(f"Selected {model.display_name} for fastest response ({model.average_response_time:.1f}s)")
        elif criteria.strategy == ModelSelectionStrategy.BALANCED:
            reasons.append(f"Selected {model.display_name} for optimal balance (score: {model.overall_score:.2f})")

        reasons.append(f"Expected cost: ${expected_cost:.4f}")
        reasons.append(f"Expected time: {expected_time:.1f}s")

        if criteria.required_capabilities:
            matched_caps = [
                cap.value for cap in criteria.required_capabilities
                if cap in model.capabilities
            ]
            reasons.append(f"Capabilities: {', '.join(matched_caps)}")

        return ". ".join(reasons)

    def _record_selection(
        self,
        result: ModelSelectionResult,
        criteria: ModelSelectionCriteria,
        agent_id: Optional[str],
        task_context: Optional[Dict[str, Any]]
    ):
        """Record selection for learning and analytics."""
        selection_record = {
            "timestamp": result.selection_timestamp,
            "agent_id": agent_id,
            "selected_model": result.selected_model.model_name,
            "strategy": criteria.strategy.value,
            "task_complexity": criteria.task_complexity,
            "expected_cost": result.expected_cost,
            "expected_time": result.expected_time,
            "confidence": result.confidence_score,
            "task_context": task_context
        }

        self.selection_history.append(selection_record)

        # Update agent performance history
        if agent_id:
            if agent_id not in self.performance_history:
                self.performance_history[agent_id] = {}

            task_type = task_context.get("task_type", "general") if task_context else "general"
            if task_type not in self.performance_history[agent_id]:
                self.performance_history[agent_id][task_type] = {
                    "selections": [],
                    "best_model": result.selected_model.model_name
                }

            self.performance_history[agent_id][task_type]["selections"].append(selection_record)

    def get_model_recommendations(
        self,
        strategy: ModelSelectionStrategy,
        capabilities: List[ModelCapability] = None,
        limit: int = 5
    ) -> List[ModelProfile]:
        """Get model recommendations for a strategy."""
        criteria = ModelSelectionCriteria(
            strategy=strategy,
            required_capabilities=capabilities or []
        )

        models = self._filter_models(criteria)

        if strategy == ModelSelectionStrategy.BEST_ANSWER:
            models.sort(key=lambda m: m.quality_score, reverse=True)
        elif strategy == ModelSelectionStrategy.LEAST_COST:
            models.sort(key=lambda m: m.cost_per_1k_tokens)
        elif strategy == ModelSelectionStrategy.SPEED:
            models.sort(key=lambda m: m.average_response_time)
        else:  # BALANCED or ADAPTIVE
            models.sort(key=lambda m: m.overall_score, reverse=True)

        return models[:limit]

    def update_model_performance(
        self,
        model_name: str,
        actual_response_time: float,
        actual_cost: float,
        quality_feedback: Optional[float] = None
    ):
        """Update model performance metrics based on actual usage."""
        if model_name not in self.models:
            return

        model = self.models[model_name]

        # Update rolling averages
        alpha = 0.1  # Learning rate

        model.average_response_time = (
            (1 - alpha) * model.average_response_time +
            alpha * actual_response_time
        )

        if quality_feedback is not None:
            model.quality_score = (
                (1 - alpha) * model.quality_score +
                alpha * quality_feedback
            )

        model.last_updated = datetime.utcnow()

        self.logger.debug(
            f"Updated performance for {model_name}",
            extra={
                "response_time": actual_response_time,
                "cost": actual_cost,
                "quality": quality_feedback
            }
        )

    def get_selection_analytics(self) -> Dict[str, Any]:
        """Get analytics about model selection patterns."""
        if not self.selection_history:
            return {"message": "No selection history available"}

        total_selections = len(self.selection_history)

        # Strategy distribution
        strategy_counts = {}
        for record in self.selection_history:
            strategy = record["strategy"]
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

        # Model popularity
        model_counts = {}
        for record in self.selection_history:
            model = record["selected_model"]
            model_counts[model] = model_counts.get(model, 0) + 1

        # Cost analysis
        total_cost = sum(r["expected_cost"] for r in self.selection_history)
        avg_cost = total_cost / total_selections

        # Performance analysis
        avg_confidence = sum(r["confidence"] for r in self.selection_history) / total_selections

        return {
            "total_selections": total_selections,
            "strategy_distribution": strategy_counts,
            "model_popularity": model_counts,
            "total_estimated_cost": total_cost,
            "average_cost_per_selection": avg_cost,
            "average_confidence": avg_confidence,
            "most_popular_model": max(model_counts.items(), key=lambda x: x[1])[0] if model_counts else None,
            "most_used_strategy": max(strategy_counts.items(), key=lambda x: x[1])[0] if strategy_counts else None
        }