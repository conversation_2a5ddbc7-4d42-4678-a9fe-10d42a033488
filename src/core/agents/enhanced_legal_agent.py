"""
Enhanced Legal Agent with Intelligent Model Selection.

This module implements enhanced legal agents that can dynamically select and switch
AI models based on different priorities: Best Answer, Least Cost, or Speed optimization.

Each of the 45 specialized legal agents can now:
- Automatically select optimal models for different task types
- Switch models based on cost, quality, or speed requirements
- Learn from performance and adapt model choices
- Balance quality vs cost vs speed trade-offs
- Provide real-time model selection reasoning

Key Features:
- Intelligent model routing based on task requirements
- Cost optimization with quality maintenance
- Speed optimization for time-sensitive queries
- Adaptive learning from performance data
- Multi-strategy model selection (Best Answer, Least Cost, Speed)
"""
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime

from .base import Agent, AgentRole, AgentCapability, ModelSelectionStrategy, AgentResponse, AgentContext
from .model_selector import IntelligentModelSelector, ModelSelectionCriteria, ModelSelectionResult
from .legal_domain_detector import LegalDomain
from infrastructure.ai_providers.base import AIProvider
from utils.logging import get_logger


@dataclass
class AgentModelPreferences:
    """Model preferences for a specific agent."""
    default_strategy: ModelSelectionStrategy = ModelSelectionStrategy.BALANCED
    cost_sensitivity: str = "normal"  # low, normal, high
    speed_priority: str = "normal"    # low, normal, high
    quality_threshold: float = 0.8    # minimum acceptable quality
    max_cost_per_1k: Optional[float] = None
    preferred_providers: List[str] = field(default_factory=list)
    adaptive_learning: bool = True


@dataclass
class ModelPerformanceMetrics:
    """Performance metrics for model usage by this agent."""
    model_name: str
    total_uses: int = 0
    successful_uses: int = 0
    average_response_time: float = 0.0
    average_cost: float = 0.0
    average_quality_score: float = 0.0
    last_used: Optional[datetime] = None

    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        return self.successful_uses / self.total_uses if self.total_uses > 0 else 0.0


class EnhancedLegalAgent(Agent):
    """
    Enhanced legal agent with intelligent model selection capabilities.

    This agent can dynamically choose between different AI models based on:
    - Task requirements (complexity, time sensitivity, cost constraints)
    - Performance history and learning
    - Quality vs cost vs speed trade-offs
    - Domain-specific optimization
    """

    def __init__(
        self,
        name: str,
        role: AgentRole,
        capabilities: List[AgentCapability],
        ai_provider: AIProvider,
        legal_domain: LegalDomain,
        model_selector: IntelligentModelSelector,
        preferences: Optional[AgentModelPreferences] = None,
        **kwargs
    ):
        """
        Initialize enhanced legal agent.

        Args:
            name: Agent name
            role: Agent role
            capabilities: Agent capabilities
            ai_provider: Primary AI provider
            legal_domain: Legal domain specialization
            model_selector: Intelligent model selector
            preferences: Model selection preferences
        """
        super().__init__(name, role, capabilities, ai_provider, **kwargs)

        self.legal_domain = legal_domain
        self.model_selector = model_selector
        self.preferences = preferences or AgentModelPreferences()
        self.model_performance: Dict[str, ModelPerformanceMetrics] = {}
        self.task_history: List[Dict[str, Any]] = []

        # Enhanced logging
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}.{name}")

        # Model selection state
        self.current_model: Optional[str] = None
        self.last_model_switch: Optional[datetime] = None

    async def initialize(self) -> bool:
        """Initialize the enhanced agent with model selection setup."""
        try:
            # Initialize base agent
            if not await super().initialize():
                return False

            # Initialize model performance tracking
            self._initialize_model_performance()

            # Test model selection
            test_criteria = ModelSelectionCriteria(
                strategy=self.preferences.default_strategy,
                required_capabilities=[self._get_domain_capability()]
            )

            test_result = self.model_selector.select_model(test_criteria, self.name)
            if not test_result.selected_model:
                self.logger.warning(f"No suitable model found for agent {self.name}")
                return False

            self.current_model = test_result.selected_model.model_name
            self.logger.info(
                f"Enhanced legal agent {self.name} initialized",
                extra={
                    "domain": self.legal_domain.value,
                    "default_model": self.current_model,
                    "strategy": self.preferences.default_strategy.value
                }
            )

            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize enhanced agent {self.name}: {str(e)}")
            return False

    def _initialize_model_performance(self):
        """Initialize performance tracking for available models."""
        # Get all available models from selector
        all_models = list(self.model_selector.models.keys())

        for model_name in all_models:
            self.model_performance[model_name] = ModelPerformanceMetrics(model_name=model_name)

    def _get_domain_capability(self) -> str:
        """Get the primary capability for this agent's legal domain."""
        domain_capabilities = {
            LegalDomain.CONSTITUTIONAL_LAW: "constitutional_analysis",
            LegalDomain.CRIMINAL_LAW: "criminal_law_analysis",
            LegalDomain.FAMILY_LAW: "family_law_analysis",
            LegalDomain.CORPORATE_LAW: "corporate_law_analysis",
            LegalDomain.INTELLECTUAL_PROPERTY: "ip_law_analysis",
            LegalDomain.EMPLOYMENT_LAW: "employment_law_analysis",
            LegalDomain.TAX_LAW: "tax_law_analysis",
            LegalDomain.ENVIRONMENTAL_LAW: "environmental_law_analysis",
            LegalDomain.IMMIGRATION_LAW: "immigration_law_analysis",
            LegalDomain.REAL_ESTATE_LAW: "real_estate_law_analysis",
            LegalDomain.CONTRACT_LAW: "contract_analysis",
            LegalDomain.TORT_LAW: "tort_law_analysis",
            LegalDomain.ADMINISTRATIVE_LAW: "administrative_law_analysis",
            LegalDomain.INTERNATIONAL_LAW: "international_law_analysis",
            LegalDomain.PROCEDURAL_COMPLIANCE: "procedural_compliance",
            LegalDomain.REGULATORY_COMPLIANCE: "regulatory_compliance",
            LegalDomain.ETHICS_COMPLIANCE: "ethics_compliance",
            LegalDomain.FINANCIAL_COMPLIANCE: "financial_compliance",
            LegalDomain.DATA_PRIVACY_COMPLIANCE: "data_privacy_compliance",
            LegalDomain.ANTITRUST_LAW: "antitrust_analysis",
            LegalDomain.BANKING_LAW: "banking_law_analysis",
            LegalDomain.BANKRUPTCY_LAW: "bankruptcy_analysis",
            LegalDomain.CONSTRUCTION_LAW: "construction_law_analysis",
            LegalDomain.CYBER_LAW: "cyber_law_analysis",
            LegalDomain.EDUCATION_LAW: "education_law_analysis",
            LegalDomain.ENERGY_LAW: "energy_law_analysis",
            LegalDomain.ENTERTAINMENT_LAW: "entertainment_law_analysis",
            LegalDomain.HEALTH_LAW: "health_law_analysis",
            LegalDomain.INSURANCE_LAW: "insurance_law_analysis",
            LegalDomain.MARITIME_LAW: "maritime_law_analysis",
            LegalDomain.MILITARY_LAW: "military_law_analysis",
            LegalDomain.MINING_LAW: "mining_law_analysis",
            LegalDomain.PHARMACEUTICAL_LAW: "pharmaceutical_law_analysis",
            LegalDomain.PRIVACY_LAW: "privacy_law_analysis",
            LegalDomain.PUBLIC_UTILITIES_LAW: "public_utilities_law_analysis",
            LegalDomain.SECURITIES_LAW: "securities_law_analysis",
            LegalDomain.SPORTS_LAW: "sports_law_analysis",
            LegalDomain.TELECOMMUNICATIONS_LAW: "telecommunications_law_analysis",
            LegalDomain.TRUSTS_ESTATES_LAW: "trusts_estates_law_analysis",
            LegalDomain.WORKERS_COMPENSATION_LAW: "workers_compensation_law_analysis",
            LegalDomain.EU_LAW: "eu_law_analysis",
            LegalDomain.HUMAN_RIGHTS_LAW: "human_rights_law_analysis",
            LegalDomain.TRADE_LAW: "trade_law_analysis",
            LegalDomain.ARBITRATION_LAW: "arbitration_law_analysis",
            LegalDomain.AI_LAW: "ai_law_analysis",
            LegalDomain.CRYPTOCURRENCY_LAW: "cryptocurrency_law_analysis",
            LegalDomain.SPACE_LAW: "space_law_analysis",
            LegalDomain.CLIMATE_LAW: "climate_law_analysis"
        }

        return domain_capabilities.get(self.legal_domain, "general_legal")

    async def process_query(
        self,
        query: str,
        context: Optional[AgentContext] = None,
        model_strategy: Optional[ModelSelectionStrategy] = None
    ) -> AgentResponse:
        """
        Process a query with intelligent model selection.

        Args:
            query: The query to process
            context: Optional context information
            model_strategy: Optional model selection strategy override

        Returns:
            AgentResponse with model selection details
        """
        start_time = time.time()

        try:
            # Determine optimal model for this query
            model_result = await self._select_optimal_model(query, context, model_strategy)

            # Update current model if different
            if model_result.selected_model.model_name != self.current_model:
                self._switch_model(model_result.selected_model.model_name)

            # Process query with selected model
            response = await self._process_with_model(query, model_result, context)

            # Track performance
            processing_time = time.time() - start_time
            await self._track_performance(
                model_result.selected_model.model_name,
                processing_time,
                model_result.expected_cost,
                response.confidence_score
            )

            # Add model selection metadata to response
            enhanced_metadata = response.metadata.copy()
            enhanced_metadata.update({
                "selected_model": model_result.selected_model.model_name,
                "model_provider": model_result.selected_model.provider.value,
                "selection_strategy": model_result.selection_reasoning,
                "expected_cost": model_result.expected_cost,
                "expected_time": model_result.expected_time,
                "model_confidence": model_result.confidence_score,
                "agent_domain": self.legal_domain.value,
                "alternative_models": [
                    alt.model_name for alt in model_result.alternative_models
                ]
            })

            enhanced_response = AgentResponse(
                content=response.content,
                confidence_score=response.confidence_score,
                metadata=enhanced_metadata,
                sources=response.sources,
                processing_time=processing_time,
                tokens_used=response.tokens_used,
                model_used=model_result.selected_model.model_name
            )

            # Record task in history
            self._record_task(query, enhanced_response, model_result)

            return enhanced_response

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Query processing failed: {str(e)}")

            # Return error response
            return AgentResponse(
                content=f"I apologize, but I encountered an error while processing your query: {str(e)}",
                confidence_score=0.0,
                metadata={"error": str(e), "agent": self.name},
                sources=[],
                processing_time=processing_time
            )

    async def _select_optimal_model(
        self,
        query: str,
        context: Optional[AgentContext] = None,
        strategy_override: Optional[ModelSelectionStrategy] = None
    ) -> ModelSelectionResult:
        """Select the optimal model for the given query."""
        # Determine task characteristics
        task_complexity = self._assess_task_complexity(query, context)
        time_sensitivity = self._assess_time_sensitivity(query, context)
        cost_sensitivity = self.preferences.cost_sensitivity

        # Determine required capabilities
        required_capabilities = [self._get_domain_capability()]

        # Create selection criteria
        criteria = ModelSelectionCriteria(
            strategy=strategy_override or self.preferences.default_strategy,
            required_capabilities=required_capabilities,
            max_cost_per_1k=self.preferences.max_cost_per_1k,
            min_quality_score=self.preferences.quality_threshold,
            preferred_providers=self.preferences.preferred_providers,
            task_complexity=task_complexity,
            time_sensitivity=time_sensitivity,
            cost_sensitivity=cost_sensitivity
        )

        # Select model using intelligent selector
        return self.model_selector.select_model(
            criteria=criteria,
            agent_id=self.name,
            task_context={
                "query": query,
                "domain": self.legal_domain.value,
                "task_type": self._categorize_query(query),
                "context": context.__dict__ if context else None
            }
        )

    def _assess_task_complexity(self, query: str, context: Optional[AgentContext] = None) -> str:
        """Assess the complexity of the task."""
        query_length = len(query.split())
        has_legal_terms = any(term in query.lower() for term in [
            "statute", "precedent", "jurisdiction", "liability", "contract",
            "litigation", "compliance", "regulation", "due diligence"
        ])

        # Check for complex indicators
        complexity_indicators = [
            query_length > 50,  # Long query
            has_legal_terms,    # Legal terminology
            "?" in query,       # Question format
            any(word in query.lower() for word in ["analyze", "review", "assess", "evaluate"])
        ]

        if sum(complexity_indicators) >= 3:
            return "complex"
        elif sum(complexity_indicators) >= 2:
            return "medium"
        else:
            return "simple"

    def _assess_time_sensitivity(self, query: str, context: Optional[AgentContext] = None) -> str:
        """Assess time sensitivity of the query."""
        urgent_indicators = [
            "urgent" in query.lower(),
            "asap" in query.lower(),
            "deadline" in query.lower(),
            "immediately" in query.lower(),
            "emergency" in query.lower(),
            "rush" in query.lower()
        ]

        if any(urgent_indicators):
            return "high"
        elif self.preferences.speed_priority == "high":
            return "high"
        else:
            return "normal"

    def _categorize_query(self, query: str) -> str:
        """Categorize the query type for performance tracking."""
        query_lower = query.lower()

        if any(word in query_lower for word in ["contract", "agreement", "terms"]):
            return "contract_analysis"
        elif any(word in query_lower for word in ["research", "case", "precedent"]):
            return "legal_research"
        elif any(word in query_lower for word in ["risk", "liability", "exposure"]):
            return "risk_assessment"
        elif any(word in query_lower for word in ["compliance", "regulation", "statute"]):
            return "compliance_check"
        else:
            return "general_legal"

    def _switch_model(self, new_model: str):
        """Switch to a different model."""
        old_model = self.current_model
        self.current_model = new_model
        self.last_model_switch = datetime.utcnow()

        self.logger.info(
            f"Model switched from {old_model} to {new_model}",
            extra={
                "agent": self.name,
                "old_model": old_model,
                "new_model": new_model,
                "domain": self.legal_domain.value
            }
        )

    async def _process_with_model(
        self,
        query: str,
        model_result: ModelSelectionResult,
        context: Optional[AgentContext] = None
    ) -> AgentResponse:
        """Process query with the selected model."""
        # This would integrate with the actual AI provider
        # For now, return a mock response with model details

        # Simulate processing time based on selected model
        processing_time = model_result.expected_time * 0.9  # Slightly less than expected

        # Generate response content based on domain expertise
        response_content = self._generate_domain_specific_response(query, model_result)

        return AgentResponse(
            content=response_content,
            confidence_score=model_result.confidence_score,
            metadata={
                "model_used": model_result.selected_model.model_name,
                "domain": self.legal_domain.value,
                "processing_strategy": model_result.selection_reasoning
            },
            sources=[
                {
                    "type": "ai_model",
                    "model": model_result.selected_model.model_name,
                    "provider": model_result.selected_model.provider.value,
                    "confidence": model_result.confidence_score
                }
            ],
            processing_time=processing_time,
            tokens_used=int(len(query.split()) * 1.5),  # Estimate
            model_used=model_result.selected_model.model_name
        )

    def _generate_domain_specific_response(self, query: str, model_result: ModelSelectionResult) -> str:
        """Generate domain-specific response content."""
        domain_responses = {
            LegalDomain.CONSTITUTIONAL_LAW: f"As a Constitutional Law specialist, I analyze this matter under the framework of constitutional principles. {query} involves considerations of fundamental rights and governmental powers.",
            LegalDomain.CRIMINAL_LAW: f"From a criminal law perspective, this matter requires careful analysis of criminal statutes, procedural rules, and constitutional protections. {query} demands attention to due process and evidentiary standards.",
            LegalDomain.FAMILY_LAW: f"In family law matters, we must consider the best interests of all parties, particularly any children involved. {query} requires balancing legal rights with family dynamics and long-term relationships.",
            LegalDomain.CORPORATE_LAW: f"Corporate law analysis focuses on fiduciary duties, shareholder rights, and corporate governance. {query} involves business entity structures and corporate compliance obligations.",
            LegalDomain.CONTRACT_LAW: f"Contract analysis requires examination of offer, acceptance, consideration, and enforceability. {query} demands careful review of contractual terms and potential breaches.",
            LegalDomain.EMPLOYMENT_LAW: f"Employment law matters involve worker rights, discrimination prohibitions, and workplace regulations. {query} requires balancing employer interests with employee protections.",
            LegalDomain.INTELLECTUAL_PROPERTY: f"Intellectual property analysis focuses on creation, ownership, and protection of intangible assets. {query} involves patent, copyright, trademark, or trade secret considerations.",
            LegalDomain.TAX_LAW: f"Tax law requires precise interpretation of tax statutes, regulations, and court decisions. {query} demands careful consideration of tax consequences and compliance obligations.",
            LegalDomain.ENVIRONMENTAL_LAW: f"Environmental law matters involve regulatory compliance and environmental protection. {query} requires balancing development needs with environmental conservation.",
            LegalDomain.IMMIGRATION_LAW: f"Immigration law focuses on visa categories, citizenship requirements, and deportation procedures. {query} involves complex immigration regulations and individual circumstances.",
            LegalDomain.REAL_ESTATE_LAW: f"Real estate law involves property rights, title issues, and transactional requirements. {query} requires careful attention to property law principles and local regulations.",
            LegalDomain.PROCEDURAL_COMPLIANCE: f"Procedural compliance requires adherence to legal processes, deadlines, and filing requirements. {query} demands meticulous attention to procedural details and timelines."
        }

        base_response = domain_responses.get(
            self.legal_domain,
            f"As a legal specialist in {self.legal_domain.value}, I provide expert analysis on: {query}"
        )

        # Add model selection context
        model_context = f"\n\n**Analysis performed using {model_result.selected_model.display_name}** ({model_result.selection_reasoning})"

        return base_response + model_context

    async def _track_performance(
        self,
        model_name: str,
        response_time: float,
        cost: float,
        quality_score: float
    ):
        """Track model performance for learning."""
        if model_name not in self.model_performance:
            self.model_performance[model_name] = ModelPerformanceMetrics(model_name=model_name)

        perf = self.model_performance[model_name]
        perf.total_uses += 1
        perf.successful_uses += 1  # Assume success for now
        perf.last_used = datetime.utcnow()

        # Update rolling averages
        alpha = 0.1  # Learning rate
        perf.average_response_time = (1 - alpha) * perf.average_response_time + alpha * response_time
        perf.average_cost = (1 - alpha) * perf.average_cost + alpha * cost
        perf.average_quality_score = (1 - alpha) * perf.average_quality_score + alpha * quality_score

        # Update model selector with performance data
        self.model_selector.update_model_performance(
            model_name=model_name,
            actual_response_time=response_time,
            actual_cost=cost,
            quality_feedback=quality_score
        )

    def _record_task(
        self,
        query: str,
        response: AgentResponse,
        model_result: ModelSelectionResult
    ):
        """Record task for analytics and learning."""
        task_record = {
            "timestamp": datetime.utcnow(),
            "query": query,
            "response_length": len(response.content),
            "model_used": model_result.selected_model.model_name,
            "processing_time": response.processing_time,
            "confidence": response.confidence_score,
            "cost": model_result.expected_cost,
            "domain": self.legal_domain.value,
            "strategy": model_result.selection_reasoning
        }

        self.task_history.append(task_record)

        # Keep only last 100 tasks
        if len(self.task_history) > 100:
            self.task_history = self.task_history[-100:]

    def get_model_preferences(self) -> AgentModelPreferences:
        """Get current model preferences."""
        return self.preferences

    def update_model_preferences(self, preferences: AgentModelPreferences):
        """Update model selection preferences."""
        self.preferences = preferences
        self.logger.info(
            f"Updated model preferences for {self.name}",
            extra={
                "strategy": preferences.default_strategy.value,
                "cost_sensitivity": preferences.cost_sensitivity,
                "quality_threshold": preferences.quality_threshold
            }
        )

    def get_performance_analytics(self) -> Dict[str, Any]:
        """Get performance analytics for this agent."""
        if not self.task_history:
            return {"message": "No task history available"}

        total_tasks = len(self.task_history)
        avg_processing_time = sum(t["processing_time"] for t in self.task_history) / total_tasks
        avg_confidence = sum(t["confidence"] for t in self.task_history) / total_tasks
        total_cost = sum(t["cost"] for t in self.task_history)

        # Model usage distribution
        model_usage = {}
        for task in self.task_history:
            model = task["model_used"]
            model_usage[model] = model_usage.get(model, 0) + 1

        return {
            "total_tasks": total_tasks,
            "average_processing_time": avg_processing_time,
            "average_confidence": avg_confidence,
            "total_cost": total_cost,
            "model_usage_distribution": model_usage,
            "most_used_model": max(model_usage.items(), key=lambda x: x[1])[0] if model_usage else None,
            "current_model": self.current_model,
            "domain": self.legal_domain.value
        }

    def can_handle_query(self, query: str, context: Optional[AgentContext] = None) -> bool:
        """Enhanced query handling with domain expertise check."""
        # Check if query is relevant to this agent's domain
        domain_keywords = self._get_domain_keywords()
        query_lower = query.lower()

        # Check for domain-specific keywords
        domain_match = any(keyword in query_lower for keyword in domain_keywords)

        # Check for general legal terms if domain match is weak
        general_legal_terms = [
            "legal", "law", "contract", "agreement", "court", "judge",
            "attorney", "lawyer", "litigation", "complaint", "defendant"
        ]
        general_match = any(term in query_lower for term in general_legal_terms)

        # Agent can handle if there's a strong domain match or general legal context
        return domain_match or (general_match and len(query.split()) > 3)

    def _get_domain_keywords(self) -> List[str]:
        """Get domain-specific keywords for this agent."""
        domain_keywords = {
            LegalDomain.CONSTITUTIONAL_LAW: [
                "constitution", "constitutional", "first amendment", "due process",
                "equal protection", "supreme court", "bill of rights"
            ],
            LegalDomain.CRIMINAL_LAW: [
                "criminal", "felony", "misdemeanor", "arrest", "indictment",
                "prosecution", "defense", "plea bargain", "sentencing"
            ],
            LegalDomain.FAMILY_LAW: [
                "divorce", "custody", "child support", "alimony", "marriage",
                "adoption", "guardianship", "domestic violence"
            ],
            LegalDomain.CORPORATE_LAW: [
                "corporation", "llc", "shareholder", "board", "merger",
                "acquisition", "ipo", "corporate governance"
            ],
            LegalDomain.CONTRACT_LAW: [
                "contract", "agreement", "breach", "consideration", "offer",
                "acceptance", "novation", "force majeure"
            ],
            LegalDomain.EMPLOYMENT_LAW: [
                "employment", "labor", "wage", "discrimination", "harassment",
                "wrongful termination", "fmla", "nlra", "union"
            ],
            LegalDomain.INTELLECTUAL_PROPERTY: [
                "patent", "trademark", "copyright", "trade secret", "ip",
                "infringement", "licensing", "cease and desist"
            ],
            LegalDomain.TAX_LAW: [
                "tax", "irs", "income tax", "corporate tax", "audit",
                "deduction", "tax return", "tax evasion"
            ],
            LegalDomain.ENVIRONMENTAL_LAW: [
                "environmental", "epa", "pollution", "clean air", "climate change",
                "endangered species", "environmental impact"
            ],
            LegalDomain.IMMIGRATION_LAW: [
                "immigration", "visa", "green card", "citizenship", "deportation",
                "asylum", "refugee", "h1b", "naturalization"
            ],
            LegalDomain.REAL_ESTATE_LAW: [
                "real estate", "property", "landlord", "tenant", "lease",
                "mortgage", "foreclosure", "easement", "title"
            ],
            LegalDomain.PROCEDURAL_COMPLIANCE: [
                "procedure", "compliance", "regulatory", "filing", "deadline",
                "notice", "hearing", "administrative process"
            ]
        }

        return domain_keywords.get(self.legal_domain, ["legal", "law"])

    def get_model_recommendations(
        self,
        strategy: ModelSelectionStrategy,
        task_description: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get model recommendations for this agent."""
        capabilities = [self._get_domain_capability()]

        recommended_models = self.model_selector.get_model_recommendations(
            strategy=strategy,
            capabilities=capabilities,
            limit=3
        )

        recommendations = []
        for model in recommended_models:
            recommendation = {
                "model_name": model.model_name,
                "display_name": model.display_name,
                "provider": model.provider.value,
                "quality_score": model.quality_score,
                "cost_per_1k": model.cost_per_1k_tokens,
                "response_time": model.average_response_time,
                "capabilities": [cap.value for cap in model.capabilities],
                "reasoning": self._generate_recommendation_reasoning(model, strategy)
            }
            recommendations.append(recommendation)

        return recommendations

    def _generate_recommendation_reasoning(self, model, strategy: ModelSelectionStrategy) -> str:
        """Generate reasoning for model recommendation."""
        if strategy == ModelSelectionStrategy.BEST_ANSWER:
            return f"Recommended for highest quality ({model.quality_score:.2f}) in {self.legal_domain.value}"
        elif strategy == ModelSelectionStrategy.LEAST_COST:
            return f"Recommended for cost-effectiveness (${model.cost_per_1k_tokens:.4f}/1K tokens)"
        elif strategy == ModelSelectionStrategy.SPEED:
            return f"Recommended for speed ({model.average_response_time:.1f}s average response)"
        else:
            return f"Recommended for balanced performance in {self.legal_domain.value}"