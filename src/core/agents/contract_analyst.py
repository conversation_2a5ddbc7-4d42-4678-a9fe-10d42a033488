"""
Contract Analyst Agent for AI Law Firm.

This agent specializes in contract analysis, risk assessment, and
commercial agreement review to identify legal issues and opportunities.
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .base import Agent, AgentRole, AgentCapability, AgentResponse, AgentContext
from infrastructure.ai_providers.base import AIProvider
from utils.logging import get_logger


class ContractAnalyst(Agent):
    """
    Specialized contract analysis agent.

    Capabilities:
    - Contract review and analysis
    - Risk identification and assessment
    - Term negotiation support
    - Compliance checking
    - Contract drafting assistance
    """

    def __init__(
        self,
        name: str,
        ai_provider: AIProvider,
        knowledge_base: Optional[Any] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        capabilities = [
            AgentCapability.CONTRACT_REVIEW,
            AgentCapability.RISK_ASSESSMENT,
            AgentCapability.DOCUMENT_ANALYSIS,
            AgentCapability.COMPLIANCE_CHECKING
        ]

        super().__init__(
            name=name,
            role=AgentRole.CONTRACT_ANALYST,
            capabilities=capabilities,
            ai_provider=ai_provider,
            knowledge_base=knowledge_base,
            config=config
        )

        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.contract_templates = self._initialize_contract_templates()
        self.risk_patterns = self._initialize_risk_patterns()

    async def initialize(self) -> bool:
        """Initialize the contract analyst agent."""
        try:
            self.logger.info(f"Initializing Contract Analyst: {self.name}")

            # Validate AI provider capabilities
            if not hasattr(self.ai_provider, 'generate_response'):
                self.logger.error("AI provider does not support text generation")
                return False

            # Initialize contract analysis tools
            self.analysis_tools = self._initialize_analysis_tools()

            self.is_initialized = True
            self.logger.info(f"Contract Analyst {self.name} initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Contract Analyst: {str(e)}")
            return False

    async def process_query(
        self,
        query: str,
        context: Optional[AgentContext] = None
    ) -> AgentResponse:
        """Process a contract analysis query."""
        self.update_activity()

        try:
            # Determine analysis type
            analysis_type = self._classify_analysis_query(query)

            # Extract contract content if provided
            contract_content = self._extract_contract_content(context)

            # Perform analysis
            analysis_results = await self._perform_contract_analysis(
                query, analysis_type, contract_content, context
            )

            # Generate response
            response_content = self._format_analysis_response(analysis_results, analysis_type)

            # Calculate confidence and metadata
            confidence_score = self._calculate_analysis_confidence(analysis_results)
            metadata = self._generate_analysis_metadata(analysis_results, analysis_type)

            return AgentResponse(
                content=response_content,
                confidence_score=confidence_score,
                metadata=metadata,
                sources=analysis_results.get('sources', []),
                processing_time=0.0,  # Will be set by caller
                model_used=getattr(self.ai_provider, 'model', 'unknown')
            )

        except Exception as e:
            self.logger.error(f"Contract analysis failed: {str(e)}")
            return AgentResponse(
                content=f"I apologize, but I encountered an error during contract analysis: {str(e)}",
                confidence_score=0.0,
                metadata={"error": str(e), "error_type": type(e).__name__},
                sources=[],
                processing_time=0.0
            )

    def can_handle_query(self, query: str, context: Optional[AgentContext] = None) -> bool:
        """Determine if this agent can handle the given query."""
        contract_indicators = [
            'contract', 'agreement', 'terms', 'clause', 'provision',
            'breach', 'liability', 'obligation', 'rights', 'duties',
            'review contract', 'analyze agreement', 'contract risk',
            'negotiation', 'draft contract', 'contract compliance'
        ]

        query_lower = query.lower()
        return any(indicator in query_lower for indicator in contract_indicators)

    def _initialize_contract_templates(self) -> Dict[str, str]:
        """Initialize contract analysis templates."""
        return {
            'comprehensive_review': """
            Perform a comprehensive review of the following contract:

            Contract Content:
            {contract_content}

            Analysis Request: {query}

            Please provide:
            1. Executive Summary
            2. Key Terms Analysis
            3. Risk Assessment
            4. Compliance Issues
            5. Recommendations
            6. Negotiation Points

            Structure your analysis professionally with clear sections and specific citations to contract clauses.
            """,

            'risk_assessment': """
            Conduct a risk assessment for this contract:

            Contract Content:
            {contract_content}

            Focus Areas: {query}

            Risk Analysis Framework:
            1. Financial Risks
            2. Operational Risks
            3. Legal/Compliance Risks
            4. Reputational Risks
            5. Strategic Risks

            For each risk identified:
            - Severity (High/Medium/Low)
            - Likelihood (High/Medium/Low)
            - Mitigation strategies
            - Contract clause references
            """,

            'clause_analysis': """
            Analyze specific clauses in this contract:

            Contract Content:
            {contract_content}

            Analysis Focus: {query}

            Provide:
            1. Clause identification and location
            2. Plain language explanation
            3. Potential issues or concerns
            4. Comparison to standard terms
            5. Recommendations for improvement
            """,

            'compliance_check': """
            Check this contract for compliance with applicable laws and regulations:

            Contract Content:
            {contract_content}

            Compliance Focus: {query}

            Review against:
            1. Consumer protection laws
            2. Data privacy regulations (GDPR, CCPA)
            3. Industry-specific regulations
            4. International trade laws
            5. Anti-corruption laws (FCPA, UK Bribery Act)

            Identify any compliance gaps or concerns.
            """
        }

    def _initialize_risk_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize risk identification patterns."""
        return {
            'unilateral_termination': {
                'patterns': [r'terminat(?:e|ion)', r'cancel', r'rescind'],
                'risk_level': 'medium',
                'category': 'termination_rights'
            },
            'indemnification': {
                'patterns': [r'indemnif(?:y|ication)', r'hold harmless', r'defend.* indemnify'],
                'risk_level': 'high',
                'category': 'liability'
            },
            'force_majeure': {
                'patterns': [r'force majeure', r'act of god', r'uncontrollable circumstances'],
                'risk_level': 'medium',
                'category': 'performance'
            },
            'liquidated_damages': {
                'patterns': [r'liquidated damages', r'penalty', r'per diem'],
                'risk_level': 'high',
                'category': 'financial'
            },
            'non_compete': {
                'patterns': [r'non.?compete', r'non.?competition', r'restrictive covenant'],
                'risk_level': 'high',
                'category': 'restrictive_covenants'
            },
            'confidentiality': {
                'patterns': [r'confidential', r'proprietary', r'non.?disclosure'],
                'risk_level': 'medium',
                'category': 'intellectual_property'
            },
            'governing_law': {
                'patterns': [r'governing law', r'choice of law', r'jurisdiction'],
                'risk_level': 'medium',
                'category': 'jurisdiction'
            }
        }

    def _initialize_analysis_tools(self) -> Dict[str, Any]:
        """Initialize contract analysis tools."""
        return {
            'clause_extractor': self._extract_contract_clauses,
            'risk_scanner': self._scan_for_risks,
            'compliance_checker': self._check_compliance,
            'term_comparator': self._compare_to_standard_terms
        }

    def _classify_analysis_query(self, query: str) -> str:
        """Classify the type of contract analysis query."""
        query_lower = query.lower()

        if any(term in query_lower for term in ['risk', 'assessment', 'hazard', 'exposure']):
            return 'risk_assessment'
        elif any(term in query_lower for term in ['clause', 'section', 'provision', 'term']):
            return 'clause_analysis'
        elif any(term in query_lower for term in ['compliance', 'regulatory', 'legal requirement']):
            return 'compliance_check'
        elif any(term in query_lower for term in ['review', 'analyze', 'examine']):
            return 'comprehensive_review'
        else:
            return 'general_analysis'

    def _extract_contract_content(self, context: Optional[AgentContext]) -> str:
        """Extract contract content from context."""
        if not context:
            return ""

        # If document content is provided in context
        if hasattr(context, 'document_content') and context.document_content:
            return context.document_content

        # If document IDs are provided, would retrieve from knowledge base
        if context.document_ids:
            # This would integrate with knowledge base to retrieve documents
            return f"Contract documents: {', '.join(context.document_ids)}"

        return ""

    async def _perform_contract_analysis(
        self,
        query: str,
        analysis_type: str,
        contract_content: str,
        context: Optional[AgentContext]
    ) -> Dict[str, Any]:
        """Perform the actual contract analysis."""
        # Select appropriate template
        template = self.contract_templates.get(analysis_type, self.contract_templates['comprehensive_review'])

        # Prepare analysis prompt
        analysis_prompt = template.format(
            contract_content=contract_content[:5000],  # Limit content for API
            query=query
        )

        # Add context information
        if context and context.metadata:
            analysis_prompt += f"\n\nAdditional Context: {context.metadata}"

        # Execute analysis
        analysis_response = await self.ai_provider.generate_response(
            prompt=analysis_prompt,
            context=None,
            temperature=0.1,  # Low temperature for analytical work
            max_tokens=2500
        )

        # Extract structured information
        structured_analysis = await self._extract_structured_analysis(
            analysis_response.content, contract_content
        )

        # Perform risk scanning
        risk_findings = self._scan_for_risks(contract_content)

        return {
            'query': query,
            'analysis_type': analysis_type,
            'contract_content': contract_content,
            'analysis': analysis_response.content,
            'structured_analysis': structured_analysis,
            'risk_findings': risk_findings,
            'sources': self._extract_contract_sources(contract_content),
            'recommendations': structured_analysis.get('recommendations', [])
        }

    async def _extract_structured_analysis(
        self,
        analysis_text: str,
        contract_content: str
    ) -> Dict[str, Any]:
        """Extract structured information from analysis text."""
        structured_prompt = f"""
        Extract structured information from this contract analysis:

        Analysis Text:
        {analysis_text[:2000]}

        Contract Content Preview:
        {contract_content[:1000]}

        Please extract and structure:
        1. Key terms and conditions
        2. Identified risks (with severity levels)
        3. Compliance issues
        4. Recommendations
        5. Critical clauses

        Format as JSON-like structure with clear categories.
        """

        structured_response = await self.ai_provider.generate_response(
            prompt=structured_prompt,
            temperature=0.0,
            max_tokens=1000
        )

        # Parse structured response (simplified parsing)
        return self._parse_structured_response(structured_response.content)

    def _scan_for_risks(self, contract_content: str) -> List[Dict[str, Any]]:
        """Scan contract content for potential risks."""
        findings = []
        content_lower = contract_content.lower()

        for risk_name, risk_info in self.risk_patterns.items():
            for pattern in risk_info['patterns']:
                matches = re.finditer(pattern, content_lower, re.IGNORECASE)
                for match in matches:
                    # Get context around the match
                    start = max(0, match.start() - 100)
                    end = min(len(contract_content), match.end() + 100)
                    context = contract_content[start:end]

                    findings.append({
                        'risk_type': risk_name,
                        'risk_level': risk_info['risk_level'],
                        'category': risk_info['category'],
                        'matched_text': match.group(),
                        'context': context,
                        'position': match.start(),
                        'recommendation': self._get_risk_recommendation(risk_name)
                    })

        return findings

    def _get_risk_recommendation(self, risk_type: str) -> str:
        """Get recommendation for a specific risk type."""
        recommendations = {
            'indemnification': 'Consider negotiating mutual indemnification or liability caps',
            'liquidated_damages': 'Review reasonableness of damage amounts and ensure they are not punitive',
            'non_compete': 'Ensure scope, duration, and geography are reasonable and necessary',
            'unilateral_termination': 'Negotiate for mutual termination rights or notice periods',
            'force_majeure': 'Define specific triggering events and notice requirements',
            'governing_law': 'Consider jurisdiction implications and dispute resolution mechanisms'
        }

        return recommendations.get(risk_type, 'Review this provision with legal counsel')

    def _extract_contract_sources(self, contract_content: str) -> List[Dict[str, Any]]:
        """Extract contract references and sources."""
        sources = []

        # Extract clause references
        clause_pattern = r'(?:Section|Clause|Article)\s+\d+(?:\.\d+)*'
        clause_matches = re.findall(clause_pattern, contract_content, re.IGNORECASE)

        for clause in clause_matches:
            sources.append({
                'type': 'contract_clause',
                'reference': clause,
                'title': f'Contract {clause}'
            })

        # Extract date references
        date_pattern = r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b'
        date_matches = re.findall(date_pattern, contract_content)

        for date in date_matches[:5]:  # Limit to first 5 dates
            sources.append({
                'type': 'contract_date',
                'reference': date,
                'title': f'Contract Date: {date}'
            })

        return sources

    def _format_analysis_response(
        self,
        analysis_results: Dict[str, Any],
        analysis_type: str
    ) -> str:
        """Format the final analysis response."""
        response_parts = []

        # Add main analysis
        response_parts.append(analysis_results['analysis'])

        # Add risk summary if risks found
        risk_findings = analysis_results.get('risk_findings', [])
        if risk_findings:
            response_parts.append("\n## Risk Summary")
            high_risks = [r for r in risk_findings if r['risk_level'] == 'high']
            medium_risks = [r for r in risk_findings if r['risk_level'] == 'medium']

            if high_risks:
                response_parts.append(f"**High Risk Items ({len(high_risks)}):**")
                for risk in high_risks[:5]:
                    response_parts.append(f"- {risk['risk_type'].replace('_', ' ').title()}: {risk['recommendation']}")

            if medium_risks:
                response_parts.append(f"**Medium Risk Items ({len(medium_risks)}):**")
                for risk in medium_risks[:3]:
                    response_parts.append(f"- {risk['risk_type'].replace('_', ' ').title()}: {risk['recommendation']}")

        # Add recommendations
        recommendations = analysis_results.get('recommendations', [])
        if recommendations:
            response_parts.append("\n## Key Recommendations")
            for i, rec in enumerate(recommendations[:5], 1):
                response_parts.append(f"{i}. {rec}")

        # Add analysis metadata
        response_parts.append(f"\n## Analysis Summary")
        response_parts.append(f"Analysis Type: {analysis_type.replace('_', ' ').title()}")
        response_parts.append(f"Risks Identified: {len(risk_findings)}")
        response_parts.append(f"Recommendations: {len(recommendations)}")

        return "\n\n".join(response_parts)

    def _calculate_analysis_confidence(self, analysis_results: Dict[str, Any]) -> float:
        """Calculate confidence score for analysis results."""
        base_confidence = 0.8  # Base confidence for contract analysis

        # Factors affecting confidence
        factors = {
            'content_length': len(analysis_results.get('contract_content', '')),
            'risks_found': len(analysis_results.get('risk_findings', [])),
            'recommendations_count': len(analysis_results.get('recommendations', [])),
            'structured_analysis': bool(analysis_results.get('structured_analysis'))
        }

        # Adjust confidence based on factors
        if factors['content_length'] > 1000:
            base_confidence += 0.05
        elif factors['content_length'] < 100:
            base_confidence -= 0.2

        if factors['risks_found'] > 0:
            base_confidence += 0.05

        if factors['structured_analysis']:
            base_confidence += 0.05

        return max(0.0, min(1.0, base_confidence))

    def _generate_analysis_metadata(
        self,
        analysis_results: Dict[str, Any],
        analysis_type: str
    ) -> Dict[str, Any]:
        """Generate metadata for analysis response."""
        return {
            'analysis_type': analysis_type,
            'contract_length': len(analysis_results.get('contract_content', '')),
            'risks_identified': len(analysis_results.get('risk_findings', [])),
            'recommendations_count': len(analysis_results.get('recommendations', [])),
            'processing_timestamp': datetime.utcnow().isoformat(),
            'agent_specialization': 'contract_analysis',
            'risk_categories': list(set(r['category'] for r in analysis_results.get('risk_findings', [])))
        }

    def _parse_structured_response(self, response_text: str) -> Dict[str, Any]:
        """Parse structured analysis response into dictionary."""
        # Simplified parsing - in practice, would use more sophisticated NLP
        structured = {
            'key_terms': [],
            'risks': [],
            'compliance_issues': [],
            'recommendations': [],
            'critical_clauses': []
        }

        lines = response_text.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Simple categorization based on keywords
            if any(term in line.lower() for term in ['recommend', 'suggest', 'consider']):
                structured['recommendations'].append(line)
            elif any(term in line.lower() for term in ['risk', 'issue', 'concern']):
                structured['risks'].append(line)
            elif any(term in line.lower() for term in ['term', 'condition', 'clause']):
                structured['key_terms'].append(line)
            elif any(term in line.lower() for term in ['compliance', 'regulatory']):
                structured['compliance_issues'].append(line)

        return structured

    # Analysis tool implementations
    def _extract_contract_clauses(self, contract_content: str) -> List[Dict[str, Any]]:
        """Extract individual clauses from contract."""
        clauses = []

        # Simple clause extraction based on numbering
        clause_patterns = [
            r'(?:Section|Clause|Article)\s+\d+(?:\.\d+)*\s*[.\-]\s*([^\n]+)',
            r'\d+\.\s*([^\n]+)',
            r'\([a-z]\)\s*([^\n]+)'
        ]

        for pattern in clause_patterns:
            matches = re.finditer(pattern, contract_content, re.IGNORECASE)
            for match in matches:
                clauses.append({
                    'clause_number': match.group(0).split()[1] if len(match.groups()) > 1 else match.group(0),
                    'content': match.group(1) if len(match.groups()) > 1 else match.group(0),
                    'position': match.start()
                })

        return clauses

    def _check_compliance(self, contract_content: str, regulations: List[str] = None) -> List[Dict[str, Any]]:
        """Check contract for compliance with regulations."""
        # This would integrate with regulatory databases
        # Simplified implementation
        compliance_issues = []

        content_lower = contract_content.lower()

        # Basic compliance checks
        if 'personal data' in content_lower and 'gdpr' not in content_lower:
            compliance_issues.append({
                'regulation': 'GDPR',
                'issue': 'Personal data processing without GDPR reference',
                'severity': 'high'
            })

        return compliance_issues

    def _compare_to_standard_terms(self, contract_content: str) -> List[Dict[str, Any]]:
        """Compare contract terms to industry standards."""
        # This would integrate with contract template databases
        # Simplified implementation
        comparisons = []

        # Example comparison for force majeure
        if 'force majeure' in contract_content.lower():
            comparisons.append({
                'term': 'force_majeure',
                'comparison': 'Standard term present',
                'assessment': 'aligned_with_standards'
            })

        return comparisons