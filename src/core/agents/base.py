"""
Abstract base classes and interfaces for AI agents.

This module defines the core interfaces and base classes that all
AI agents in the system must implement, ensuring consistency
and enabling pluggable agent architectures.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Protocol
from dataclasses import dataclass
from enum import Enum


class AgentRole(str, Enum):
    """Enumeration of possible agent roles."""
    LEGAL_RESEARCHER = "legal_researcher"
    CONTRACT_ANALYST = "contract_analyst"
    LEGAL_STRATEGIST = "legal_strategist"
    TEAM_LEAD = "team_lead"
    DOCUMENT_ANALYST = "document_analyst"
    COMPLIANCE_CHECKER = "compliance_checker"
    LEGAL_SPECIALIST = "legal_specialist"


class AgentCapability(str, Enum):
    """Enumeration of agent capabilities."""
    DOCUMENT_ANALYSIS = "document_analysis"
    LEGAL_RESEARCH = "legal_research"
    CONTRACT_REVIEW = "contract_review"
    RISK_ASSESSMENT = "risk_assessment"
    STRATEGY_DEVELOPMENT = "strategy_development"
    COMPLIANCE_CHECKING = "compliance_checking"
    TEAM_COORDINATION = "team_coordination"


class ModelSelectionStrategy(str, Enum):
    """Model selection strategies for agents."""
    BEST_ANSWER = "best_answer"      # Prioritize quality over cost/speed
    LEAST_COST = "least_cost"        # Minimize API costs
    SPEED = "speed"                  # Fastest response time
    BALANCED = "balanced"            # Balance quality, cost, and speed
    ADAPTIVE = "adaptive"            # Learn and adapt based on performance


@dataclass
class AgentResponse:
    """Standardized response format for agent interactions."""
    content: str
    confidence_score: float
    metadata: Dict[str, Any]
    sources: List[Dict[str, Any]]
    processing_time: float
    tokens_used: Optional[int] = None
    model_used: Optional[str] = None

    def __post_init__(self):
        """Validate response after initialization."""
        if not (0 <= self.confidence_score <= 1):
            raise ValueError("Confidence score must be between 0 and 1")
        if self.processing_time < 0:
            raise ValueError("Processing time cannot be negative")


@dataclass
class AgentContext:
    """Context information for agent operations."""
    session_id: str
    user_id: Optional[str] = None
    document_ids: List[str] = None
    query: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        """Initialize defaults."""
        if self.document_ids is None:
            self.document_ids = []
        if self.metadata is None:
            self.metadata = {}


class KnowledgeBaseProtocol(Protocol):
    """Protocol for knowledge base interactions."""

    def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search the knowledge base for relevant information."""
        ...

    def store(self, document_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Store document content in the knowledge base."""
        ...

    def retrieve(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve document from the knowledge base."""
        ...


class AIProviderProtocol(Protocol):
    """Protocol for AI provider interactions."""

    def generate_response(
        self,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResponse:
        """Generate a response using the AI provider."""
        ...

    def is_available(self) -> bool:
        """Check if the AI provider is available."""
        ...

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the AI model."""
        ...


class Agent(ABC):
    """
    Abstract base class for all AI agents in the system.

    This class defines the interface that all agents must implement,
    ensuring consistency across different agent types and enabling
    the pluggable agent architecture.
    """

    def __init__(
        self,
        name: str,
        role: AgentRole,
        capabilities: List[AgentCapability],
        ai_provider: AIProviderProtocol,
        knowledge_base: Optional[KnowledgeBaseProtocol] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the agent.

        Args:
            name: Human-readable name for the agent
            role: The agent's primary role
            capabilities: List of capabilities this agent possesses
            ai_provider: AI provider for generating responses
            knowledge_base: Optional knowledge base for context
            config: Optional configuration parameters
        """
        self.name = name
        self.role = role
        self.capabilities = capabilities
        self.ai_provider = ai_provider
        self.knowledge_base = knowledge_base
        self.config = config or {}

        # Agent state
        self.is_initialized = False
        self.last_activity = None
        self.interaction_count = 0

    @abstractmethod
    async def initialize(self) -> bool:
        """
        Initialize the agent and its dependencies.

        Returns:
            bool: True if initialization successful
        """
        pass

    @abstractmethod
    async def process_query(
        self,
        query: str,
        context: Optional[AgentContext] = None
    ) -> AgentResponse:
        """
        Process a query and generate a response.

        Args:
            query: The query to process
            context: Optional context information

        Returns:
            AgentResponse: The agent's response
        """
        pass

    @abstractmethod
    def get_capabilities(self) -> List[AgentCapability]:
        """
        Get the list of capabilities this agent supports.

        Returns:
            List of supported capabilities
        """
        return self.capabilities

    @abstractmethod
    def can_handle_query(self, query: str, context: Optional[AgentContext] = None) -> bool:
        """
        Determine if this agent can handle the given query.

        Args:
            query: The query to evaluate
            context: Optional context information

        Returns:
            bool: True if the agent can handle the query
        """
        pass

    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the agent.

        Returns:
            Dict containing agent metadata
        """
        return {
            "name": self.name,
            "role": self.role.value,
            "capabilities": [cap.value for cap in self.capabilities],
            "is_initialized": self.is_initialized,
            "interaction_count": self.interaction_count,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "ai_provider": self.ai_provider.get_model_info() if hasattr(self.ai_provider, 'get_model_info') else None,
            "knowledge_base_available": self.knowledge_base is not None,
        }

    def update_activity(self):
        """Update the agent's last activity timestamp."""
        from datetime import datetime
        self.last_activity = datetime.utcnow()
        self.interaction_count += 1

    def validate_capabilities(self, required_capabilities: List[AgentCapability]) -> bool:
        """
        Validate that the agent has the required capabilities.

        Args:
            required_capabilities: List of required capabilities

        Returns:
            bool: True if all required capabilities are present
        """
        return all(cap in self.capabilities for cap in required_capabilities)

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the agent.

        Returns:
            Dict containing health check results
        """
        health_status = {
            "agent_name": self.name,
            "is_initialized": self.is_initialized,
            "ai_provider_available": await self._check_ai_provider(),
            "knowledge_base_available": await self._check_knowledge_base(),
            "last_health_check": None,
            "issues": []
        }

        # Check for any issues
        if not self.is_initialized:
            health_status["issues"].append("Agent not initialized")

        if not health_status["ai_provider_available"]:
            health_status["issues"].append("AI provider not available")

        if self.knowledge_base and not health_status["knowledge_base_available"]:
            health_status["issues"].append("Knowledge base not available")

        health_status["healthy"] = len(health_status["issues"]) == 0

        from datetime import datetime
        health_status["last_health_check"] = datetime.utcnow().isoformat()

        return health_status

    async def _check_ai_provider(self) -> bool:
        """Check if the AI provider is available."""
        try:
            return self.ai_provider.is_available()
        except Exception:
            return False

    async def _check_knowledge_base(self) -> bool:
        """Check if the knowledge base is available."""
        if not self.knowledge_base:
            return True  # Not required, so considered available

        try:
            # Perform a simple test query
            test_results = self.knowledge_base.search("test", limit=1)
            return test_results is not None
        except Exception:
            return False


class TeamCoordinator(Agent):
    """
    Abstract base class for agents that coordinate other agents.

    This class extends the base Agent class with team coordination
    capabilities, allowing agents to delegate tasks and synthesize
    responses from multiple team members.
    """

    def __init__(
        self,
        name: str,
        role: AgentRole,
        capabilities: List[AgentCapability],
        ai_provider: AIProviderProtocol,
        team_members: List[Agent],
        knowledge_base: Optional[KnowledgeBaseProtocol] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the team coordinator.

        Args:
            name: Human-readable name for the coordinator
            role: The coordinator's primary role
            capabilities: List of capabilities this coordinator possesses
            ai_provider: AI provider for generating responses
            team_members: List of agents this coordinator manages
            knowledge_base: Optional knowledge base for context
            config: Optional configuration parameters
        """
        super().__init__(name, role, capabilities, ai_provider, knowledge_base, config)
        self.team_members = team_members
        self.coordination_history = []

    @abstractmethod
    async def delegate_task(
        self,
        task: str,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """
        Delegate a task to appropriate team members.

        Args:
            task: The task to delegate
            context: Optional context information

        Returns:
            Dict containing delegation results
        """
        pass

    @abstractmethod
    async def synthesize_responses(
        self,
        responses: List[AgentResponse],
        original_query: str,
        context: Optional[AgentContext] = None
    ) -> AgentResponse:
        """
        Synthesize multiple agent responses into a coherent final response.

        Args:
            responses: List of responses from team members
            original_query: The original query
            context: Optional context information

        Returns:
            AgentResponse: Synthesized final response
        """
        pass

    def get_team_status(self) -> Dict[str, Any]:
        """
        Get the status of all team members.

        Returns:
            Dict containing team status information
        """
        team_status = {
            "coordinator": self.name,
            "total_members": len(self.team_members),
            "members": []
        }

        for member in self.team_members:
            member_status = {
                "name": member.name,
                "role": member.role.value,
                "capabilities": [cap.value for cap in member.capabilities],
                "is_initialized": member.is_initialized,
                "interaction_count": member.interaction_count,
            }
            team_status["members"].append(member_status)

        return team_status

    def find_agents_by_capability(self, capability: AgentCapability) -> List[Agent]:
        """
        Find agents that possess a specific capability.

        Args:
            capability: The capability to search for

        Returns:
            List of agents with the specified capability
        """
        return [
            agent for agent in self.team_members
            if capability in agent.capabilities
        ]

    def find_best_agent_for_query(
        self,
        query: str,
        context: Optional[AgentContext] = None
    ) -> Optional[Agent]:
        """
        Find the best agent to handle a specific query.

        Args:
            query: The query to evaluate
            context: Optional context information

        Returns:
            Agent best suited for the query, or None if no suitable agent found
        """
        suitable_agents = [
            agent for agent in self.team_members
            if agent.can_handle_query(query, context)
        ]

        if not suitable_agents:
            return None

        # For now, return the first suitable agent
        # In the future, this could use more sophisticated selection logic
        return suitable_agents[0]

    async def coordinate_team_response(
        self,
        query: str,
        context: Optional[AgentContext] = None
    ) -> AgentResponse:
        """
        Coordinate a team response to a query.

        Args:
            query: The query to process
            context: Optional context information

        Returns:
            AgentResponse: Coordinated team response
        """
        # Delegate the task to appropriate team members
        delegation_result = await self.delegate_task(query, context)

        if not delegation_result["assigned_agents"]:
            # If no agents can handle the query, handle it directly
            return await self.process_query(query, context)

        # Collect responses from assigned agents
        responses = []
        for agent in delegation_result["assigned_agents"]:
            try:
                response = await agent.process_query(query, context)
                responses.append(response)
            except Exception as e:
                # Log the error but continue with other agents
                print(f"Error getting response from {agent.name}: {e}")

        if not responses:
            # Fallback to direct processing if all agents failed
            return await self.process_query(query, context)

        # Synthesize the responses
        final_response = await self.synthesize_responses(responses, query, context)

        # Record the coordination
        self.coordination_history.append({
            "query": query,
            "assigned_agents": [agent.name for agent in delegation_result["assigned_agents"]],
            "response_count": len(responses),
            "timestamp": None,  # Will be set by calling code
        })

        return final_response


# Factory functions for creating agents
def create_agent(
    agent_type: str,
    name: str,
    ai_provider: AIProviderProtocol,
    knowledge_base: Optional[KnowledgeBaseProtocol] = None,
    config: Optional[Dict[str, Any]] = None
) -> Agent:
    """
    Factory function to create agents of different types.

    Args:
        agent_type: Type of agent to create
        name: Name for the agent
        ai_provider: AI provider for the agent
        knowledge_base: Optional knowledge base
        config: Optional configuration

    Returns:
        Agent: Created agent instance

    Raises:
        ValueError: If agent type is not supported
    """
    if agent_type == "legal_researcher":
        from .legal_researcher import LegalResearcher
        return LegalResearcher(name, ai_provider, knowledge_base, config)
    elif agent_type == "contract_analyst":
        from .contract_analyst import ContractAnalyst
        return ContractAnalyst(name, ai_provider, knowledge_base, config)
    elif agent_type == "legal_strategist":
        from .legal_strategist import LegalStrategist
        return LegalStrategist(name, ai_provider, knowledge_base, config)
    elif agent_type == "team_lead":
        # Team lead requires team members, so this is a simplified version
        from .team_lead import TeamLead
        return TeamLead(name, ai_provider, [], knowledge_base, config)
    else:
        raise ValueError(f"Unsupported agent type: {agent_type}")


def create_team_coordinator(
    name: str,
    ai_provider: AIProviderProtocol,
    team_members: List[Agent],
    knowledge_base: Optional[KnowledgeBaseProtocol] = None,
    config: Optional[Dict[str, Any]] = None
) -> TeamCoordinator:
    """
    Factory function to create team coordinators.

    Args:
        name: Name for the coordinator
        ai_provider: AI provider for the coordinator
        team_members: List of agents to coordinate
        knowledge_base: Optional knowledge base
        config: Optional configuration

    Returns:
        TeamCoordinator: Created coordinator instance
    """
    from .team_lead import TeamLead
    return TeamLead(name, ai_provider, team_members, knowledge_base, config)