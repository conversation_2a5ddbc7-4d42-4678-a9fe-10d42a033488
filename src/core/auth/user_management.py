#!/usr/bin/env python3
"""
User Management and Authentication System for AI Law Firm.

This module provides comprehensive user management, authentication, and authorization:
- User registration and login with secure password hashing
- JWT token-based authentication with refresh tokens
- Role-based access control (RBAC) with granular permissions
- Multi-tenant user isolation and management
- API key management for external integrations
- Usage tracking and quota management
- Session management and security monitoring
- User profile and preference management
- Password reset and account recovery
- Audit logging for security events
"""

import asyncio
import hashlib
import hmac
import json
import secrets
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

import asyncpg
import redis
from motor.motor_asyncio import AsyncIOMotorClient
import bcrypt
import jwt
from pydantic import BaseModel, EmailStr, Field, validator

from utils.logging import get_logger
from utils.exceptions import AuthenticationError, AuthorizationError, ValidationError


class UserRole(str, Enum):
    """User role enumeration with hierarchical permissions."""
    SUPER_ADMIN = "super_admin"      # Full system access
    ADMIN = "admin"                  # Organization management
    MANAGER = "manager"              # Team management
    USER = "user"                    # Standard user
    GUEST = "guest"                  # Limited access


class UserStatus(str, Enum):
    """User account status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"
    LOCKED = "locked"


class Permission(str, Enum):
    """Granular permission enumeration."""
    # System Administration
    SYSTEM_ADMIN = "system:admin"
    USER_MANAGEMENT = "user:management"
    ORGANIZATION_MANAGEMENT = "organization:management"

    # Document Operations
    DOCUMENT_UPLOAD = "document:upload"
    DOCUMENT_VIEW = "document:view"
    DOCUMENT_EDIT = "document:edit"
    DOCUMENT_DELETE = "document:delete"
    DOCUMENT_SHARE = "document:share"

    # Analysis Operations
    ANALYSIS_RUN = "analysis:run"
    ANALYSIS_VIEW = "analysis:view"
    ANALYSIS_EXPORT = "analysis:export"

    # Search Operations
    SEARCH_BASIC = "search:basic"
    SEARCH_ADVANCED = "search:advanced"
    SEARCH_EXPORT = "search:export"

    # API Access
    API_ACCESS = "api:access"
    API_KEY_MANAGEMENT = "api:key_management"

    # Billing & Usage
    BILLING_VIEW = "billing:view"
    BILLING_MANAGE = "billing:manage"
    USAGE_VIEW = "usage:view"
    USAGE_MANAGE = "usage:manage"


@dataclass
class User:
    """User model with comprehensive profile information."""
    user_id: str
    email: str
    username: str
    first_name: str
    last_name: str
    role: UserRole
    status: UserStatus
    organization_id: Optional[str] = None
    hashed_password: Optional[str] = None
    email_verified: bool = False
    phone_number: Optional[str] = None
    avatar_url: Optional[str] = None
    preferences: Dict[str, Any] = field(default_factory=dict)
    api_keys: List[Dict[str, Any]] = field(default_factory=list)
    usage_limits: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    last_login_at: Optional[datetime] = None
    password_reset_token: Optional[str] = None
    password_reset_expires: Optional[datetime] = None

    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"

    @property
    def is_active(self) -> bool:
        """Check if user account is active."""
        return self.status == UserStatus.ACTIVE

    def has_permission(self, permission: Permission) -> bool:
        """Check if user has specific permission based on role."""
        role_permissions = self._get_role_permissions()
        return permission.value in role_permissions

    def _get_role_permissions(self) -> List[str]:
        """Get permissions for user's role."""
        role_hierarchy = {
            UserRole.SUPER_ADMIN: [
                Permission.SYSTEM_ADMIN, Permission.USER_MANAGEMENT,
                Permission.ORGANIZATION_MANAGEMENT, Permission.DOCUMENT_UPLOAD,
                Permission.DOCUMENT_VIEW, Permission.DOCUMENT_EDIT,
                Permission.DOCUMENT_DELETE, Permission.DOCUMENT_SHARE,
                Permission.ANALYSIS_RUN, Permission.ANALYSIS_VIEW,
                Permission.ANALYSIS_EXPORT, Permission.SEARCH_BASIC,
                Permission.SEARCH_ADVANCED, Permission.SEARCH_EXPORT,
                Permission.API_ACCESS, Permission.API_KEY_MANAGEMENT,
                Permission.BILLING_VIEW, Permission.BILLING_MANAGE,
                Permission.USAGE_VIEW, Permission.USAGE_MANAGE
            ],
            UserRole.ADMIN: [
                Permission.USER_MANAGEMENT, Permission.ORGANIZATION_MANAGEMENT,
                Permission.DOCUMENT_UPLOAD, Permission.DOCUMENT_VIEW,
                Permission.DOCUMENT_EDIT, Permission.DOCUMENT_DELETE,
                Permission.DOCUMENT_SHARE, Permission.ANALYSIS_RUN,
                Permission.ANALYSIS_VIEW, Permission.ANALYSIS_EXPORT,
                Permission.SEARCH_BASIC, Permission.SEARCH_ADVANCED,
                Permission.SEARCH_EXPORT, Permission.API_ACCESS,
                Permission.BILLING_VIEW, Permission.USAGE_VIEW
            ],
            UserRole.MANAGER: [
                Permission.DOCUMENT_UPLOAD, Permission.DOCUMENT_VIEW,
                Permission.DOCUMENT_EDIT, Permission.DOCUMENT_SHARE,
                Permission.ANALYSIS_RUN, Permission.ANALYSIS_VIEW,
                Permission.SEARCH_BASIC, Permission.SEARCH_ADVANCED,
                Permission.USAGE_VIEW
            ],
            UserRole.USER: [
                Permission.DOCUMENT_UPLOAD, Permission.DOCUMENT_VIEW,
                Permission.ANALYSIS_RUN, Permission.ANALYSIS_VIEW,
                Permission.SEARCH_BASIC, Permission.USAGE_VIEW
            ],
            UserRole.GUEST: [
                Permission.DOCUMENT_VIEW, Permission.SEARCH_BASIC
            ]
        }

        return [p.value for p in role_hierarchy.get(self.role, [])]


@dataclass
class Organization:
    """Organization model for multi-tenant support."""
    organization_id: str
    name: str
    domain: Optional[str] = None
    description: Optional[str] = None
    settings: Dict[str, Any] = field(default_factory=dict)
    usage_limits: Dict[str, Any] = field(default_factory=dict)
    billing_info: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class APIKey:
    """API key model for external integrations."""
    key_id: str
    user_id: str
    name: str
    key_hash: str
    permissions: List[str]
    expires_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    usage_count: int = 0
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class AuthToken:
    """Authentication token model."""
    token_id: str
    user_id: str
    token_type: str  # access, refresh
    token_hash: str
    expires_at: datetime
    created_at: datetime = field(default_factory=datetime.utcnow)
    revoked_at: Optional[datetime] = None


class UserRegistrationRequest(BaseModel):
    """User registration request model."""
    email: EmailStr
    username: str
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None

    @validator('password')
    def password_strength(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(char.isdigit() for char in v):
            raise ValueError('Password must contain at least one digit')
        if not any(char.isupper() for char in v):
            raise ValueError('Password must contain at least one uppercase letter')
        return v

    @validator('username')
    def username_format(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, underscores, and hyphens')
        return v.lower()


class LoginRequest(BaseModel):
    """Login request model."""
    username_or_email: str
    password: str
    remember_me: bool = False


class TokenResponse(BaseModel):
    """Token response model."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]


class UserManagementService:
    """
    Comprehensive user management and authentication service.

    Features:
    - Secure user registration and authentication
    - JWT token management with refresh tokens
    - Role-based access control (RBAC)
    - Multi-tenant organization support
    - API key management
    - Usage tracking and quota management
    - Session management and security monitoring
    - Password reset and account recovery
    - Audit logging for security events
    """

    def __init__(self, db_config: Dict[str, Any], jwt_config: Dict[str, Any]):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.db_config = db_config
        self.jwt_config = jwt_config

        # Database connections
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None
        self.mongo_db = None

        # JWT settings
        self.jwt_secret = jwt_config.get('secret', 'dummy-jwt-secret-for-development')
        self.jwt_algorithm = jwt_config.get('algorithm', 'HS256')
        self.access_token_expire_minutes = jwt_config.get('access_token_expire_minutes', 30)
        self.refresh_token_expire_days = jwt_config.get('refresh_token_expire_days', 7)

        # Password settings
        self.password_salt_rounds = 12

        # Rate limiting
        self.max_login_attempts = 5
        self.login_lockout_minutes = 15

    async def initialize(self):
        """Initialize database connections and service."""
        try:
            # PostgreSQL connection
            self.pg_pool = await asyncpg.create_pool(
                host=self.db_config.get('postgres_host', 'localhost'),
                port=self.db_config.get('postgres_port', 54320),
                database=self.db_config.get('postgres_db', 'ai_law_firm'),
                user=self.db_config.get('postgres_user', 'ai_law_user'),
                password=self.db_config.get('postgres_password', 'ai_law_password_2024'),
                min_size=1,
                max_size=10
            )

            # Redis connection
            self.redis_client = redis.Redis(
                host=self.db_config.get('redis_host', 'localhost'),
                port=self.db_config.get('redis_port', 63790),
                password=self.db_config.get('redis_password', 'ai_law_redis_password_2024'),
                decode_responses=True
            )

            # MongoDB connection
            mongo_uri = f"mongodb://{self.db_config.get('mongo_host', 'localhost')}:{self.db_config.get('mongo_port', 27019)}"
            self.mongo_client = AsyncIOMotorClient(mongo_uri)
            self.mongo_db = self.mongo_client.ai_law_firm

            # Create database tables if they don't exist
            await self._create_tables()

            self.logger.info("User Management Service initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize user management service: {str(e)}")
            return False

    async def _create_tables(self):
        """Create necessary database tables."""
        try:
            async with self.pg_pool.acquire() as conn:
                # Users table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS users (
                        user_id VARCHAR(36) PRIMARY KEY,
                        email VARCHAR(255) UNIQUE NOT NULL,
                        username VARCHAR(50) UNIQUE NOT NULL,
                        first_name VARCHAR(100) NOT NULL,
                        last_name VARCHAR(100) NOT NULL,
                        role VARCHAR(20) NOT NULL,
                        status VARCHAR(20) NOT NULL,
                        organization_id VARCHAR(36),
                        hashed_password VARCHAR(255),
                        email_verified BOOLEAN DEFAULT FALSE,
                        phone_number VARCHAR(20),
                        avatar_url VARCHAR(500),
                        preferences JSONB DEFAULT '{}',
                        api_keys JSONB DEFAULT '[]',
                        usage_limits JSONB DEFAULT '{}',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_login_at TIMESTAMP,
                        password_reset_token VARCHAR(255),
                        password_reset_expires TIMESTAMP
                    )
                """)

                # Organizations table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS organizations (
                        organization_id VARCHAR(36) PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        domain VARCHAR(255),
                        description TEXT,
                        settings JSONB DEFAULT '{}',
                        usage_limits JSONB DEFAULT '{}',
                        billing_info JSONB DEFAULT '{}',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # API Keys table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS api_keys (
                        key_id VARCHAR(36) PRIMARY KEY,
                        user_id VARCHAR(36) NOT NULL REFERENCES users(user_id),
                        name VARCHAR(100) NOT NULL,
                        key_hash VARCHAR(255) NOT NULL,
                        permissions JSONB DEFAULT '[]',
                        expires_at TIMESTAMP,
                        last_used_at TIMESTAMP,
                        usage_count INTEGER DEFAULT 0,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Auth Tokens table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS auth_tokens (
                        token_id VARCHAR(36) PRIMARY KEY,
                        user_id VARCHAR(36) NOT NULL REFERENCES users(user_id),
                        token_type VARCHAR(20) NOT NULL,
                        token_hash VARCHAR(255) NOT NULL,
                        expires_at TIMESTAMP NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        revoked_at TIMESTAMP
                    )
                """)

                # Login attempts table for rate limiting
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS login_attempts (
                        attempt_id SERIAL PRIMARY KEY,
                        username_or_email VARCHAR(255) NOT NULL,
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        success BOOLEAN NOT NULL,
                        attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Create indexes
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_users_organization ON users(organization_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_api_keys_user ON api_keys(user_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_auth_tokens_user ON auth_tokens(user_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_login_attempts_username ON login_attempts(username_or_email)")

        except Exception as e:
            self.logger.error(f"Error creating tables: {str(e)}")

    async def register_user(self, registration_data: UserRegistrationRequest) -> User:
        """
        Register a new user.

        Args:
            registration_data: User registration information

        Returns:
            User: Created user object

        Raises:
            ValidationError: If registration data is invalid
            AuthenticationError: If user already exists
        """
        try:
            # Check if user already exists
            async with self.pg_pool.acquire() as conn:
                existing_user = await conn.fetchrow(
                    "SELECT user_id FROM users WHERE email = $1 OR username = $2",
                    registration_data.email, registration_data.username
                )

                if existing_user:
                    raise AuthenticationError("User with this email or username already exists")

                # Create organization if provided
                organization_id = None
                if registration_data.organization_name:
                    org = await self.create_organization(registration_data.organization_name)
                    organization_id = org.organization_id

                # Hash password
                hashed_password = self._hash_password(registration_data.password)

                # Create user
                user_id = str(uuid.uuid4())
                user = User(
                    user_id=user_id,
                    email=registration_data.email,
                    username=registration_data.username,
                    first_name=registration_data.first_name,
                    last_name=registration_data.last_name,
                    role=UserRole.USER,  # Default role
                    status=UserStatus.PENDING_VERIFICATION,
                    organization_id=organization_id,
                    hashed_password=hashed_password,
                    email_verified=False
                )

                # Save to database
                await conn.execute("""
                    INSERT INTO users (
                        user_id, email, username, first_name, last_name, role, status,
                        organization_id, hashed_password, email_verified, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                """, user.user_id, user.email, user.username, user.first_name, user.last_name,
                     user.role.value, user.status.value, user.organization_id, user.hashed_password,
                     user.email_verified, user.created_at, user.updated_at)

                # Log registration
                await self._log_security_event(
                    "user_registration",
                    user_id=user.user_id,
                    details={"email": user.email, "username": user.username}
                )

                self.logger.info(f"User registered successfully: {user.username}")
                return user

        except Exception as e:
            self.logger.error(f"Error registering user: {str(e)}")
            raise

    async def authenticate_user(self, login_data: LoginRequest) -> TokenResponse:
        """
        Authenticate user and return tokens.

        Args:
            login_data: Login credentials

        Returns:
            TokenResponse: Authentication tokens and user info

        Raises:
            AuthenticationError: If authentication fails
        """
        try:
            # Check rate limiting
            if await self._is_rate_limited(login_data.username_or_email):
                raise AuthenticationError("Too many login attempts. Please try again later.")

            # Get user
            async with self.pg_pool.acquire() as conn:
                user_data = await conn.fetchrow("""
                    SELECT * FROM users
                    WHERE (email = $1 OR username = $1) AND status = $2
                """, login_data.username_or_email, UserStatus.ACTIVE.value)

                if not user_data:
                    await self._record_login_attempt(login_data.username_or_email, False)
                    raise AuthenticationError("Invalid username/email or password")

                # Verify password
                if not self._verify_password(login_data.password, user_data['hashed_password']):
                    await self._record_login_attempt(login_data.username_or_email, False)
                    raise AuthenticationError("Invalid username/email or password")

                # Record successful login
                await self._record_login_attempt(login_data.username_or_email, True)

                # Update last login
                await conn.execute(
                    "UPDATE users SET last_login_at = $1, updated_at = $1 WHERE user_id = $2",
                    datetime.utcnow(), user_data['user_id']
                )

                # Create user object
                user = User(
                    user_id=user_data['user_id'],
                    email=user_data['email'],
                    username=user_data['username'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    role=UserRole(user_data['role']),
                    status=UserStatus(user_data['status']),
                    organization_id=user_data['organization_id'],
                    hashed_password=user_data['hashed_password'],
                    email_verified=user_data['email_verified'],
                    phone_number=user_data['phone_number'],
                    avatar_url=user_data['avatar_url'],
                    preferences=user_data['preferences'] or {},
                    api_keys=user_data['api_keys'] or [],
                    usage_limits=user_data['usage_limits'] or {},
                    created_at=user_data['created_at'],
                    updated_at=user_data['updated_at'],
                    last_login_at=user_data['last_login_at']
                )

                # Generate tokens
                access_token = self._generate_access_token(user)
                refresh_token = self._generate_refresh_token(user)

                # Store refresh token
                await self._store_refresh_token(user.user_id, refresh_token)

                # Create response
                token_response = TokenResponse(
                    access_token=access_token,
                    refresh_token=refresh_token,
                    token_type="bearer",
                    expires_in=self.access_token_expire_minutes * 60,
                    user={
                        "user_id": user.user_id,
                        "email": user.email,
                        "username": user.username,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "role": user.role.value,
                        "organization_id": user.organization_id
                    }
                )

                # Log successful authentication
                await self._log_security_event(
                    "user_login",
                    user_id=user.user_id,
                    details={"ip_address": "system", "user_agent": "api"}
                )

                return token_response

        except Exception as e:
            self.logger.error(f"Error authenticating user: {str(e)}")
            raise

    async def refresh_access_token(self, refresh_token: str) -> TokenResponse:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Refresh token

        Returns:
            TokenResponse: New authentication tokens

        Raises:
            AuthenticationError: If refresh token is invalid
        """
        try:
            # Verify refresh token
            payload = jwt.decode(refresh_token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            user_id = payload.get("sub")

            if not user_id:
                raise AuthenticationError("Invalid refresh token")

            # Check if refresh token exists and is valid
            async with self.pg_pool.acquire() as conn:
                token_data = await conn.fetchrow("""
                    SELECT * FROM auth_tokens
                    WHERE user_id = $1 AND token_type = 'refresh' AND revoked_at IS NULL
                    AND expires_at > $2
                """, user_id, datetime.utcnow())

                if not token_data:
                    raise AuthenticationError("Invalid or expired refresh token")

                # Get user
                user_data = await conn.fetchrow(
                    "SELECT * FROM users WHERE user_id = $1 AND status = $2",
                    user_id, UserStatus.ACTIVE.value
                )

                if not user_data:
                    raise AuthenticationError("User not found or inactive")

                # Create user object
                user = User(
                    user_id=user_data['user_id'],
                    email=user_data['email'],
                    username=user_data['username'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    role=UserRole(user_data['role']),
                    status=UserStatus(user_data['status']),
                    organization_id=user_data['organization_id']
                )

                # Generate new tokens
                access_token = self._generate_access_token(user)
                new_refresh_token = self._generate_refresh_token(user)

                # Revoke old refresh token
                await conn.execute(
                    "UPDATE auth_tokens SET revoked_at = $1 WHERE token_id = $2",
                    datetime.utcnow(), token_data['token_id']
                )

                # Store new refresh token
                await self._store_refresh_token(user.user_id, new_refresh_token)

                return TokenResponse(
                    access_token=access_token,
                    refresh_token=new_refresh_token,
                    token_type="bearer",
                    expires_in=self.access_token_expire_minutes * 60,
                    user={
                        "user_id": user.user_id,
                        "email": user.email,
                        "username": user.username,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "role": user.role.value,
                        "organization_id": user.organization_id
                    }
                )

        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Refresh token has expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid refresh token")
        except Exception as e:
            self.logger.error(f"Error refreshing token: {str(e)}")
            raise

    async def validate_access_token(self, token: str) -> User:
        """
        Validate access token and return user.

        Args:
            token: Access token

        Returns:
            User: User object if token is valid

        Raises:
            AuthenticationError: If token is invalid
        """
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            user_id = payload.get("sub")

            if not user_id:
                raise AuthenticationError("Invalid token")

            # Get user from database
            async with self.pg_pool.acquire() as conn:
                user_data = await conn.fetchrow(
                    "SELECT * FROM users WHERE user_id = $1 AND status = $2",
                    user_id, UserStatus.ACTIVE.value
                )

                if not user_data:
                    raise AuthenticationError("User not found or inactive")

                return User(
                    user_id=user_data['user_id'],
                    email=user_data['email'],
                    username=user_data['username'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    role=UserRole(user_data['role']),
                    status=UserStatus(user_data['status']),
                    organization_id=user_data['organization_id'],
                    preferences=user_data['preferences'] or {},
                    usage_limits=user_data['usage_limits'] or {}
                )

        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
        except Exception as e:
            self.logger.error(f"Error validating token: {str(e)}")
            raise

    async def create_organization(self, name: str, domain: Optional[str] = None) -> Organization:
        """
        Create a new organization.

        Args:
            name: Organization name
            domain: Organization domain (optional)

        Returns:
            Organization: Created organization
        """
        try:
            async with self.pg_pool.acquire() as conn:
                # Check if organization already exists
                existing_org = await conn.fetchrow(
                    "SELECT organization_id FROM organizations WHERE name = $1",
                    name
                )

                if existing_org:
                    raise ValidationError(f"Organization '{name}' already exists")

                # Create organization
                organization_id = str(uuid.uuid4())
                organization = Organization(
                    organization_id=organization_id,
                    name=name,
                    domain=domain
                )

                await conn.execute("""
                    INSERT INTO organizations (organization_id, name, domain, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5)
                """, organization.organization_id, organization.name, organization.domain,
                     organization.created_at, organization.updated_at)

                self.logger.info(f"Organization created: {organization.name}")
                return organization

        except Exception as e:
            self.logger.error(f"Error creating organization: {str(e)}")
            raise

    async def create_api_key(
        self,
        user_id: str,
        name: str,
        permissions: List[str],
        expires_at: Optional[datetime] = None
    ) -> Tuple[str, APIKey]:
        """
        Create API key for user.

        Args:
            user_id: User ID
            name: API key name
            permissions: List of permissions
            expires_at: Expiration date (optional)

        Returns:
            Tuple[str, APIKey]: Raw API key and API key object
        """
        try:
            # Generate API key
            raw_key = secrets.token_urlsafe(32)
            key_hash = self._hash_api_key(raw_key)

            # Create API key object
            key_id = str(uuid.uuid4())
            api_key = APIKey(
                key_id=key_id,
                user_id=user_id,
                name=name,
                key_hash=key_hash,
                permissions=permissions,
                expires_at=expires_at
            )

            # Save to database
            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO api_keys (
                        key_id, user_id, name, key_hash, permissions, expires_at, created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """, api_key.key_id, api_key.user_id, api_key.name, api_key.key_hash,
                     api_key.permissions, api_key.expires_at, api_key.created_at)

            # Log API key creation
            await self._log_security_event(
                "api_key_created",
                user_id=user_id,
                details={"key_name": name, "permissions": permissions}
            )

            return raw_key, api_key

        except Exception as e:
            self.logger.error(f"Error creating API key: {str(e)}")
            raise

    async def validate_api_key(self, api_key: str) -> Optional[User]:
        """
        Validate API key and return user.

        Args:
            api_key: API key to validate

        Returns:
            User: User object if API key is valid, None otherwise
        """
        try:
            key_hash = self._hash_api_key(api_key)

            async with self.pg_pool.acquire() as conn:
                # Find API key
                key_data = await conn.fetchrow("""
                    SELECT k.*, u.* FROM api_keys k
                    JOIN users u ON k.user_id = u.user_id
                    WHERE k.key_hash = $1 AND k.is_active = TRUE
                    AND (k.expires_at IS NULL OR k.expires_at > $2)
                    AND u.status = $3
                """, key_hash, datetime.utcnow(), UserStatus.ACTIVE.value)

                if not key_data:
                    return None

                # Update usage
                await conn.execute("""
                    UPDATE api_keys
                    SET usage_count = usage_count + 1, last_used_at = $1
                    WHERE key_id = $2
                """, datetime.utcnow(), key_data['key_id'])

                # Create user object
                return User(
                    user_id=key_data['user_id'],
                    email=key_data['email'],
                    username=key_data['username'],
                    first_name=key_data['first_name'],
                    last_name=key_data['last_name'],
                    role=UserRole(key_data['role']),
                    status=UserStatus(key_data['status']),
                    organization_id=key_data['organization_id'],
                    preferences=key_data['preferences'] or {},
                    usage_limits=key_data['usage_limits'] or {}
                )

        except Exception as e:
            self.logger.error(f"Error validating API key: {str(e)}")
            return None

    async def check_permission(self, user: User, permission: Permission, resource_id: Optional[str] = None) -> bool:
        """
        Check if user has specific permission.

        Args:
            user: User object
            permission: Permission to check
            resource_id: Resource ID for resource-specific permissions

        Returns:
            bool: True if user has permission
        """
        try:
            # Check role-based permissions
            if not user.has_permission(permission):
                return False

            # Additional resource-specific checks can be added here
            if resource_id:
                # Check resource ownership or sharing permissions
                pass

            return True

        except Exception as e:
            self.logger.error(f"Error checking permission: {str(e)}")
            return False

    async def track_usage(self, user_id: str, action: str, resource_type: str, resource_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        Track user usage for billing and analytics.

        Args:
            user_id: User ID
            action: Action performed
            resource_type: Type of resource used
            resource_id: Resource ID (optional)
            metadata: Additional metadata
        """
        try:
            usage_data = {
                'user_id': user_id,
                'action': action,
                'resource_type': resource_type,
                'resource_id': resource_id,
                'metadata': metadata or {},
                'timestamp': datetime.utcnow()
            }

            # Store in MongoDB for analytics
            await self.mongo_db.usage_tracking.insert_one(usage_data)

            # Update Redis counters for real-time tracking
            redis_key = f"usage:{user_id}:{resource_type}"
            self.redis_client.incr(redis_key)
            self.redis_client.expire(redis_key, 86400)  # Expire in 24 hours

        except Exception as e:
            self.logger.error(f"Error tracking usage: {str(e)}")

    async def get_user_usage(self, user_id: str, time_range: str = "month") -> Dict[str, Any]:
        """
        Get user usage statistics.

        Args:
            user_id: User ID
            time_range: Time range for usage data

        Returns:
            Dict: Usage statistics
        """
        try:
            # Calculate date range
            now = datetime.utcnow()
            if time_range == "day":
                start_date = now - timedelta(days=1)
            elif time_range == "week":
                start_date = now - timedelta(weeks=1)
            elif time_range == "month":
                start_date = now - timedelta(days=30)
            else:
                start_date = now - timedelta(days=30)

            # Get usage from MongoDB
            pipeline = [
                {"$match": {"user_id": user_id, "timestamp": {"$gte": start_date}}},
                {"$group": {
                    "_id": {
                        "action": "$action",
                        "resource_type": "$resource_type"
                    },
                    "count": {"$sum": 1},
                    "last_used": {"$max": "$timestamp"}
                }},
                {"$sort": {"count": -1}}
            ]

            usage_stats = await self.mongo_db.usage_tracking.aggregate(pipeline).to_list(None)

            return {
                'user_id': user_id,
                'time_range': time_range,
                'start_date': start_date,
                'end_date': now,
                'usage_stats': usage_stats
            }

        except Exception as e:
            self.logger.error(f"Error getting user usage: {str(e)}")
            return {}

    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(self.password_salt_rounds)).decode('utf-8')

    def _verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))

    def _hash_api_key(self, api_key: str) -> str:
        """Hash API key using SHA-256."""
        return hashlib.sha256(api_key.encode('utf-8')).hexdigest()

    def _generate_access_token(self, user: User) -> str:
        """Generate JWT access token."""
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        payload = {
            "sub": user.user_id,
            "email": user.email,
            "username": user.username,
            "role": user.role.value,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)

    def _generate_refresh_token(self, user: User) -> str:
        """Generate JWT refresh token."""
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        payload = {
            "sub": user.user_id,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        }
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)

    async def _store_refresh_token(self, user_id: str, refresh_token: str):
        """Store refresh token in database."""
        try:
            token_id = str(uuid.uuid4())
            token_hash = hashlib.sha256(refresh_token.encode('utf-8')).hexdigest()
            expires_at = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)

            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO auth_tokens (token_id, user_id, token_type, token_hash, expires_at, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, token_id, user_id, "refresh", token_hash, expires_at, datetime.utcnow())

        except Exception as e:
            self.logger.error(f"Error storing refresh token: {str(e)}")

    async def _is_rate_limited(self, username_or_email: str) -> bool:
        """Check if user is rate limited for login attempts."""
        try:
            async with self.pg_pool.acquire() as conn:
                # Count failed attempts in the last lockout period
                cutoff_time = datetime.utcnow() - timedelta(minutes=self.login_lockout_minutes)

                failed_attempts = await conn.fetchval("""
                    SELECT COUNT(*) FROM login_attempts
                    WHERE username_or_email = $1 AND success = FALSE
                    AND attempted_at > $2
                """, username_or_email, cutoff_time)

                return failed_attempts >= self.max_login_attempts

        except Exception as e:
            self.logger.error(f"Error checking rate limit: {str(e)}")
            return False

    async def _record_login_attempt(self, username_or_email: str, success: bool):
        """Record login attempt for rate limiting."""
        try:
            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO login_attempts (username_or_email, success, attempted_at)
                    VALUES ($1, $2, $3)
                """, username_or_email, success, datetime.utcnow())

        except Exception as e:
            self.logger.error(f"Error recording login attempt: {str(e)}")

    async def _log_security_event(self, event_type: str, user_id: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """Log security event for audit purposes."""
        try:
            security_event = {
                'event_type': event_type,
                'user_id': user_id,
                'details': details or {},
                'timestamp': datetime.utcnow(),
                'ip_address': 'system',  # Would be populated from request context
                'user_agent': 'api'      # Would be populated from request context
            }

            await self.mongo_db.security_events.insert_one(security_event)

        except Exception as e:
            self.logger.error(f"Error logging security event: {str(e)}")

    async def close(self):
        """Close all database connections."""
        if self.pg_pool:
            await self.pg_pool.close()
        if self.redis_client:
            self.redis_client.close()
        if self.mongo_client:
            self.mongo_client.close()

        self.logger.info("User Management Service connections closed")


# Convenience functions

async def authenticate_user(login_data: LoginRequest, db_config: Dict[str, Any] = None, jwt_config: Dict[str, Any] = None) -> TokenResponse:
    """Convenience function for user authentication."""
    if db_config is None:
        db_config = {
            'postgres_host': 'localhost',
            'postgres_port': 54320,
            'postgres_db': 'ai_law_firm',
            'postgres_user': 'ai_law_user',
            'postgres_password': 'ai_law_password_2024',
            'redis_host': 'localhost',
            'redis_port': 63790,
            'redis_password': 'ai_law_redis_password_2024',
            'mongo_host': 'localhost',
            'mongo_port': 27019
        }

    if jwt_config is None:
        jwt_config = {
            'secret': 'dummy-jwt-secret-for-development',
            'algorithm': 'HS256',
            'access_token_expire_minutes': 30,
            'refresh_token_expire_days': 7
        }

    service = UserManagementService(db_config, jwt_config)
    if await service.initialize():
        result = await service.authenticate_user(login_data)
        await service.close()
        return result
    else:
        raise Exception("Failed to initialize user management service")


async def validate_token(token: str, db_config: Dict[str, Any] = None, jwt_config: Dict[str, Any] = None) -> User:
    """Convenience function for token validation."""
    if db_config is None:
        db_config = {
            'postgres_host': 'localhost',
            'postgres_port': 54320,
            'postgres_db': 'ai_law_firm',
            'postgres_user': 'ai_law_user',
            'postgres_password': 'ai_law_password_2024',
            'redis_host': 'localhost',
            'redis_port': 63790,
            'redis_password': 'ai_law_redis_password_2024',
            'mongo_host': 'localhost',
            'mongo_port': 27019
        }

    if jwt_config is None:
        jwt_config = {
            'secret': 'dummy-jwt-secret-for-development',
            'algorithm': 'HS256',
            'access_token_expire_minutes': 30,
            'refresh_token_expire_days': 7
        }

    service = UserManagementService(db_config, jwt_config)
    if await service.initialize():
        result = await service.validate_access_token(token)
        await service.close()
        return result
    else:
        raise Exception("Failed to initialize user management service")


# UserManager class for backward compatibility
class UserManager:
    """
    UserManager class that wraps UserManagementService for backward compatibility.
    """

    def __init__(self, db_config: Dict[str, Any], jwt_config: Dict[str, Any]):
        self.service = UserManagementService(db_config, jwt_config)

    async def initialize(self):
        """Initialize the user management service."""
        return await self.service.initialize()

    async def authenticate_user(self, login_data: LoginRequest) -> TokenResponse:
        """Authenticate user."""
        return await self.service.authenticate_user(login_data)

    async def validate_token(self, token: str) -> User:
        """Validate access token."""
        return await self.service.validate_access_token(token)

    async def register_user(self, registration_data: UserRegistrationRequest) -> User:
        """Register new user."""
        return await self.service.register_user(registration_data)

    async def close(self):
        """Close connections."""
        await self.service.close()


# Export key classes and functions
__all__ = [
    "UserManagementService",
    "UserManager",  # Added for backward compatibility
    "User",
    "Organization",
    "APIKey",
    "AuthToken",
    "UserRole",
    "UserStatus",
    "Permission",
    "UserRegistrationRequest",
    "LoginRequest",
    "TokenResponse",
    "authenticate_user",
    "validate_token"
]