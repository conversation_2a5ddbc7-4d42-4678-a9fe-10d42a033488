#!/usr/bin/env python3
"""
Smart Document Classification for AI Law Firm.

This module provides AI-powered document classification capabilities:
- Document type classification (contracts, briefs, pleadings, etc.)
- Legal domain detection (employment, corporate, IP, etc.)
- Risk level assessment and scoring
- Key clause and provision extraction
- Automated tagging and categorization
- Confidence scoring and uncertainty handling
- Learning from user feedback and corrections
"""

import asyncio
import json
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import asyncpg
import redis
from motor.motor_asyncio import AsyncIOMotorClient

from utils.logging import get_logger
from infrastructure.ai_providers.base import AIProvider


class DocumentType(Enum):
    """Primary document type classifications."""
    CONTRACT = "contract"
    LEGAL_BRIEF = "legal_brief"
    COURT_DOCUMENT = "court_document"
    PLEADING = "pleading"
    AGREEMENT = "agreement"
    MEMORANDUM = "memorandum"
    CORRESPONDENCE = "correspondence"
    REGULATORY_FILING = "regulatory_filing"
    INTELLECTUAL_PROPERTY = "intellectual_property"
    EMPLOYMENT_DOCUMENT = "employment_document"
    CORPORATE_DOCUMENT = "corporate_document"
    LITIGATION_DOCUMENT = "litigation_document"
    COMPLIANCE_DOCUMENT = "compliance_document"
    OTHER = "other"


class LegalDomain(Enum):
    """Legal practice area classifications."""
    EMPLOYMENT_LAW = "employment_law"
    CORPORATE_LAW = "corporate_law"
    INTELLECTUAL_PROPERTY = "intellectual_property"
    CONTRACT_LAW = "contract_law"
    LITIGATION = "litigation"
    REGULATORY_COMPLIANCE = "regulatory_compliance"
    REAL_ESTATE = "real_estate"
    TAX_LAW = "tax_law"
    FAMILY_LAW = "family_law"
    CRIMINAL_LAW = "criminal_law"
    ADMINISTRATIVE_LAW = "administrative_law"
    INTERNATIONAL_LAW = "international_law"
    ENVIRONMENTAL_LAW = "environmental_law"
    HEALTH_LAW = "health_law"
    IMMIGRATION_LAW = "immigration_law"
    BANKING_FINANCE = "banking_finance"
    INSURANCE = "insurance"
    CONSTRUCTION = "construction"
    ENERGY = "energy"
    TELECOMMUNICATIONS = "telecommunications"
    OTHER = "other"


class RiskLevel(Enum):
    """Risk level classifications."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ClassificationResult:
    """Complete classification result."""
    document_id: str
    document_type: DocumentType
    legal_domain: LegalDomain
    risk_level: RiskLevel
    confidence_scores: Dict[str, float]
    key_clauses: List[Dict[str, Any]]
    tags: List[str]
    extracted_entities: Dict[str, List[str]]
    summary: str
    recommendations: List[str]
    processing_metadata: Dict[str, Any]
    classified_at: datetime


@dataclass
class ClassificationTraining:
    """Training data for classification improvement."""
    document_id: str
    original_classification: Dict[str, Any]
    corrected_classification: Dict[str, Any]
    user_feedback: str
    feedback_timestamp: datetime


class SmartDocumentClassifier:
    """
    AI-powered document classification system for legal documents.

    Features:
    - Multi-level classification (type, domain, risk)
    - AI-powered content analysis and entity extraction
    - Confidence scoring and uncertainty handling
    - Learning from user feedback and corrections
    - Performance monitoring and caching
    - Batch processing capabilities
    """

    def __init__(self, db_config: Dict[str, Any], ai_provider: Optional[AIProvider] = None):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.db_config = db_config
        self.ai_provider = ai_provider

        # Database connections
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None
        self.mongo_db = None

        # Classification cache
        self.classification_cache_ttl = 3600  # 1 hour

        # Classification patterns and keywords
        self._initialize_classification_patterns()

    async def initialize(self):
        """Initialize database connections and classification system."""
        try:
            # PostgreSQL connection
            self.pg_pool = await asyncpg.create_pool(
                host=self.db_config.get('postgres_host', 'localhost'),
                port=self.db_config.get('postgres_port', 54320),
                database=self.db_config.get('postgres_db', 'ai_law_firm'),
                user=self.db_config.get('postgres_user', 'ai_law_user'),
                password=self.db_config.get('postgres_password', 'ai_law_password_2024'),
                min_size=1,
                max_size=10
            )

            # Redis connection
            self.redis_client = redis.Redis(
                host=self.db_config.get('redis_host', 'localhost'),
                port=self.db_config.get('redis_port', 63790),
                password=self.db_config.get('redis_password', 'ai_law_redis_password_2024'),
                decode_responses=True
            )

            # MongoDB connection
            mongo_uri = f"mongodb://{self.db_config.get('mongo_host', 'localhost')}:{self.db_config.get('mongo_port', 27019)}"
            self.mongo_client = AsyncIOMotorClient(mongo_uri)
            self.mongo_db = self.mongo_client.ai_law_firm

            self.logger.info("Smart Document Classifier initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize classifier: {str(e)}")
            return False

    def _initialize_classification_patterns(self):
        """Initialize classification patterns and keywords."""
        self.document_type_patterns = {
            DocumentType.CONTRACT: [
                r'\b(agreement|contract|accord|treaty)\b',
                r'\b(parties|party|hereinafter|hereto)\b',
                r'\b(terms|conditions|provisions|clauses)\b',
                r'\b(breach|default|termination|remedies)\b'
            ],
            DocumentType.LEGAL_BRIEF: [
                r'\b(brief|memorandum|motion|petition)\b',
                r'\b(court|judge|jurisdiction|venue)\b',
                r'\b(plaintiff|defendant|petitioner|respondent)\b',
                r'\b(precedent|case law|stare decisis)\b'
            ],
            DocumentType.COURT_DOCUMENT: [
                r'\b(court|superior court|district court)\b',
                r'\b(judge|justice|magistrate)\b',
                r'\b(order|ruling|judgment|decree)\b',
                r'\b(case|matter|docket|file)\b'
            ],
            DocumentType.EMPLOYMENT_DOCUMENT: [
                r'\b(employment|employee|employer)\b',
                r'\b(salary|wage|compensation|benefits)\b',
                r'\b(termination|severance|notice)\b',
                r'\b(at-will|probation|performance)\b'
            ]
        }

        self.legal_domain_keywords = {
            LegalDomain.EMPLOYMENT_LAW: [
                'employment', 'labor', 'wage', 'discrimination', 'harassment',
                'wrongful termination', 'fmla', 'flsa', 'nlra', 'union'
            ],
            LegalDomain.CORPORATE_LAW: [
                'corporation', 'shareholder', 'board', 'merger', 'acquisition',
                'fiduciary duty', 'corporate governance', 'ipo', 'securities'
            ],
            LegalDomain.INTELLECTUAL_PROPERTY: [
                'patent', 'trademark', 'copyright', 'trade secret', 'ip',
                'infringement', 'licensing', 'royalty', 'intellectual property'
            ],
            LegalDomain.CONTRACT_LAW: [
                'contract', 'agreement', 'breach', 'consideration', 'offer',
                'acceptance', 'novation', 'force majeure', 'liquidated damages'
            ]
        }

    async def classify_document(
        self,
        document_id: str,
        use_ai: bool = True
    ) -> ClassificationResult:
        """
        Classify a document comprehensively.

        Args:
            document_id: Document ID to classify
            use_ai: Whether to use AI for enhanced classification

        Returns:
            ClassificationResult with complete analysis
        """
        start_time = datetime.utcnow()

        try:
            # Check cache first
            cache_key = f"classification:{document_id}"
            cached_result = self._get_cached_classification(cache_key)
            if cached_result:
                self.logger.info(f"Classification cache hit for {document_id}")
                return cached_result

            # Fetch document data
            doc_data = await self._fetch_document_data(document_id)
            if not doc_data:
                raise ValueError(f"Document {document_id} not found")

            # Perform multi-level classification
            document_type, type_confidence = await self._classify_document_type(doc_data)
            legal_domain, domain_confidence = await self._classify_legal_domain(doc_data)
            risk_level, risk_confidence = await self._assess_risk_level(doc_data)

            # Extract key information
            key_clauses = await self._extract_key_clauses(doc_data)
            tags = await self._generate_tags(doc_data)
            entities = await self._extract_entities(doc_data)

            # Generate AI-enhanced analysis if requested
            summary = ""
            recommendations = []
            if use_ai and self.ai_provider:
                ai_analysis = await self._perform_ai_analysis(doc_data, document_type, legal_domain)
                summary = ai_analysis.get('summary', '')
                recommendations = ai_analysis.get('recommendations', [])

            # Calculate confidence scores
            confidence_scores = {
                'document_type': type_confidence,
                'legal_domain': domain_confidence,
                'risk_level': risk_confidence,
                'overall': (type_confidence + domain_confidence + risk_confidence) / 3
            }

            # Create result
            result = ClassificationResult(
                document_id=document_id,
                document_type=document_type,
                legal_domain=legal_domain,
                risk_level=risk_level,
                confidence_scores=confidence_scores,
                key_clauses=key_clauses,
                tags=tags,
                extracted_entities=entities,
                summary=summary,
                recommendations=recommendations,
                processing_metadata={
                    'processing_time_ms': (datetime.utcnow() - start_time).total_seconds() * 1000,
                    'ai_used': use_ai and self.ai_provider is not None,
                    'classification_method': 'hybrid' if use_ai else 'rule_based'
                },
                classified_at=datetime.utcnow()
            )

            # Cache result
            self._cache_classification(cache_key, result)

            # Store classification in database
            await self._store_classification_result(result)

            # Update classification analytics
            await self._update_classification_analytics(result)

            return result

        except Exception as e:
            self.logger.error(f"Classification error for {document_id}: {str(e)}")
            raise

    async def _classify_document_type(self, doc_data: Dict[str, Any]) -> Tuple[DocumentType, float]:
        """Classify document type using pattern matching and AI."""
        content = doc_data.get('content', '').lower()
        filename = doc_data.get('filename', '').lower()

        # Score each document type
        type_scores = {}

        for doc_type, patterns in self.document_type_patterns.items():
            score = 0
            matches = 0

            # Check filename
            for pattern in patterns:
                if re.search(pattern, filename, re.IGNORECASE):
                    score += 0.3
                    matches += 1

            # Check content
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    score += 0.7
                    matches += 1

            # Boost score based on number of matches
            if matches > 0:
                score *= (1 + matches * 0.1)

            type_scores[doc_type] = min(score, 1.0)

        # Find best match
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            confidence = type_scores[best_type]
        else:
            best_type = DocumentType.OTHER
            confidence = 0.0

        # Use AI for refinement if available
        if self.ai_provider and confidence < 0.7:
            ai_type, ai_confidence = await self._classify_with_ai(doc_data, "document_type")
            if ai_confidence > confidence:
                try:
                    best_type = DocumentType(ai_type.lower().replace(' ', '_'))
                    confidence = ai_confidence
                except:
                    pass

        return best_type, confidence

    async def _classify_legal_domain(self, doc_data: Dict[str, Any]) -> Tuple[LegalDomain, float]:
        """Classify legal domain using keyword analysis and AI."""
        content = doc_data.get('content', '').lower()
        metadata = doc_data.get('metadata', {})

        # Score each legal domain
        domain_scores = {}

        for domain, keywords in self.legal_domain_keywords.items():
            score = 0
            matches = 0

            for keyword in keywords:
                if keyword.lower() in content:
                    score += 0.8
                    matches += 1

            # Check metadata
            if isinstance(metadata, dict):
                for key, value in metadata.items():
                    if isinstance(value, str) and keyword.lower() in value.lower():
                        score += 0.2
                        matches += 1

            # Boost score based on matches
            if matches > 0:
                score *= (1 + matches * 0.05)

            domain_scores[domain] = min(score, 1.0)

        # Find best match
        if domain_scores:
            best_domain = max(domain_scores, key=domain_scores.get)
            confidence = domain_scores[best_domain]
        else:
            best_domain = LegalDomain.OTHER
            confidence = 0.0

        # Use AI for refinement
        if self.ai_provider and confidence < 0.6:
            ai_domain, ai_confidence = await self._classify_with_ai(doc_data, "legal_domain")
            if ai_confidence > confidence:
                try:
                    best_domain = LegalDomain(ai_domain.lower().replace(' ', '_'))
                    confidence = ai_confidence
                except:
                    pass

        return best_domain, confidence

    async def _assess_risk_level(self, doc_data: Dict[str, Any]) -> Tuple[RiskLevel, float]:
        """Assess risk level of the document."""
        content = doc_data.get('content', '').lower()

        # Risk indicators
        high_risk_keywords = [
            'penalty', 'fine', 'liability', 'breach', 'termination',
            'default', 'liquidated damages', 'indemnification', 'warranty'
        ]

        medium_risk_keywords = [
            'compliance', 'regulation', 'deadline', 'notice', 'approval',
            'consent', 'condition', 'obligation', 'requirement'
        ]

        # Count risk indicators
        high_risk_count = sum(1 for keyword in high_risk_keywords if keyword in content)
        medium_risk_count = sum(1 for keyword in medium_risk_keywords if keyword in content)

        # Calculate risk score
        risk_score = (high_risk_count * 0.7) + (medium_risk_count * 0.3)
        risk_score = min(risk_score / 10, 1.0)  # Normalize

        # Determine risk level
        if risk_score > 0.7:
            risk_level = RiskLevel.CRITICAL
        elif risk_score > 0.4:
            risk_level = RiskLevel.HIGH
        elif risk_score > 0.2:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW

        confidence = min(risk_score + 0.3, 1.0)  # Add base confidence

        return risk_level, confidence

    async def _extract_key_clauses(self, doc_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract key clauses and provisions from the document."""
        content = doc_data.get('content', '')

        # Common clause patterns
        clause_patterns = {
            'termination': r'(termination|ending|conclusion).*?(?=[\n\r]|$)',
            'confidentiality': r'(confidential|non.disclosure).*?(?=[\n\r]|$)',
            'compensation': r'(compensation|salary|payment|fee).*?(?=[\n\r]|$)',
            'liability': r'(liability|responsibility|obligation).*?(?=[\n\r]|$)',
            'governing_law': r'(governing law|jurisdiction|venue).*?(?=[\n\r]|$)',
            'force_majeure': r'(force majeure|act of god|unforeseeable).*?(?=[\n\r]|$)',
            'severability': r'(severability|severable|invalid).*?(?=[\n\r]|$)',
            'assignment': r'(assignment|transfer|successor).*?(?=[\n\r]|$)',
            'waiver': r'(waiver|amendment|modification).*?(?=[\n\r]|$)'
        }

        key_clauses = []

        for clause_type, pattern in clause_patterns.items():
            matches = re.finditer(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                clause_text = match.group().strip()
                if len(clause_text) > 20:  # Only meaningful clauses
                    key_clauses.append({
                        'type': clause_type,
                        'text': clause_text[:200] + "..." if len(clause_text) > 200 else clause_text,
                        'position': match.start(),
                        'confidence': 0.8
                    })

        return key_clauses[:10]  # Limit to top 10 clauses

    async def _generate_tags(self, doc_data: Dict[str, Any]) -> List[str]:
        """Generate automated tags for the document."""
        content = doc_data.get('content', '').lower()
        tags = []

        # Document type tags
        if 'contract' in content:
            tags.append('contract')
        if 'agreement' in content:
            tags.append('agreement')
        if 'employment' in content:
            tags.append('employment')
        if 'confidential' in content:
            tags.append('confidentiality')

        # Legal domain tags
        if 'intellectual property' in content or 'patent' in content:
            tags.append('intellectual-property')
        if 'corporate' in content or 'shareholder' in content:
            tags.append('corporate')
        if 'litigation' in content or 'court' in content:
            tags.append('litigation')

        # Risk level tags
        if 'penalty' in content or 'fine' in content:
            tags.append('high-risk')
        if 'deadline' in content or 'compliance' in content:
            tags.append('time-sensitive')

        return list(set(tags))  # Remove duplicates

    async def _extract_entities(self, doc_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Extract named entities from the document."""
        content = doc_data.get('content', '')

        entities = {
            'companies': [],
            'people': [],
            'dates': [],
            'monetary_values': [],
            'legal_references': []
        }

        # Extract company names (simple pattern)
        company_pattern = r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:Inc|LLC|Corp|Corporation|Company|Partners|Associates|Ltd))\b'
        companies = re.findall(company_pattern, content)
        entities['companies'] = list(set(companies))[:5]  # Limit to 5

        # Extract dates
        date_pattern = r'\b(\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}[/-]\d{1,2}[/-]\d{1,2})\b'
        dates = re.findall(date_pattern, content)
        entities['dates'] = list(set(dates))[:5]

        # Extract monetary values
        money_pattern = r'\$\d+(?:,\d{3})*(?:\.\d{2})?'
        money_values = re.findall(money_pattern, content)
        entities['monetary_values'] = list(set(money_values))[:5]

        return entities

    async def _classify_with_ai(self, doc_data: Dict[str, Any], classification_type: str) -> Tuple[str, float]:
        """Use AI for enhanced classification."""
        if not self.ai_provider:
            return "unknown", 0.0

        try:
            # Prepare classification prompt
            content_preview = doc_data.get('content', '')[:1000]  # Limit content
            filename = doc_data.get('filename', '')

            prompt = f"""
            Analyze this legal document and classify its {classification_type}.

            Filename: {filename}

            Content Preview:
            {content_preview}

            Based on the filename and content, classify the {classification_type}.
            Provide your answer in JSON format:
            {{
                "classification": "specific_type_or_domain",
                "confidence": 0.0 to 1.0,
                "reasoning": "brief explanation"
            }}
            """

            response = await self.ai_provider.generate_response(
                prompt=prompt,
                temperature=0.1,
                max_tokens=200
            )

            # Parse response
            try:
                result = json.loads(response.content)
                return result.get('classification', 'unknown'), result.get('confidence', 0.5)
            except:
                return "unknown", 0.3

        except Exception as e:
            self.logger.error(f"AI classification error: {str(e)}")
            return "unknown", 0.0

    async def _perform_ai_analysis(
        self,
        doc_data: Dict[str, Any],
        document_type: DocumentType,
        legal_domain: LegalDomain
    ) -> Dict[str, Any]:
        """Perform comprehensive AI analysis of the document."""
        if not self.ai_provider:
            return {}

        try:
            content_preview = doc_data.get('content', '')[:1500]

            prompt = f"""
            Provide a comprehensive analysis of this legal document.

            Document Type: {document_type.value}
            Legal Domain: {legal_domain.value}
            Filename: {doc_data.get('filename', '')}

            Content Preview:
            {content_preview}

            Provide analysis in JSON format:
            {{
                "summary": "2-3 sentence summary of the document",
                "recommendations": ["3-5 key recommendations for review or action"],
                "key_risks": ["2-3 main risk factors identified"],
                "compliance_notes": ["Any compliance considerations"]
            }}
            """

            response = await self.ai_provider.generate_response(
                prompt=prompt,
                temperature=0.2,
                max_tokens=500
            )

            try:
                return json.loads(response.content)
            except:
                return {
                    "summary": "Document analysis completed.",
                    "recommendations": ["Review document thoroughly"],
                    "key_risks": [],
                    "compliance_notes": []
                }

        except Exception as e:
            self.logger.error(f"AI analysis error: {str(e)}")
            return {}

    async def _fetch_document_data(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Fetch complete document data."""
        try:
            # Fetch from PostgreSQL
            async with self.pg_pool.acquire() as conn:
                pg_data = await conn.fetchrow("""
                    SELECT * FROM documents WHERE document_id = $1
                """, document_id)

            if not pg_data:
                return None

            # Parse metadata
            metadata = pg_data['metadata']
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except:
                    metadata = {}

            # Fetch from MongoDB
            try:
                mongo_data = await self.mongo_db.documents.find_one({"document_id": document_id})
                content = mongo_data.get('content', '') if mongo_data else ''
            except:
                content = ''

            return {
                'document_id': str(pg_data['document_id']),
                'filename': pg_data['filename'],
                'content_type': pg_data['content_type'],
                'file_size': pg_data['file_size'],
                'upload_timestamp': pg_data['upload_timestamp'],
                'metadata': metadata,
                'content': content
            }

        except Exception as e:
            self.logger.error(f"Error fetching document {document_id}: {str(e)}")
            return None

    def _get_cached_classification(self, cache_key: str) -> Optional[ClassificationResult]:
        """Get cached classification result."""
        try:
            cached = self.redis_client.get(cache_key)
            if cached:
                # Would need proper deserialization in production
                return None
        except:
            pass
        return None

    def _cache_classification(self, cache_key: str, result: ClassificationResult):
        """Cache classification result."""
        try:
            # Simplified caching
            self.redis_client.setex(cache_key, self.classification_cache_ttl, "cached")
        except:
            pass

    async def _store_classification_result(self, result: ClassificationResult):
        """Store classification result in database."""
        try:
            # Store in PostgreSQL
            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE documents
                    SET metadata = jsonb_set(
                        COALESCE(metadata, '{}'),
                        '{classification}',
                        $2::jsonb
                    )
                    WHERE document_id = $1
                """, result.document_id, json.dumps({
                    'document_type': result.document_type.value,
                    'legal_domain': result.legal_domain.value,
                    'risk_level': result.risk_level.value,
                    'confidence_scores': result.confidence_scores,
                    'tags': result.tags,
                    'classified_at': result.classified_at.isoformat()
                }))

            # Store detailed result in MongoDB
            await self.mongo_db.classifications.insert_one({
                'document_id': result.document_id,
                'classification': {
                    'document_type': result.document_type.value,
                    'legal_domain': result.legal_domain.value,
                    'risk_level': result.risk_level.value,
                    'confidence_scores': result.confidence_scores,
                    'key_clauses': result.key_clauses,
                    'tags': result.tags,
                    'extracted_entities': result.extracted_entities,
                    'summary': result.summary,
                    'recommendations': result.recommendations
                },
                'processing_metadata': result.processing_metadata,
                'classified_at': result.classified_at
            })

        except Exception as e:
            self.logger.error(f"Error storing classification result: {str(e)}")

    async def _update_classification_analytics(self, result: ClassificationResult):
        """Update classification analytics."""
        try:
            # Update counters
            self.redis_client.incr("classifications:total")

            # Update type counters
            self.redis_client.incr(f"classifications:type:{result.document_type.value}")
            self.redis_client.incr(f"classifications:domain:{result.legal_domain.value}")
            self.redis_client.incr(f"classifications:risk:{result.risk_level.value}")

            # Update processing times
            processing_time = result.processing_metadata.get('processing_time_ms', 0)
            self.redis_client.lpush("classifications:processing_times", processing_time)
            self.redis_client.ltrim("classifications:processing_times", 0, 99)

        except Exception as e:
            self.logger.error(f"Error updating classification analytics: {str(e)}")

    async def learn_from_feedback(self, feedback: ClassificationTraining):
        """Learn from user feedback to improve classification."""
        try:
            # Store feedback for future model training
            await self.mongo_db.classification_feedback.insert_one({
                'document_id': feedback.document_id,
                'original_classification': feedback.original_classification,
                'corrected_classification': feedback.corrected_classification,
                'user_feedback': feedback.user_feedback,
                'feedback_timestamp': feedback.feedback_timestamp
            })

            self.logger.info(f"Stored classification feedback for document {feedback.document_id}")

        except Exception as e:
            self.logger.error(f"Error storing classification feedback: {str(e)}")

    async def close(self):
        """Close all database connections."""
        if self.pg_pool:
            await self.pg_pool.close()
        if self.redis_client:
            self.redis_client.close()
        if self.mongo_client:
            self.mongo_client.close()

        self.logger.info("Smart Document Classifier connections closed")


# Convenience functions

async def classify_document(
    document_id: str,
    db_config: Dict[str, Any] = None,
    ai_provider: Optional[AIProvider] = None,
    use_ai: bool = True
) -> ClassificationResult:
    """Convenience function for document classification."""
    if db_config is None:
        db_config = {
            'postgres_host': 'localhost',
            'postgres_port': 54320,
            'postgres_db': 'ai_law_firm',
            'postgres_user': 'ai_law_user',
            'postgres_password': 'ai_law_password_2024',
            'redis_host': 'localhost',
            'redis_port': 63790,
            'redis_password': 'ai_law_redis_password_2024',
            'mongo_host': 'localhost',
            'mongo_port': 27019
        }

    classifier = SmartDocumentClassifier(db_config, ai_provider)
    if await classifier.initialize():
        result = await classifier.classify_document(document_id, use_ai)
        await classifier.close()
        return result
    else:
        raise Exception("Failed to initialize document classifier")


# SmartClassifier class for backward compatibility
class SmartClassifier:
    """
    SmartClassifier class that wraps SmartDocumentClassifier for backward compatibility.
    """

    def __init__(self, ai_provider, knowledge_base):
        from infrastructure.ai_providers.base import AIProvider
        from core.knowledge.base import KnowledgeBase

        # Convert to expected format
        db_config = {
            'postgres_host': 'localhost',
            'postgres_port': 54320,
            'postgres_db': 'ai_law_firm',
            'postgres_user': 'ai_law_user',
            'postgres_password': 'ai_law_password_2024',
            'redis_host': 'localhost',
            'redis_port': 63790,
            'redis_password': 'ai_law_redis_password_2024',
            'mongo_host': 'localhost',
            'mongo_port': 27019
        }

        self.classifier = SmartDocumentClassifier(db_config, ai_provider)

    async def initialize(self):
        """Initialize the classifier."""
        return await self.classifier.initialize()

    async def classify_document(self, document_id: str, use_ai: bool = True):
        """Classify a document."""
        return await self.classifier.classify_document(document_id, use_ai)

    async def close(self):
        """Close connections."""
        await self.classifier.close()


# Export key classes and functions
__all__ = [
    "SmartDocumentClassifier",
    "SmartClassifier",  # Added for backward compatibility
    "ClassificationResult",
    "ClassificationTraining",
    "DocumentType",
    "LegalDomain",
    "RiskLevel",
    "classify_document"
]