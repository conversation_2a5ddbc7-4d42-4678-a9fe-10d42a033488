#!/usr/bin/env python3
"""
Real-Time Analytics Dashboard for AI Law Firm.

This module provides comprehensive analytics and monitoring capabilities
for the AI Law Firm system, leveraging data from PostgreSQL, Redis, and MongoDB.
"""

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

import asyncpg
import redis
from motor.motor_asyncio import AsyncIOMotorClient

from utils.logging import get_logger


class MetricType(Enum):
    """Types of metrics tracked in the dashboard."""
    DOCUMENT_UPLOADS = "document_uploads"
    USER_ACTIVITY = "user_activity"
    AI_USAGE = "ai_usage"
    DATABASE_PERFORMANCE = "database_performance"
    SYSTEM_HEALTH = "system_health"
    SEARCH_QUERIES = "search_queries"


@dataclass
class DashboardMetric:
    """Represents a single dashboard metric."""
    name: str
    value: Any
    unit: str
    trend: Optional[float] = None
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class AnalyticsDashboard:
    """
    Real-Time Analytics Dashboard for AI Law Firm.

    Provides comprehensive insights into:
    - Document upload trends and patterns
    - User activity and engagement
    - AI model usage and performance
    - Database performance metrics
    - System health and reliability
    """

    def __init__(self, db_config: Dict[str, Any]):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.db_config = db_config

        # Database connections
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None

        # Cache for metrics
        self.metrics_cache = {}
        self.cache_ttl = 300  # 5 minutes

    async def initialize(self):
        """Initialize database connections."""
        try:
            # PostgreSQL connection
            self.pg_pool = await asyncpg.create_pool(
                host=self.db_config.get('postgres_host', 'localhost'),
                port=self.db_config.get('postgres_port', 54320),
                database=self.db_config.get('postgres_db', 'ai_law_firm'),
                user=self.db_config.get('postgres_user', 'ai_law_user'),
                password=self.db_config.get('postgres_password', 'ai_law_password_2024'),
                min_size=1,
                max_size=10
            )

            # Redis connection
            self.redis_client = redis.Redis(
                host=self.db_config.get('redis_host', 'localhost'),
                port=self.db_config.get('redis_port', 63790),
                password=self.db_config.get('redis_password', 'ai_law_redis_password_2024'),
                decode_responses=True
            )

            # MongoDB connection
            mongo_uri = f"mongodb://{self.db_config.get('mongo_host', 'localhost')}:{self.db_config.get('mongo_port', 27019)}"
            self.mongo_client = AsyncIOMotorClient(mongo_uri)
            self.mongo_db = self.mongo_client.ai_law_firm

            self.logger.info("Analytics Dashboard initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize dashboard: {str(e)}")
            return False

    async def get_dashboard_overview(self) -> Dict[str, Any]:
        """
        Get comprehensive dashboard overview with key metrics.

        Returns:
            Dict containing all dashboard metrics and insights
        """
        try:
            # Get all metrics concurrently
            tasks = [
                self.get_document_metrics(),
                self.get_user_activity_metrics(),
                self.get_ai_usage_metrics(),
                self.get_database_performance_metrics(),
                self.get_system_health_metrics(),
                self.get_search_analytics()
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            metrics = {}
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"Error getting metric {i}: {str(result)}")
                    continue

                if i == 0:
                    metrics['documents'] = result
                elif i == 1:
                    metrics['users'] = result
                elif i == 2:
                    metrics['ai_usage'] = result
                elif i == 3:
                    metrics['database'] = result
                elif i == 4:
                    metrics['system'] = result
                elif i == 5:
                    metrics['search'] = result

            # Add summary statistics
            metrics['summary'] = self._calculate_summary_stats(metrics)
            metrics['generated_at'] = datetime.utcnow().isoformat()

            return metrics

        except Exception as e:
            self.logger.error(f"Error generating dashboard overview: {str(e)}")
            return {"error": str(e)}

    async def get_document_metrics(self) -> Dict[str, Any]:
        """Get document-related metrics."""
        try:
            async with self.pg_pool.acquire() as conn:
                # Total documents
                total_docs = await conn.fetchval("SELECT COUNT(*) FROM documents")

                # Documents by type
                doc_types = await conn.fetch("""
                    SELECT
                        metadata->>'document_type' as type,
                        COUNT(*) as count
                    FROM documents
                    WHERE metadata->>'document_type' IS NOT NULL
                    GROUP BY metadata->>'document_type'
                    ORDER BY count DESC
                    LIMIT 10
                """)

                # Recent uploads (last 7 days)
                week_ago = datetime.utcnow() - timedelta(days=7)
                recent_uploads = await conn.fetchval("""
                    SELECT COUNT(*)
                    FROM documents
                    WHERE upload_timestamp >= $1
                """, week_ago)

                # Average document size
                avg_size = await conn.fetchval("""
                    SELECT AVG(file_size)
                    FROM documents
                    WHERE file_size IS NOT NULL
                """)

                return {
                    "total_documents": total_docs or 0,
                    "recent_uploads_7d": recent_uploads or 0,
                    "average_file_size_kb": round((avg_size or 0) / 1024, 2),
                    "document_types": [{"type": row['type'], "count": row['count']} for row in doc_types],
                    "upload_trend": await self._get_upload_trend()
                }

        except Exception as e:
            self.logger.error(f"Error getting document metrics: {str(e)}")
            return {"error": str(e)}

    async def get_user_activity_metrics(self) -> Dict[str, Any]:
        """Get user activity and engagement metrics."""
        try:
            # Get user activity from Redis (if we have user sessions)
            active_users = 0
            try:
                # Count active user sessions in Redis
                session_keys = self.redis_client.keys("session:*")
                active_users = len(session_keys) if session_keys else 0
            except:
                active_users = 0

            # Get document activity from PostgreSQL
            async with self.pg_pool.acquire() as conn:
                # Users by activity level (last 30 days)
                month_ago = datetime.utcnow() - timedelta(days=30)

                user_activity = await conn.fetch("""
                    SELECT
                        COUNT(DISTINCT d.document_id) as documents_uploaded,
                        COUNT(*) as total_uploads
                    FROM documents d
                    WHERE d.upload_timestamp >= $1
                """, month_ago)

                # Peak usage hours
                peak_hours = await conn.fetch("""
                    SELECT
                        EXTRACT(hour from upload_timestamp) as hour,
                        COUNT(*) as uploads
                    FROM documents
                    WHERE upload_timestamp >= $1
                    GROUP BY EXTRACT(hour from upload_timestamp)
                    ORDER BY uploads DESC
                    LIMIT 5
                """, month_ago)

            return {
                "active_users": active_users,
                "total_uploads_30d": user_activity[0]['total_uploads'] if user_activity else 0,
                "unique_documents_30d": user_activity[0]['documents_uploaded'] if user_activity else 0,
                "peak_usage_hours": [{"hour": int(row['hour']), "uploads": row['uploads']} for row in peak_hours],
                "user_engagement_score": self._calculate_engagement_score(user_activity[0] if user_activity else {})
            }

        except Exception as e:
            self.logger.error(f"Error getting user activity metrics: {str(e)}")
            return {"error": str(e)}

    async def get_ai_usage_metrics(self) -> Dict[str, Any]:
        """Get AI usage and performance metrics."""
        try:
            # Get AI usage stats from Redis
            ai_requests = 0
            ai_tokens = 0
            avg_response_time = 0.0

            try:
                ai_requests = int(self.redis_client.get("ai:requests:total") or 0)
                ai_tokens = int(self.redis_client.get("ai:tokens:total") or 0)
                response_times = self.redis_client.lrange("ai:response_times", 0, 99)  # Last 100
                if response_times:
                    avg_response_time = sum(float(rt) for rt in response_times) / len(response_times)
            except:
                pass

            # Get model usage breakdown
            model_usage = {}
            try:
                model_keys = self.redis_client.keys("ai:model:*")
                for key in model_keys:
                    model_name = key.split(":")[-1]
                    usage = int(self.redis_client.get(key) or 0)
                    model_usage[model_name] = usage
            except:
                pass

            return {
                "total_ai_requests": ai_requests,
                "total_tokens_used": ai_tokens,
                "average_response_time_ms": round(avg_response_time * 1000, 2),
                "model_usage_breakdown": model_usage,
                "cost_estimate_usd": self._estimate_ai_cost(ai_tokens),
                "requests_per_minute": await self._get_requests_per_minute()
            }

        except Exception as e:
            self.logger.error(f"Error getting AI usage metrics: {str(e)}")
            return {"error": str(e)}

    async def get_database_performance_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        try:
            perf_metrics = {}

            # PostgreSQL performance
            async with self.pg_pool.acquire() as conn:
                # Connection count
                conn_count = await conn.fetchval("""
                    SELECT count(*) FROM pg_stat_activity
                    WHERE datname = current_database()
                """)

                # Slow queries (if we had query logging)
                perf_metrics["postgres_connections"] = conn_count or 0

            # Redis performance
            try:
                redis_info = self.redis_client.info()
                perf_metrics.update({
                    "redis_memory_used_mb": round(redis_info.get("used_memory", 0) / 1024 / 1024, 2),
                    "redis_connected_clients": redis_info.get("connected_clients", 0),
                    "redis_operations_per_sec": redis_info.get("instantaneous_ops_per_sec", 0)
                })
            except:
                perf_metrics.update({
                    "redis_memory_used_mb": 0,
                    "redis_connected_clients": 0,
                    "redis_operations_per_sec": 0
                })

            # MongoDB performance (basic)
            try:
                mongo_stats = await self.mongo_db.command("dbStats")
                perf_metrics.update({
                    "mongodb_size_mb": round(mongo_stats.get("dataSize", 0) / 1024 / 1024, 2),
                    "mongodb_collections": mongo_stats.get("collections", 0),
                    "mongodb_indexes": mongo_stats.get("indexes", 0)
                })
            except:
                perf_metrics.update({
                    "mongodb_size_mb": 0,
                    "mongodb_collections": 0,
                    "mongodb_indexes": 0
                })

            return perf_metrics

        except Exception as e:
            self.logger.error(f"Error getting database performance metrics: {str(e)}")
            return {"error": str(e)}

    async def get_system_health_metrics(self) -> Dict[str, Any]:
        """Get system health and reliability metrics."""
        try:
            health_metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "services": {}
            }

            # Check PostgreSQL health
            try:
                async with self.pg_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
                    health_metrics["services"]["postgresql"] = "healthy"
            except:
                health_metrics["services"]["postgresql"] = "unhealthy"

            # Check Redis health
            try:
                self.redis_client.ping()
                health_metrics["services"]["redis"] = "healthy"
            except:
                health_metrics["services"]["redis"] = "unhealthy"

            # Check MongoDB health
            try:
                await self.mongo_client.admin.command('ping')
                health_metrics["services"]["mongodb"] = "healthy"
            except:
                health_metrics["services"]["mongodb"] = "unhealthy"

            # Calculate overall health score
            healthy_services = sum(1 for status in health_metrics["services"].values() if status == "healthy")
            total_services = len(health_metrics["services"])
            health_metrics["overall_health_score"] = (healthy_services / total_services) * 100 if total_services > 0 else 0

            # Get uptime information
            health_metrics["uptime_seconds"] = await self._get_system_uptime()

            return health_metrics

        except Exception as e:
            self.logger.error(f"Error getting system health metrics: {str(e)}")
            return {"error": str(e)}

    async def get_search_analytics(self) -> Dict[str, Any]:
        """Get search and query analytics."""
        try:
            search_metrics = {}

            # Get search query count from Redis
            try:
                search_count = int(self.redis_client.get("search:queries:total") or 0)
                search_metrics["total_search_queries"] = search_count
            except:
                search_metrics["total_search_queries"] = 0

            # Get popular search terms
            try:
                popular_terms = self.redis_client.zrevrange("search:terms", 0, 9, withscores=True)
                search_metrics["popular_search_terms"] = [
                    {"term": term, "count": int(score)} for term, score in popular_terms
                ]
            except:
                search_metrics["popular_search_terms"] = []

            # Get search performance
            try:
                search_times = self.redis_client.lrange("search:response_times", 0, 99)
                if search_times:
                    avg_search_time = sum(float(t) for t in search_times) / len(search_times)
                    search_metrics["average_search_time_ms"] = round(avg_search_time * 1000, 2)
                else:
                    search_metrics["average_search_time_ms"] = 0
            except:
                search_metrics["average_search_time_ms"] = 0

            return search_metrics

        except Exception as e:
            self.logger.error(f"Error getting search analytics: {str(e)}")
            return {"error": str(e)}

    # Helper methods

    async def _get_upload_trend(self) -> List[Dict[str, Any]]:
        """Get document upload trend for the last 7 days."""
        try:
            async with self.pg_pool.acquire() as conn:
                trend_data = await conn.fetch("""
                    SELECT
                        DATE(upload_timestamp) as date,
                        COUNT(*) as uploads
                    FROM documents
                    WHERE upload_timestamp >= CURRENT_DATE - INTERVAL '7 days'
                    GROUP BY DATE(upload_timestamp)
                    ORDER BY date
                """)

                return [{"date": str(row['date']), "uploads": row['uploads']} for row in trend_data]

        except Exception:
            return []

    async def _get_requests_per_minute(self) -> float:
        """Calculate AI requests per minute."""
        try:
            # Get requests in the last hour
            hour_ago = datetime.utcnow() - timedelta(hours=1)
            async with self.pg_pool.acquire() as conn:
                recent_requests = await conn.fetchval("""
                    SELECT COUNT(*)
                    FROM documents
                    WHERE upload_timestamp >= $1
                """, hour_ago)

                return round(recent_requests / 60, 2) if recent_requests else 0

        except Exception:
            return 0

    def _calculate_engagement_score(self, activity_data: Dict[str, Any]) -> float:
        """Calculate user engagement score (0-100)."""
        if not activity_data:
            return 0

        uploads = activity_data.get('total_uploads', 0)
        unique_docs = activity_data.get('documents_uploaded', 0)

        # Simple engagement calculation
        base_score = min(uploads * 2, 50)  # Max 50 points for uploads
        diversity_bonus = min(unique_docs * 5, 30)  # Max 30 points for diversity
        consistency_bonus = min(uploads / max(unique_docs, 1) * 10, 20)  # Max 20 points for consistency

        return round(base_score + diversity_bonus + consistency_bonus, 1)

    def _estimate_ai_cost(self, tokens: int) -> float:
        """Estimate AI usage cost in USD."""
        # Rough estimates (adjust based on your actual pricing)
        gpt4_tokens_per_dollar = 1000  # Approximate
        return round(tokens / gpt4_tokens_per_dollar, 2) if tokens else 0

    async def _get_system_uptime(self) -> int:
        """Get system uptime in seconds."""
        try:
            import time
            return int(time.time() - time.time())  # Simplified - would need actual uptime tracking
        except:
            return 0

    def _calculate_summary_stats(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate summary statistics across all metrics."""
        summary = {
            "total_documents": metrics.get('documents', {}).get('total_documents', 0),
            "active_users": metrics.get('users', {}).get('active_users', 0),
            "ai_requests_today": metrics.get('ai_usage', {}).get('total_ai_requests', 0),
            "system_health_score": metrics.get('system', {}).get('overall_health_score', 0),
            "database_performance_score": self._calculate_db_performance_score(metrics.get('database', {}))
        }

        # Calculate overall system score
        scores = [
            summary['system_health_score'],
            summary['database_performance_score'],
            100 if summary['active_users'] > 0 else 0,  # User activity indicator
            min(summary['total_documents'] / 10, 100)  # Document volume indicator
        ]

        summary['overall_system_score'] = round(sum(scores) / len(scores), 1)

        return summary

    def _calculate_db_performance_score(self, db_metrics: Dict[str, Any]) -> float:
        """Calculate database performance score (0-100)."""
        if not db_metrics:
            return 0

        score = 0

        # PostgreSQL connections (max 30 points)
        pg_conns = db_metrics.get('postgres_connections', 0)
        if pg_conns < 10:
            score += 30
        elif pg_conns < 50:
            score += 20
        else:
            score += 10

        # Redis memory usage (max 35 points)
        redis_mem = db_metrics.get('redis_memory_used_mb', 0)
        if redis_mem < 100:
            score += 35
        elif redis_mem < 500:
            score += 25
        else:
            score += 15

        # Redis operations (max 35 points)
        redis_ops = db_metrics.get('redis_operations_per_sec', 0)
        if redis_ops > 1000:
            score += 35
        elif redis_ops > 100:
            score += 25
        else:
            score += 15

        return score

    async def close(self):
        """Close all database connections."""
        if self.pg_pool:
            await self.pg_pool.close()
        if self.redis_client:
            self.redis_client.close()
        if self.mongo_client:
            self.mongo_client.close()

        self.logger.info("Analytics Dashboard connections closed")


# Convenience functions for quick access

async def get_dashboard_overview(db_config: Dict[str, Any]) -> Dict[str, Any]:
    """Get dashboard overview with default configuration."""
    dashboard = AnalyticsDashboard(db_config)
    if await dashboard.initialize():
        result = await dashboard.get_dashboard_overview()
        await dashboard.close()
        return result
    else:
        return {"error": "Failed to initialize dashboard"}


async def get_document_metrics(db_config: Dict[str, Any]) -> Dict[str, Any]:
    """Get document metrics only."""
    dashboard = AnalyticsDashboard(db_config)
    if await dashboard.initialize():
        result = await dashboard.get_document_metrics()
        await dashboard.close()
        return result
    else:
        return {"error": "Failed to initialize dashboard"}


# Export key classes and functions
__all__ = [
    "AnalyticsDashboard",
    "get_dashboard_overview",
    "get_document_metrics",
    "MetricType",
    "DashboardMetric"
]