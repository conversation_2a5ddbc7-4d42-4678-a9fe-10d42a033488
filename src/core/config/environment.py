"""
Environment detection and management utilities.

This module provides utilities for detecting and managing different
deployment environments and their specific requirements.
"""
import os
import platform
import socket
from typing import Dict, Any, Optional
from pathlib import Path

from .settings import Environment, AppConfig


class EnvironmentDetector:
    """Detects and provides information about the current environment."""

    def __init__(self):
        self._cache: Dict[str, Any] = {}

    def detect_environment(self) -> Environment:
        """
        Detect the current environment based on various indicators.

        Returns:
            Environment: Detected environment
        """
        # Check environment variable first
        env_var = os.getenv("APP_ENV", "").lower()
        if env_var in [e.value for e in Environment]:
            return Environment(env_var)

        # Check for common environment indicators
        if self._is_production_environment():
            return Environment.PRODUCTION
        elif self._is_local_environment():
            return Environment.LOCAL
        elif self._is_testing_environment():
            return Environment.TESTING
        else:
            return Environment.DEVELOPMENT

    def _is_production_environment(self) -> bool:
        """Check if running in production environment."""
        indicators = [
            os.getenv("PRODUCTION", "").lower() in ("true", "1", "yes"),
            os.getenv("ENV") == "production",
            "production" in os.getenv("NODE_ENV", "").lower(),
            self._is_cloud_platform(),
        ]
        return any(indicators)

    def _is_local_environment(self) -> bool:
        """Check if running in local development environment."""
        indicators = [
            os.getenv("LOCAL", "").lower() in ("true", "1", "yes"),
            socket.gethostname() in ["localhost", "127.0.0.1"],
            platform.node() in ["localhost", "127.0.0.1"],
            self._is_development_machine(),
        ]
        return any(indicators)

    def _is_testing_environment(self) -> bool:
        """Check if running in testing environment."""
        indicators = [
            os.getenv("TESTING", "").lower() in ("true", "1", "yes"),
            os.getenv("PYTEST_CURRENT_TEST") is not None,
            "pytest" in os.getenv("_", ""),
            "test" in os.getenv("PYTEST_DISABLE_PLUGIN_AUTOLOAD", ""),
        ]
        return any(indicators)

    def _is_cloud_platform(self) -> bool:
        """Check if running on a cloud platform."""
        cloud_indicators = [
            # AWS
            os.getenv("AWS_LAMBDA_FUNCTION_NAME") is not None,
            os.getenv("ECS_CONTAINER_METADATA_URI") is not None,
            "/aws/lambda/" in os.getenv("LAMBDA_TASK_ROOT", ""),

            # Google Cloud
            os.getenv("GAE_APPLICATION") is not None,
            os.getenv("GOOGLE_CLOUD_PROJECT") is not None,

            # Azure
            os.getenv("WEBSITE_INSTANCE_ID") is not None,

            # Docker
            Path("/.dockerenv").exists(),

            # Kubernetes
            Path("/var/run/secrets/kubernetes.io").exists(),
        ]
        return any(cloud_indicators)

    def _is_development_machine(self) -> bool:
        """Check if running on a development machine."""
        dev_indicators = [
            # Common development hostnames
            any(host in socket.gethostname().lower() for host in [
                "macbook", "laptop", "desktop", "pc", "dev"
            ]),

            # Development user names
            os.getenv("USER", "").lower() in ["dev", "developer", "user"],

            # Development directories
            any(Path(path).exists() for path in [
                "/Users",  # macOS
                "/home",   # Linux
                "C:\\Users",  # Windows
            ]),
        ]
        return any(dev_indicators)

    def get_environment_info(self) -> Dict[str, Any]:
        """
        Get comprehensive information about the current environment.

        Returns:
            Dict containing environment details
        """
        if "env_info" not in self._cache:
            self._cache["env_info"] = {
                "environment": self.detect_environment().value,
                "hostname": socket.gethostname(),
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "user": os.getenv("USER", "unknown"),
                "working_directory": os.getcwd(),
                "is_cloud": self._is_cloud_platform(),
                "is_docker": Path("/.dockerenv").exists(),
                "is_kubernetes": Path("/var/run/secrets/kubernetes.io").exists(),
                "environment_variables": self._get_relevant_env_vars(),
            }

        return self._cache["env_info"]

    def _get_relevant_env_vars(self) -> Dict[str, str]:
        """Get relevant environment variables (without sensitive data)."""
        relevant_vars = [
            "APP_ENV", "DEBUG", "LOG_LEVEL", "PYTHONPATH",
            "STREAMLIT_SERVER_PORT", "STREAMLIT_SERVER_ADDRESS"
        ]

        env_vars = {}
        for var in relevant_vars:
            value = os.getenv(var)
            if value is not None:
                env_vars[var] = value

        return env_vars

    def get_system_requirements(self, environment: Optional[Environment] = None) -> Dict[str, Any]:
        """
        Get system requirements for the specified environment.

        Args:
            environment: Environment to get requirements for (current if None)

        Returns:
            Dict containing system requirements
        """
        if environment is None:
            environment = self.detect_environment()

        base_requirements = {
            "python_version": ">=3.8",
            "memory_gb": 2,
            "disk_gb": 5,
        }

        env_specific = {
            Environment.DEVELOPMENT: {
                "memory_gb": 4,
                "disk_gb": 10,
                "additional_tools": ["git", "docker", "make"],
            },
            Environment.PRODUCTION: {
                "memory_gb": 8,
                "disk_gb": 50,
                "cpu_cores": 4,
                "network_bandwidth": "high",
            },
            Environment.LOCAL: {
                "memory_gb": 4,
                "disk_gb": 20,
                "additional_tools": ["ollama", "docker"],
            },
            Environment.TESTING: {
                "memory_gb": 2,
                "disk_gb": 5,
                "additional_tools": ["pytest", "coverage"],
            },
        }

        requirements = base_requirements.copy()
        if environment in env_specific:
            requirements.update(env_specific[environment])

        return requirements

    def validate_environment(self, config: AppConfig) -> Dict[str, Any]:
        """
        Validate that the current environment meets requirements.

        Args:
            config: Application configuration

        Returns:
            Dict with validation results
        """
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "recommendations": []
        }

        env_info = self.get_environment_info()
        requirements = self.get_system_requirements(config.environment)

        # Check Python version
        import sys
        current_version = tuple(map(int, platform.python_version().split('.')[:2]))
        required_version = tuple(map(int, requirements["python_version"].replace(">=", "").split(".")[:2]))

        if current_version < required_version:
            validation_results["errors"].append(
                f"Python {requirements['python_version']} required, found {platform.python_version()}"
            )
            validation_results["valid"] = False

        # Check memory (rough estimate)
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < requirements["memory_gb"]:
                validation_results["warnings"].append(
                    f"Recommended {requirements['memory_gb']}GB RAM, found {memory_gb:.1f}GB"
                )
        except ImportError:
            validation_results["warnings"].append("psutil not available for memory check")

        # Check disk space
        try:
            stat = os.statvfs(os.getcwd())
            disk_gb = (stat.f_bavail * stat.f_frsize) / (1024**3)
            if disk_gb < requirements["disk_gb"]:
                validation_results["warnings"].append(
                    f"Recommended {requirements['disk_gb']}GB free disk space, found {disk_gb:.1f}GB"
                )
        except (OSError, AttributeError):
            validation_results["warnings"].append("Cannot check disk space")

        # Environment-specific validations
        if config.environment == Environment.PRODUCTION:
            if config.debug:
                validation_results["errors"].append("Debug mode should be disabled in production")
                validation_results["valid"] = False

            if not config.security.secret_key or len(config.security.secret_key) < 32:
                validation_results["errors"].append("Strong secret key required in production")
                validation_results["valid"] = False

        elif config.environment == Environment.LOCAL:
            # Check for local tools
            missing_tools = []
            for tool in requirements.get("additional_tools", []):
                if not self._is_tool_available(tool):
                    missing_tools.append(tool)

            if missing_tools:
                validation_results["warnings"].extend([
                    f"Tool '{tool}' not found, may be required for local development"
                    for tool in missing_tools
                ])

        return validation_results

    def _is_tool_available(self, tool: str) -> bool:
        """Check if a tool is available on the system."""
        import shutil
        return shutil.which(tool) is not None

    def get_environment_variables_template(self, environment: Environment) -> Dict[str, str]:
        """
        Get template of required environment variables for an environment.

        Args:
            environment: Target environment

        Returns:
            Dict with variable names and descriptions
        """
        base_vars = {
            "APP_ENV": f"Application environment ({environment.value})",
            "DEBUG": "Enable debug mode (true/false)",
            "LOG_LEVEL": "Logging level (DEBUG/INFO/WARNING/ERROR)",
        }

        env_specific_vars = {
            Environment.DEVELOPMENT: {
                "OPENAI_API_KEY": "OpenAI API key for development",
                "QDRANT_URL": "Qdrant instance URL",
                "QDRANT_API_KEY": "Qdrant API key",
            },
            Environment.PRODUCTION: {
                "OPENAI_API_KEY": "OpenAI API key for production",
                "QDRANT_URL": "Production Qdrant instance URL",
                "QDRANT_API_KEY": "Production Qdrant API key",
                "SECRET_KEY": "Application secret key (32+ characters)",
            },
            Environment.LOCAL: {
                "OLLAMA_BASE_URL": "Ollama server URL (default: http://localhost:11434)",
                "OLLAMA_MODEL": "Ollama model name (default: llama3.2:3b)",
            },
            Environment.TESTING: {
                "TEST_DATABASE_URL": "Test database URL",
                "MOCK_EXTERNAL_APIS": "Mock external API calls (true/false)",
            },
        }

        template = base_vars.copy()
        if environment in env_specific_vars:
            template.update(env_specific_vars[environment])

        return template


# Global environment detector instance
_detector = EnvironmentDetector()


def get_environment_detector() -> EnvironmentDetector:
    """Get the global environment detector instance."""
    return _detector


def detect_environment() -> Environment:
    """Detect the current environment."""
    return _detector.detect_environment()


def get_environment_info() -> Dict[str, Any]:
    """Get comprehensive environment information."""
    return _detector.get_environment_info()


def validate_environment(config: AppConfig) -> Dict[str, Any]:
    """Validate environment against configuration."""
    return _detector.validate_environment(config)


def create_env_file_template(environment: Environment, output_path: str = ".env.template") -> None:
    """
    Create a template .env file for the specified environment.

    Args:
        environment: Target environment
        output_path: Path to write the template file
    """
    template_vars = _detector.get_environment_variables_template(environment)

    with open(output_path, "w") as f:
        f.write(f"# Environment variables template for {environment.value} environment\n")
        f.write("# Copy this file to .env and fill in the actual values\n\n")

        for var_name, description in template_vars.items():
            f.write(f"# {description}\n")
            f.write(f"{var_name}=\n\n")

    print(f"Environment template created: {output_path}")


if __name__ == "__main__":
    # Command-line usage
    import argparse

    parser = argparse.ArgumentParser(description="Environment detection utilities")
    parser.add_argument("--info", action="store_true", help="Show environment information")
    parser.add_argument("--validate", action="store_true", help="Validate current environment")
    parser.add_argument("--create-template", choices=[e.value for e in Environment],
                       help="Create .env template for specified environment")

    args = parser.parse_args()

    if args.info:
        import json
        print(json.dumps(get_environment_info(), indent=2))

    elif args.validate:
        from .settings import load_config
        config = load_config()
        results = validate_environment(config)

        print("Environment Validation Results:")
        print(f"Valid: {results['valid']}")

        if results['errors']:
            print("\nErrors:")
            for error in results['errors']:
                print(f"  ❌ {error}")

        if results['warnings']:
            print("\nWarnings:")
            for warning in results['warnings']:
                print(f"  ⚠️  {warning}")

        if results['recommendations']:
            print("\nRecommendations:")
            for rec in results['recommendations']:
                print(f"  💡 {rec}")

    elif args.create_template:
        env = Environment(args.create_template)
        create_env_file_template(env)

    else:
        print(f"Current environment: {detect_environment().value}")