"""
Application settings and configuration management for AI Law Firm.

This module provides a centralized configuration system that supports:
- Environment-based configuration (development, production, local)
- Type-safe configuration with validation
- Dynamic configuration loading
- Secure credential management
"""
import os
import secrets
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from enum import Enum


class Environment(str, Enum):
    """Application environment enumeration."""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    LOCAL = "local"
    TESTING = "testing"


class AIProvider(str, Enum):
    """Supported AI providers."""
    OPENAI = "openai"
    OLLAMA = "ollama"
    ANTHROPIC = "anthropic"
    HUGGINGFACE = "huggingface"
    OPENROUTER = "openrouter"
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"


class VectorDatabase(str, Enum):
    """Supported vector databases."""
    QDRANT_CLOUD = "qdrant_cloud"
    QDRANT_LOCAL = "qdrant_local"


@dataclass
class ModelRoleConfig:
    """Configuration for different model roles in the analysis pipeline."""
    primary_analysis: str = "gpt-4o-mini"      # Fast, cost-effective initial analysis
    secondary_review: str = "gpt-4o-mini"      # Validation and cross-checking
    final_polish: str = "gpt-4o"               # High-quality final output
    specialized_tasks: Dict[str, str] = None   # Task-specific model overrides

    def __post_init__(self):
        """Initialize specialized tasks if not provided."""
        if self.specialized_tasks is None:
            self.specialized_tasks = {
                "contract_analysis": self.primary_analysis,
                "legal_research": self.secondary_review,
                "risk_assessment": self.final_polish,
                "compliance_check": self.secondary_review,
                "strategic_planning": self.final_polish
            }


@dataclass
class AgentPoolConfig:
    """Configuration for the legal agent pool."""
    min_agents: int = 3          # Minimum number of active agents
    max_agents: int = 45         # Maximum number of agents in pool
    concurrent_limit: int = 10   # Max concurrent agent operations
    agent_timeout: int = 300     # Agent operation timeout (seconds)
    load_balancing: str = "round_robin"  # round_robin, least_loaded, priority

    def __post_init__(self):
        """Validate agent pool configuration."""
        if self.min_agents < 1:
            raise ValueError("Minimum agents must be at least 1")
        if self.max_agents < self.min_agents:
            raise ValueError("Maximum agents must be >= minimum agents")
        if self.concurrent_limit < 1:
            raise ValueError("Concurrent limit must be at least 1")


@dataclass
class OpenAIConfig:
    """Enhanced OpenAI provider configuration with multi-model support."""
    api_key: str
    model_roles: ModelRoleConfig = None
    agent_pool: AgentPoolConfig = None
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30
    enable_streaming: bool = True
    retry_attempts: int = 3
    rate_limit_buffer: float = 0.8  # Use 80% of rate limit

    def __post_init__(self):
        """Initialize configuration with defaults."""
        # Initialize model roles if not provided
        if self.model_roles is None:
            self.model_roles = ModelRoleConfig()

        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")
        if self.max_tokens < 1:
            raise ValueError("Max tokens must be positive")

    def get_model_for_task(self, task_type: str) -> str:
        """Get the appropriate model for a specific task type."""
        return self.model_roles.specialized_tasks.get(task_type, self.model_roles.primary_analysis)

    def get_cost_estimate_for_task(self, task_type: str, prompt_tokens: int, completion_tokens: int) -> float:
        """Get cost estimate for a specific task type."""
        model = self.get_model_for_task(task_type)
        return self._estimate_cost_for_model(model, prompt_tokens, completion_tokens)

    def _estimate_cost_for_model(self, model: str, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate cost for a specific model."""
        # Cost per 1K tokens (as of 2024)
        cost_per_1k = {
            "gpt-5-nano": {"input": 0.00005, "output": 0.0002},  # Ultra-efficient nano model
            "gpt-5-mini": {"input": 0.0001, "output": 0.0004},  # Next-gen ultra-efficient
            "gpt-5": {"input": 0.002, "output": 0.008},  # Next-gen flagship model
            "gpt-4o": {"input": 0.005, "output": 0.015},
            "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
        }

        model_costs = cost_per_1k.get(model, {"input": 0.01, "output": 0.03})

        return (
            (prompt_tokens / 1000) * model_costs["input"] +
            (completion_tokens / 1000) * model_costs["output"]
        )

    def __post_init__(self):
        """Initialize configuration with defaults and validate."""
        # Initialize model roles if not provided
        if self.model_roles is None:
            self.model_roles = ModelRoleConfig()

        # Validate configuration
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")
        if self.max_tokens < 1:
            raise ValueError("Max tokens must be positive")

    @property
    def model(self) -> str:
        """Get the default model for general use."""
        return self.model_roles.primary_analysis


@dataclass
class OllamaConfig:
    """Ollama provider configuration."""
    base_url: str = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    model: str = os.getenv("OLLAMA_MODEL", "llama3.2:3b")
    temperature: float = 0.1
    timeout: int = 60

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.base_url:
            raise ValueError("Ollama base URL is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")


@dataclass
class AnthropicConfig:
    """Anthropic provider configuration."""
    api_key: str
    model: str = "claude-3-haiku-20240307"  # Cost-effective default
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.api_key:
            raise ValueError("Anthropic API key is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")
        if self.max_tokens < 1:
            raise ValueError("Max tokens must be positive")


@dataclass
class HuggingFaceConfig:
    """HuggingFace provider configuration."""
    api_key: str = ""  # Optional for local models
    model: str = "microsoft/DialoGPT-medium"  # Local-friendly default
    temperature: float = 0.1
    max_tokens: int = 1000
    timeout: int = 30
    use_local: bool = True  # Prefer local inference

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.model:
            raise ValueError("HuggingFace model is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")


@dataclass
class OpenRouterConfig:
    """OpenRouter provider configuration."""
    api_key: str
    model: str = "anthropic/claude-3-haiku"  # Cost-effective default
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.api_key:
            raise ValueError("OpenRouter API key is required")
        if not self.model:
            raise ValueError("Model is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")


@dataclass
class DeepSeekConfig:
    """DeepSeek provider configuration."""
    api_key: str
    model: str = "deepseek-chat"  # Cost-effective model
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")
        if not self.model:
            raise ValueError("Model is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")


@dataclass
class GeminiConfig:
    """Google Gemini provider configuration."""
    api_key: str
    model: str = "gemini-pro"  # Cost-effective default
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        if not self.model:
            raise ValueError("Model is required")
        if not (0 <= self.temperature <= 2):
            raise ValueError("Temperature must be between 0 and 2")


@dataclass
class QdrantCloudConfig:
    """Qdrant Cloud configuration."""
    url: str
    api_key: str
    collection: str = "legal_documents"
    timeout: int = 30

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.url:
            raise ValueError("Qdrant URL is required")
        if not self.api_key:
            raise ValueError("Qdrant API key is required")


@dataclass
class QdrantLocalConfig:
    """Local Qdrant configuration."""
    url: str = os.getenv("QDRANT_URL", "http://localhost:63331")
    collection: str = os.getenv("QDRANT_COLLECTION", "legal_documents")
    timeout: int = 30

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.url:
            raise ValueError("Qdrant URL is required")


@dataclass
class DocumentConfig:
    """Document processing configuration."""
    max_size_mb: int = 50
    allowed_extensions: List[str] = field(default_factory=lambda: [
        "pdf", "docx", "txt", "html", "htm", "md", "markdown",
        "csv", "xls", "xlsx", "eml", "zip"
    ])
    chunk_size: int = 1000
    chunk_overlap: int = 200
    supported_languages: List[str] = field(default_factory=lambda: ["en"])

    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.max_size_mb < 1:
            raise ValueError("Max size must be at least 1MB")
        if self.chunk_size < 100:
            raise ValueError("Chunk size must be at least 100 characters")
        if self.chunk_overlap >= self.chunk_size:
            raise ValueError("Chunk overlap must be less than chunk size")


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

    def __post_init__(self):
        """Validate configuration after initialization."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.level.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")


@dataclass
class SecurityConfig:
    """Security configuration."""
    secret_key: str = field(default_factory=lambda: secrets.token_hex(32))
    session_timeout: int = 3600  # 1 hour
    max_login_attempts: int = 5
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # 1 minute

    def __post_init__(self):
        """Validate configuration after initialization."""
        if len(self.secret_key) < 32:
            raise ValueError("Secret key must be at least 32 characters")


@dataclass
class AppConfig:
    """Main application configuration."""
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8501
    title: str = "AI Law Firm - Multi-Agent Legal Analysis"
    version: str = "1.0.0"

    # Database configurations
    postgres_host: str = "localhost"
    postgres_port: int = 54320
    postgres_db: str = "ai_law_firm"
    postgres_user: str = "ai_law_user"
    postgres_password: str = "ai_law_password_2024"

    mongodb_host: str = "localhost"
    mongodb_port: int = 27019
    mongodb_db: str = "ai_law_firm"
    mongodb_user: str = "ai_law_admin"
    mongodb_password: str = "ai_law_mongo_password_2024"

    redis_host: str = "localhost"
    redis_port: int = 63790
    redis_password: str = "ai_law_redis_password_2024"

    # Component configurations
    ai_provider: AIProvider = AIProvider.OPENAI
    vector_database: VectorDatabase = VectorDatabase.QDRANT_CLOUD

    # Nested configurations
    openai: Optional[OpenAIConfig] = None
    ollama: Optional[OllamaConfig] = None
    anthropic: Optional[AnthropicConfig] = None
    huggingface: Optional[HuggingFaceConfig] = None
    openrouter: Optional[OpenRouterConfig] = None
    deepseek: Optional[DeepSeekConfig] = None
    gemini: Optional[GeminiConfig] = None
    qdrant_cloud: Optional[QdrantCloudConfig] = None
    qdrant_local: Optional[QdrantLocalConfig] = None
    document: DocumentConfig = field(default_factory=DocumentConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)

    # Multi-model pipeline configuration
    model_roles: ModelRoleConfig = field(default_factory=ModelRoleConfig)
    agent_pool: AgentPoolConfig = field(default_factory=AgentPoolConfig)

    def __post_init__(self):
        """Validate and initialize configuration after creation."""
        self._validate_configuration()
        self._initialize_components()

    def _validate_configuration(self):
        """Validate the overall configuration."""
        if self.environment == Environment.PRODUCTION and self.debug:
            raise ValueError("Debug mode should not be enabled in production")

        # Skip validation for testing environment or if explicitly disabled
        if os.getenv("SKIP_CONFIG_VALIDATION", "").lower() in ("true", "1", "yes"):
            return

        # Only validate AI provider config if we're not in testing mode
        if self.environment != Environment.TESTING:
            # Validate AI provider configurations
            if self.ai_provider == AIProvider.OPENAI and not self.openai:
                raise ValueError("OpenAI configuration required when using OpenAI provider")
            elif self.ai_provider == AIProvider.OLLAMA and not self.ollama:
                raise ValueError("Ollama configuration required when using Ollama provider")
            elif self.ai_provider == AIProvider.ANTHROPIC and not self.anthropic:
                raise ValueError("Anthropic configuration required when using Anthropic provider")
            elif self.ai_provider == AIProvider.HUGGINGFACE and not self.huggingface:
                raise ValueError("HuggingFace configuration required when using HuggingFace provider")
            elif self.ai_provider == AIProvider.OPENROUTER and not self.openrouter:
                raise ValueError("OpenRouter configuration required when using OpenRouter provider")
            elif self.ai_provider == AIProvider.DEEPSEEK and not self.deepseek:
                raise ValueError("DeepSeek configuration required when using DeepSeek provider")
            elif self.ai_provider == AIProvider.GEMINI and not self.gemini:
                raise ValueError("Gemini configuration required when using Gemini provider")

            # Validate vector database configurations
            if self.vector_database == VectorDatabase.QDRANT_CLOUD and not self.qdrant_cloud:
                raise ValueError("Qdrant Cloud configuration required when using Qdrant Cloud")
            elif self.vector_database == VectorDatabase.QDRANT_LOCAL and not self.qdrant_local:
                raise ValueError("Qdrant Local configuration required when using local Qdrant")

        # Validate agent pool configuration
        if self.agent_pool.max_agents > 100:
            raise ValueError("Maximum agents cannot exceed 100 for system stability")
        if self.agent_pool.concurrent_limit > self.agent_pool.max_agents:
            raise ValueError("Concurrent limit cannot exceed maximum agents")

    def _initialize_components(self):
        """Initialize component configurations with defaults if needed."""
        # Skip initialization if validation is disabled (for testing)
        if os.getenv("SKIP_CONFIG_VALIDATION", "").lower() in ("true", "1", "yes"):
            return

        # Initialize AI provider configurations
        if self.ai_provider == AIProvider.OPENAI:
            if not self.openai:
                api_key = os.getenv("OPENAI_API_KEY", "")
                if api_key:  # Only create if API key is available
                    self.openai = OpenAIConfig(api_key=api_key)
            # If still no openai config, create a minimal one for development
            if not self.openai:
                self.openai = OpenAIConfig(api_key="dummy-key-for-development")

        elif self.ai_provider == AIProvider.OLLAMA:
            if not self.ollama:
                self.ollama = OllamaConfig()

        elif self.ai_provider == AIProvider.ANTHROPIC:
            if not self.anthropic:
                api_key = os.getenv("ANTHROPIC_API_KEY", "")
                if api_key:  # Only create if API key is available
                    self.anthropic = AnthropicConfig(api_key=api_key)
                else:
                    # Create minimal config for development
                    self.anthropic = AnthropicConfig(api_key="dummy-key-for-development")

        elif self.ai_provider == AIProvider.HUGGINGFACE:
            if not self.huggingface:
                self.huggingface = HuggingFaceConfig()

        elif self.ai_provider == AIProvider.OPENROUTER:
            if not self.openrouter:
                api_key = os.getenv("OPENROUTER_API_KEY", "")
                if api_key:  # Only create if API key is available
                    self.openrouter = OpenRouterConfig(api_key=api_key)
                else:
                    # Create minimal config for development
                    self.openrouter = OpenRouterConfig(api_key="dummy-key-for-development")

        elif self.ai_provider == AIProvider.DEEPSEEK:
            if not self.deepseek:
                api_key = os.getenv("DEEPSEEK_API_KEY", "")
                if api_key:  # Only create if API key is available
                    self.deepseek = DeepSeekConfig(api_key=api_key)
                else:
                    # Create minimal config for development
                    self.deepseek = DeepSeekConfig(api_key="dummy-key-for-development")

        elif self.ai_provider == AIProvider.GEMINI:
            if not self.gemini:
                api_key = os.getenv("GEMINI_API_KEY", "")
                if api_key:  # Only create if API key is available
                    self.gemini = GeminiConfig(api_key=api_key)
                else:
                    # Create minimal config for development
                    self.gemini = GeminiConfig(api_key="dummy-key-for-development")

        # Initialize vector database configurations
        if self.vector_database == VectorDatabase.QDRANT_CLOUD:
            if not self.qdrant_cloud:
                url = os.getenv("QDRANT_URL", "")
                api_key = os.getenv("QDRANT_API_KEY", "")
                if url and api_key:  # Only create if both are available
                    self.qdrant_cloud = QdrantCloudConfig(url=url, api_key=api_key)
                else:
                    # For development, create a minimal config but only if we're not in production
                    if self.environment != Environment.PRODUCTION:
                        self.qdrant_cloud = QdrantCloudConfig(
                            url="http://localhost:6333",
                            api_key="dummy-key-for-development"
                        )
                    # In production, this will cause validation to fail, which is correct

        elif self.vector_database == VectorDatabase.QDRANT_LOCAL:
            if not self.qdrant_local:
                self.qdrant_local = QdrantLocalConfig()

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "AppConfig":
        """Create configuration from dictionary."""
        # Convert string enums back to enum values
        if "environment" in config_dict:
            config_dict["environment"] = Environment(config_dict["environment"])
        if "ai_provider" in config_dict:
            config_dict["ai_provider"] = AIProvider(config_dict["ai_provider"])
        if "vector_database" in config_dict:
            config_dict["vector_database"] = VectorDatabase(config_dict["vector_database"])

        # Convert nested configuration dictionaries to objects
        if "openai" in config_dict and isinstance(config_dict["openai"], dict):
            config_dict["openai"] = OpenAIConfig(**config_dict["openai"])
        if "ollama" in config_dict and isinstance(config_dict["ollama"], dict):
            config_dict["ollama"] = OllamaConfig(**config_dict["ollama"])
        if "anthropic" in config_dict and isinstance(config_dict["anthropic"], dict):
            config_dict["anthropic"] = AnthropicConfig(**config_dict["anthropic"])
        if "huggingface" in config_dict and isinstance(config_dict["huggingface"], dict):
            config_dict["huggingface"] = HuggingFaceConfig(**config_dict["huggingface"])
        if "openrouter" in config_dict and isinstance(config_dict["openrouter"], dict):
            config_dict["openrouter"] = OpenRouterConfig(**config_dict["openrouter"])
        if "deepseek" in config_dict and isinstance(config_dict["deepseek"], dict):
            config_dict["deepseek"] = DeepSeekConfig(**config_dict["deepseek"])
        if "gemini" in config_dict and isinstance(config_dict["gemini"], dict):
            config_dict["gemini"] = GeminiConfig(**config_dict["gemini"])
        if "qdrant_cloud" in config_dict and isinstance(config_dict["qdrant_cloud"], dict):
            qdrant_cloud_config = config_dict["qdrant_cloud"]
            # Only create QdrantCloudConfig if both url and api_key are present
            if qdrant_cloud_config.get("url") and qdrant_cloud_config.get("api_key"):
                config_dict["qdrant_cloud"] = QdrantCloudConfig(**qdrant_cloud_config)
            else:
                # Remove incomplete qdrant_cloud config to avoid errors
                del config_dict["qdrant_cloud"]
        if "qdrant_local" in config_dict and isinstance(config_dict["qdrant_local"], dict):
            config_dict["qdrant_local"] = QdrantLocalConfig(**config_dict["qdrant_local"])
        if "document" in config_dict and isinstance(config_dict["document"], dict):
            config_dict["document"] = DocumentConfig(**config_dict["document"])
        if "logging" in config_dict and isinstance(config_dict["logging"], dict):
            config_dict["logging"] = LoggingConfig(**config_dict["logging"])
        if "security" in config_dict and isinstance(config_dict["security"], dict):
            config_dict["security"] = SecurityConfig(**config_dict["security"])
        if "model_roles" in config_dict and isinstance(config_dict["model_roles"], dict):
            config_dict["model_roles"] = ModelRoleConfig(**config_dict["model_roles"])
        if "agent_pool" in config_dict and isinstance(config_dict["agent_pool"], dict):
            config_dict["agent_pool"] = AgentPoolConfig(**config_dict["agent_pool"])

        # Flatten database configurations
        if "postgres" in config_dict and isinstance(config_dict["postgres"], dict):
            postgres_config = config_dict.pop("postgres")
            config_dict["postgres_host"] = postgres_config.get("host", "localhost")
            config_dict["postgres_port"] = postgres_config.get("port", 5432)
            config_dict["postgres_db"] = postgres_config.get("db", "ai_law_firm")
            config_dict["postgres_user"] = postgres_config.get("user", "ai_law_user")
            config_dict["postgres_password"] = postgres_config.get("password", "ai_law_password_2024")

        if "mongodb" in config_dict and isinstance(config_dict["mongodb"], dict):
            mongodb_config = config_dict.pop("mongodb")
            config_dict["mongodb_host"] = mongodb_config.get("host", "localhost")
            config_dict["mongodb_port"] = mongodb_config.get("port", 27017)
            config_dict["mongodb_db"] = mongodb_config.get("db", "ai_law_firm")
            config_dict["mongodb_user"] = mongodb_config.get("user", "ai_law_admin")
            config_dict["mongodb_password"] = mongodb_config.get("password", "ai_law_mongo_password_2024")

        if "redis" in config_dict and isinstance(config_dict["redis"], dict):
            redis_config = config_dict.pop("redis")
            config_dict["redis_host"] = redis_config.get("host", "localhost")
            config_dict["redis_port"] = redis_config.get("port", 6379)
            config_dict["redis_password"] = redis_config.get("password", "ai_law_redis_password_2024")

        return cls(**config_dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        result = {}
        for key, value in self.__dict__.items():
            if hasattr(value, "to_dict"):
                result[key] = value.to_dict()
            elif isinstance(value, Enum):
                result[key] = value.value
            else:
                result[key] = value
        return result

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == Environment.PRODUCTION

    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == Environment.DEVELOPMENT

    def is_local(self) -> bool:
        """Check if running in local environment."""
        return self.environment == Environment.LOCAL

    @property
    def document_config(self) -> DocumentConfig:
        """Get document configuration, ensuring it's always a DocumentConfig object."""
        if isinstance(self.document, DocumentConfig):
            return self.document
        elif isinstance(self.document, dict):
            # Convert dict to DocumentConfig
            return DocumentConfig(**self.document)
        else:
            # Fallback to default
            return DocumentConfig()

    def get_ai_config(self) -> Union[OpenAIConfig, OllamaConfig, AnthropicConfig, HuggingFaceConfig, OpenRouterConfig, DeepSeekConfig, GeminiConfig]:
        """Get the active AI provider configuration."""
        if self.ai_provider == AIProvider.OPENAI:
            if not self.openai:
                # Create a minimal OpenAI config for development
                self.openai = OpenAIConfig(api_key="dummy-key-for-development")
            return self.openai
        elif self.ai_provider == AIProvider.OLLAMA:
            if not self.ollama:
                self.ollama = OllamaConfig()
            return self.ollama
        elif self.ai_provider == AIProvider.ANTHROPIC:
            if not self.anthropic:
                # Create a minimal Anthropic config for development
                self.anthropic = AnthropicConfig(api_key="dummy-key-for-development")
            return self.anthropic
        elif self.ai_provider == AIProvider.HUGGINGFACE:
            if not self.huggingface:
                self.huggingface = HuggingFaceConfig()
            return self.huggingface
        elif self.ai_provider == AIProvider.OPENROUTER:
            if not self.openrouter:
                # Create a minimal OpenRouter config for development
                self.openrouter = OpenRouterConfig(api_key="dummy-key-for-development")
            return self.openrouter
        elif self.ai_provider == AIProvider.DEEPSEEK:
            if not self.deepseek:
                # Create a minimal DeepSeek config for development
                self.deepseek = DeepSeekConfig(api_key="dummy-key-for-development")
            return self.deepseek
        elif self.ai_provider == AIProvider.GEMINI:
            if not self.gemini:
                # Create a minimal Gemini config for development
                self.gemini = GeminiConfig(api_key="dummy-key-for-development")
            return self.gemini
        else:
            raise ValueError(f"Unsupported AI provider: {self.ai_provider}")

    def get_vector_db_config(self) -> Union[QdrantCloudConfig, QdrantLocalConfig]:
        """Get the active vector database configuration."""
        if self.vector_database == VectorDatabase.QDRANT_CLOUD:
            if not self.qdrant_cloud:
                # Create a minimal Qdrant Cloud config for development
                self.qdrant_cloud = QdrantCloudConfig(
                    url=os.getenv("QDRANT_URL", "http://localhost:6333"),
                    api_key=os.getenv("QDRANT_API_KEY", "dummy-key-for-development")
                )
            return self.qdrant_cloud
        elif self.vector_database == VectorDatabase.QDRANT_LOCAL:
            if not self.qdrant_local:
                self.qdrant_local = QdrantLocalConfig()
            return self.qdrant_local
        else:
            raise ValueError(f"Unsupported vector database: {self.vector_database}")

    def get_model_for_task(self, task_type: str) -> str:
        """Get the appropriate model for a specific task type."""
        if self.ai_provider == AIProvider.OPENAI and self.openai:
            return self.openai.get_model_for_task(task_type)
        else:
            # Fallback to primary model for other providers
            return self.model_roles.specialized_tasks.get(task_type, self.model_roles.primary_analysis)

    def get_agent_pool_status(self) -> Dict[str, Any]:
        """Get current agent pool status and capacity."""
        return {
            "min_agents": self.agent_pool.min_agents,
            "max_agents": self.agent_pool.max_agents,
            "concurrent_limit": self.agent_pool.concurrent_limit,
            "load_balancing": self.agent_pool.load_balancing,
            "capacity_utilization": 0.0,  # Would be calculated based on active agents
            "available_slots": self.agent_pool.concurrent_limit
        }

    def can_scale_agents(self, requested_count: int) -> bool:
        """Check if the system can scale to the requested number of agents."""
        return (
            self.agent_pool.min_agents <= requested_count <= self.agent_pool.max_agents and
            requested_count <= self.agent_pool.concurrent_limit
        )

    def get_cost_estimate_for_pipeline(self, tasks: List[str], avg_tokens_per_task: int = 1000) -> Dict[str, Any]:
        """Get cost estimate for a complete analysis pipeline."""
        total_cost = 0.0
        task_costs = {}

        for task in tasks:
            model = self.get_model_for_task(task)
            if self.ai_provider == AIProvider.OPENAI and self.openai:
                cost = self.openai.get_cost_estimate_for_task(
                    task, avg_tokens_per_task, avg_tokens_per_task // 2
                )
            else:
                # Estimate for other providers (simplified)
                cost = avg_tokens_per_task * 0.00001  # Rough estimate

            task_costs[task] = {
                "model": model,
                "estimated_cost": cost,
                "tokens": avg_tokens_per_task
            }
            total_cost += cost

        return {
            "total_estimated_cost": total_cost,
            "task_breakdown": task_costs,
            "currency": "USD",
            "disclaimer": "Estimates are approximate and may vary based on actual usage"
        }

    def get_optimal_agent_count(self, workload_size: str) -> int:
        """Get optimal agent count based on workload size."""
        workload_multipliers = {
            "small": 1,
            "medium": 2,
            "large": 3,
            "xlarge": 5
        }

        base_count = workload_multipliers.get(workload_size, 1)
        optimal_count = min(
            self.agent_pool.max_agents,
            max(self.agent_pool.min_agents, base_count * 3)  # 3 agents per workload multiplier
        )

        return optimal_count


# Global configuration instance
_config: Optional[AppConfig] = None


def get_config() -> AppConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = load_config()
    return _config


def load_config(config_path: Optional[str] = None, environment: Optional[str] = None) -> AppConfig:
    """
    Load configuration from file or environment.

    Args:
        config_path: Path to configuration file (optional)
        environment: Environment name (optional, auto-detected if not provided)

    Returns:
        AppConfig: Loaded configuration instance
    """
    import yaml

    # Auto-detect environment if not provided
    if environment is None:
        environment = os.getenv("APP_ENV", "development")

    # Default config paths to try
    config_paths = [
        config_path,
        f"config/{environment}.yaml",
        f"config/{environment}.yml",
        "config/default.yaml",
        "config/default.yml",
        f"../config/{environment}.yaml",
        f"../config/{environment}.yml",
        "../config/default.yaml",
        "../config/default.yml"
    ]

    config_dict = {}

    # Try to load from files
    for path in config_paths:
        if path and Path(path).exists():
            try:
                with open(path, "r") as f:
                    # Try safe_load first, if it fails due to Python objects, try full_load
                    try:
                        file_config = yaml.safe_load(f) or {}
                    except yaml.YAMLError:
                        # Reset file pointer and try full load for Python objects
                        f.seek(0)
                        file_config = yaml.full_load(f) or {}

                    # Convert any loaded Python objects to dictionaries
                    config_dict.update(_convert_config_objects_to_dict(file_config))

                    # Debug: Print what we loaded
                    print(f"DEBUG: Loaded config from {path}")
                    print(f"DEBUG: postgres in file_config: {'postgres' in file_config}")
                    print(f"DEBUG: mongodb in file_config: {'mongodb' in file_config}")
                    print(f"DEBUG: redis in file_config: {'redis' in file_config}")
                    print(f"DEBUG: Full config_dict after loading: {config_dict}")
                break
            except Exception as e:
                print(f"Warning: Could not load config from {path}: {e}")
        else:
            print(f"DEBUG: Config path {path} does not exist or is None")

    # Override with environment variables
    config_dict.update(_load_from_env())

    # Set environment
    config_dict["environment"] = environment

    # Ensure we have proper defaults for missing configurations
    _ensure_config_defaults(config_dict)

    return AppConfig.from_dict(config_dict)


def _ensure_config_defaults(config_dict: Dict[str, Any]) -> None:
    """Ensure configuration has proper defaults for missing nested configs."""
    # Ensure document config exists
    if "document" not in config_dict or not isinstance(config_dict["document"], dict):
        config_dict["document"] = {
            "max_size_mb": 50,
            "allowed_extensions": ["pdf", "docx", "txt", "html", "htm", "md", "markdown", "csv", "xls", "xlsx", "eml", "zip"],
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "supported_languages": ["en"]
        }

    # Ensure logging config exists
    if "logging" not in config_dict or not isinstance(config_dict["logging"], dict):
        config_dict["logging"] = {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_path": None,
            "max_file_size": 10485760,
            "backup_count": 5
        }

    # Ensure security config exists
    if "security" not in config_dict or not isinstance(config_dict["security"], dict):
        config_dict["security"] = {
            "secret_key": secrets.token_hex(32),
            "session_timeout": 3600,
            "max_login_attempts": 5,
            "rate_limit_requests": 100,
            "rate_limit_window": 60
        }

    # Ensure model_roles config exists
    if "model_roles" not in config_dict or not isinstance(config_dict["model_roles"], dict):
        config_dict["model_roles"] = {
            "primary_analysis": "gpt-4o-mini",
            "secondary_review": "gpt-4o-mini",
            "final_polish": "gpt-4o"
        }

    # Ensure agent_pool config exists
    if "agent_pool" not in config_dict or not isinstance(config_dict["agent_pool"], dict):
        config_dict["agent_pool"] = {
            "min_agents": 3,
            "max_agents": 45,
            "concurrent_limit": 10,
            "agent_timeout": 300,
            "load_balancing": "round_robin"
        }


def _convert_config_objects_to_dict(config_data: Any) -> Dict[str, Any]:
    """Convert Python objects in config to dictionaries."""
    if isinstance(config_data, dict):
        result = {}
        for key, value in config_data.items():
            if hasattr(value, '__dict__') and hasattr(value, '__class__'):
                # Convert Python object to dict
                obj_dict = {}
                for attr_name in dir(value):
                    if not attr_name.startswith('_') and not callable(getattr(value, attr_name)):
                        try:
                            obj_dict[attr_name] = getattr(value, attr_name)
                        except:
                            pass  # Skip attributes that can't be accessed
                result[key] = obj_dict
            else:
                result[key] = _convert_config_objects_to_dict(value)
        return result
    elif isinstance(config_data, list):
        return [_convert_config_objects_to_dict(item) for item in config_data]
    elif hasattr(config_data, '__dict__') and hasattr(config_data, '__class__'):
        # Handle top-level Python objects
        obj_dict = {}
        for attr_name in dir(config_data):
            if not attr_name.startswith('_') and not callable(getattr(config_data, attr_name)):
                try:
                    obj_dict[attr_name] = getattr(config_data, attr_name)
                except:
                    pass  # Skip attributes that can't be accessed
        return obj_dict
    else:
        return config_data


def _load_from_env() -> Dict[str, Any]:
    """Load configuration from environment variables."""
    env_config = {}

    # Environment
    if os.getenv("APP_ENV"):
        env_config["environment"] = os.getenv("APP_ENV")

    # Debug mode
    if os.getenv("DEBUG"):
        env_config["debug"] = os.getenv("DEBUG").lower() in ("true", "1", "yes")

    # AI Provider settings
    if os.getenv("AI_PROVIDER"):
        env_config["ai_provider"] = os.getenv("AI_PROVIDER")

    # OpenAI settings
    if os.getenv("OPENAI_API_KEY"):
        env_config.setdefault("openai", {})
        env_config["openai"]["api_key"] = os.getenv("OPENAI_API_KEY")
    if os.getenv("OPENAI_MODEL"):
        env_config.setdefault("openai", {})
        env_config["openai"]["model"] = os.getenv("OPENAI_MODEL")

    # Ollama settings
    if os.getenv("OLLAMA_BASE_URL"):
        env_config.setdefault("ollama", {})
        env_config["ollama"]["base_url"] = os.getenv("OLLAMA_BASE_URL")
    if os.getenv("OLLAMA_MODEL"):
        env_config.setdefault("ollama", {})
        env_config["ollama"]["model"] = os.getenv("OLLAMA_MODEL")

    # Anthropic settings
    if os.getenv("ANTHROPIC_API_KEY"):
        env_config.setdefault("anthropic", {})
        env_config["anthropic"]["api_key"] = os.getenv("ANTHROPIC_API_KEY")
    if os.getenv("ANTHROPIC_MODEL"):
        env_config.setdefault("anthropic", {})
        env_config["anthropic"]["model"] = os.getenv("ANTHROPIC_MODEL")

    # HuggingFace settings
    if os.getenv("HUGGINGFACE_API_KEY"):
        env_config.setdefault("huggingface", {})
        env_config["huggingface"]["api_key"] = os.getenv("HUGGINGFACE_API_KEY")
    if os.getenv("HUGGINGFACE_MODEL"):
        env_config.setdefault("huggingface", {})
        env_config["huggingface"]["model"] = os.getenv("HUGGINGFACE_MODEL")

    # OpenRouter settings
    if os.getenv("OPENROUTER_API_KEY"):
        env_config.setdefault("openrouter", {})
        env_config["openrouter"]["api_key"] = os.getenv("OPENROUTER_API_KEY")
    if os.getenv("OPENROUTER_MODEL"):
        env_config.setdefault("openrouter", {})
        env_config["openrouter"]["model"] = os.getenv("OPENROUTER_MODEL")

    # DeepSeek settings
    if os.getenv("DEEPSEEK_API_KEY"):
        env_config.setdefault("deepseek", {})
        env_config["deepseek"]["api_key"] = os.getenv("DEEPSEEK_API_KEY")
    if os.getenv("DEEPSEEK_MODEL"):
        env_config.setdefault("deepseek", {})
        env_config["deepseek"]["model"] = os.getenv("DEEPSEEK_MODEL")

    # Gemini settings
    if os.getenv("GEMINI_API_KEY"):
        env_config.setdefault("gemini", {})
        env_config["gemini"]["api_key"] = os.getenv("GEMINI_API_KEY")
    if os.getenv("GEMINI_MODEL"):
        env_config.setdefault("gemini", {})
        env_config["gemini"]["model"] = os.getenv("GEMINI_MODEL")

    # Vector Database settings
    if os.getenv("VECTOR_DATABASE"):
        env_config["vector_database"] = os.getenv("VECTOR_DATABASE")

    # Qdrant Cloud settings - only set if both URL and API key are provided
    qdrant_url = os.getenv("QDRANT_URL")
    qdrant_api_key = os.getenv("QDRANT_API_KEY")
    if qdrant_url and qdrant_api_key:
        env_config.setdefault("qdrant_cloud", {})
        env_config["qdrant_cloud"]["url"] = qdrant_url
        env_config["qdrant_cloud"]["api_key"] = qdrant_api_key

    # Document settings
    if os.getenv("MAX_DOCUMENT_SIZE_MB"):
        env_config.setdefault("document", {})
        env_config["document"]["max_size_mb"] = int(os.getenv("MAX_DOCUMENT_SIZE_MB"))

    # Logging settings
    if os.getenv("LOG_LEVEL"):
        env_config.setdefault("logging", {})
        env_config["logging"]["level"] = os.getenv("LOG_LEVEL")

    # Database settings
    if os.getenv("POSTGRES_HOST"):
        env_config["postgres_host"] = os.getenv("POSTGRES_HOST")
    if os.getenv("POSTGRES_PORT"):
        env_config["postgres_port"] = int(os.getenv("POSTGRES_PORT"))
    if os.getenv("POSTGRES_DB"):
        env_config["postgres_db"] = os.getenv("POSTGRES_DB")
    if os.getenv("POSTGRES_USER"):
        env_config["postgres_user"] = os.getenv("POSTGRES_USER")
    if os.getenv("POSTGRES_PASSWORD"):
        env_config["postgres_password"] = os.getenv("POSTGRES_PASSWORD")

    if os.getenv("MONGODB_HOST"):
        env_config["mongodb_host"] = os.getenv("MONGODB_HOST")
    if os.getenv("MONGODB_PORT"):
        env_config["mongodb_port"] = int(os.getenv("MONGODB_PORT"))
    if os.getenv("MONGODB_DB"):
        env_config["mongodb_db"] = os.getenv("MONGODB_DB")
    if os.getenv("MONGODB_USER"):
        env_config["mongodb_user"] = os.getenv("MONGODB_USER")
    if os.getenv("MONGODB_PASSWORD"):
        env_config["mongodb_password"] = os.getenv("MONGODB_PASSWORD")

    if os.getenv("REDIS_HOST"):
        env_config["redis_host"] = os.getenv("REDIS_HOST")
    if os.getenv("REDIS_PORT"):
        env_config["redis_port"] = int(os.getenv("REDIS_PORT"))
    if os.getenv("REDIS_PASSWORD"):
        env_config["redis_password"] = os.getenv("REDIS_PASSWORD")

    return env_config


def save_config(config: AppConfig, config_path: str) -> None:
    """Save configuration to file."""
    import yaml

    config_dir = Path(config_path).parent
    config_dir.mkdir(parents=True, exist_ok=True)

    with open(config_path, "w") as f:
        yaml.dump(config.to_dict(), f, default_flow_style=False, sort_keys=False)


def create_default_configs() -> None:
    """Create default configuration files for all environments."""
    environments = ["default", "development", "production", "local"]

    for env in environments:
        config_path = f"config/{env}.yaml"
        if not Path(config_path).exists():
            if env == "default":
                # Create default config with minimal validation
                config = AppConfig.__new__(AppConfig)
                config.environment = Environment.DEVELOPMENT
                config.debug = False
                config.ai_provider = AIProvider.OPENAI
                config.vector_database = VectorDatabase.QDRANT_CLOUD
                config.openai = OpenAIConfig(api_key="dummy-key-for-development")
                config.qdrant_cloud = QdrantCloudConfig(
                    url="http://localhost:6333",
                    api_key="dummy-key-for-development"
                )
                config.document = DocumentConfig()
                config.logging = LoggingConfig()
                config.security = SecurityConfig()
            elif env == "development":
                config = AppConfig.__new__(AppConfig)
                config.environment = Environment.DEVELOPMENT
                config.debug = True
                config.ai_provider = AIProvider.OPENAI
                config.vector_database = VectorDatabase.QDRANT_CLOUD
                config.openai = OpenAIConfig(api_key="dummy-key-for-development")
                config.qdrant_cloud = QdrantCloudConfig(
                    url="http://localhost:6333",
                    api_key="dummy-key-for-development"
                )
                config.document = DocumentConfig()
                config.logging = LoggingConfig(level="DEBUG")
                config.security = SecurityConfig()
            elif env == "production":
                config = AppConfig.__new__(AppConfig)
                config.environment = Environment.PRODUCTION
                config.debug = False
                config.ai_provider = AIProvider.OPENAI
                config.vector_database = VectorDatabase.QDRANT_CLOUD
                config.openai = OpenAIConfig(api_key="dummy-key-for-development")
                config.qdrant_cloud = QdrantCloudConfig(
                    url="http://localhost:6333",
                    api_key="dummy-key-for-development"
                )
                config.document = DocumentConfig()
                config.logging = LoggingConfig(level="WARNING")
                config.security = SecurityConfig()
            elif env == "local":
                config = AppConfig.__new__(AppConfig)
                config.environment = Environment.LOCAL
                config.debug = True
                config.ai_provider = AIProvider.OLLAMA
                config.vector_database = VectorDatabase.QDRANT_LOCAL
                config.ollama = OllamaConfig()
                config.qdrant_local = QdrantLocalConfig()
                config.document = DocumentConfig()
                config.logging = LoggingConfig(level="INFO")
                config.security = SecurityConfig()

            save_config(config, config_path)
            print(f"Created default configuration: {config_path}")


# Initialize default configurations on import
if __name__ == "__main__":
    create_default_configs()