"""
Service URL and Port Configuration for AI Law Firm.

This module provides centralized configuration for all service URLs and ports,
using environment variables to avoid hardcoded values.
"""

import os
from typing import Dict, Any


class ServiceConfig:
    """Centralized service configuration using environment variables."""

    # Database Service URLs and Ports
    @property
    def postgres_host(self) -> str:
        return os.getenv("POSTGRES_HOST", "localhost")

    @property
    def postgres_port(self) -> int:
        return int(os.getenv("POSTGRES_PORT", "5432"))

    @property
    def postgres_db(self) -> str:
        return os.getenv("POSTGRES_DB", "ai_law_firm")

    @property
    def postgres_user(self) -> str:
        return os.getenv("POSTGRES_USER", "ai_law_user")

    @property
    def postgres_password(self) -> str:
        return os.getenv("POSTGRES_PASSWORD", "ai_law_password_2024ai_law_password_2024f7d9e3a8-dc21-11d1-bd54-00c04fd430c8")

    @property
    def mongodb_host(self) -> str:
        return os.getenv("MONGODB_HOST", "localhost")

    @property
    def mongodb_port(self) -> int:
        return int(os.getenv("MONGODB_PORT", "27017"))

    @property
    def mongodb_db(self) -> str:
        return os.getenv("MONGODB_DB", "ai_law_firm")

    @property
    def mongodb_user(self) -> str:
        return os.getenv("MONGODB_USER", "ai_law_admin")

    @property
    def mongodb_password(self) -> str:
        return os.getenv("MONGODB_PASSWORD", "ai_law_mongo_password_2024ai_law_password_2024f7d9e3a8-dc21-11d1-bd54-00c04fd430c8")

    @property
    def redis_host(self) -> str:
        return os.getenv("REDIS_HOST", "localhost")

    @property
    def redis_port(self) -> int:
        return int(os.getenv("REDIS_PORT", "6379"))

    @property
    def redis_password(self) -> str:
        return os.getenv("REDIS_PASSWORD", "ai_law_redis_password_2024ai_law_password_2024f7d9e3a8-dc21-11d1-bd54-00c04fd430c8")

    # Vector Database URLs and Ports
    @property
    def qdrant_url(self) -> str:
        return os.getenv("QDRANT_URL", "http://localhost:6334")

    @property
    def qdrant_api_key(self) -> str:
        return os.getenv("QDRANT_API_KEY", "")

    @property
    def qdrant_collection(self) -> str:
        return os.getenv("QDRANT_COLLECTION", "legal_documents")

    # AI Provider URLs
    @property
    def ollama_base_url(self) -> str:
        return os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

    @property
    def ollama_model(self) -> str:
        return os.getenv("OLLAMA_MODEL", "llama3.2:3b")

    # Application URLs
    @property
    def frontend_url(self) -> str:
        return os.getenv("FRONTEND_URL", "http://localhost:3000")

    @property
    def backend_url(self) -> str:
        return os.getenv("BACKEND_URL", "http://localhost:8000")

    # Docker-specific overrides for local development
    def get_docker_service_url(self, service_name: str, default_url: str) -> str:
        """Get service URL with Docker port mapping for local development."""
        env = os.getenv("APP_ENV", "development")

        if env == "local":
            # Docker port mappings for local development
            docker_mappings = {
                "qdrant_http": os.getenv("QDRANT_HTTP_URL", "http://localhost:63331"),
                "qdrant_grpc": os.getenv("QDRANT_GRPC_URL", "http://localhost:63330"),
                "postgres": f"{self.postgres_host}:{os.getenv('POSTGRES_DOCKER_PORT', '54320')}",
                "mongodb": f"{self.mongodb_host}:{os.getenv('MONGODB_DOCKER_PORT', '27019')}",
                "redis": f"{self.redis_host}:{os.getenv('REDIS_DOCKER_PORT', '63790')}",
            }
            return docker_mappings.get(service_name, default_url)

        return default_url

    # Override database connection methods for Docker
    def get_postgres_connection_string(self) -> str:
        """Build PostgreSQL connection string with Docker port mapping."""
        env = os.getenv("APP_ENV", "development")
        if env == "local":
            port = os.getenv("POSTGRES_DOCKER_PORT", "54320")
        else:
            port = self.postgres_port

        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{port}/{self.postgres_db}"

    def get_mongodb_connection_string(self) -> str:
        """Build MongoDB connection string with Docker port mapping."""
        env = os.getenv("APP_ENV", "development")
        if env == "local":
            port = os.getenv("MONGODB_DOCKER_PORT", "27019")
        else:
            port = self.mongodb_port

        return f"mongodb://{self.mongodb_user}:{self.mongodb_password}@{self.mongodb_host}:{port}/?authSource=admin"

    def get_redis_connection_string(self) -> str:
        """Build Redis connection string with Docker port mapping."""
        env = os.getenv("APP_ENV", "development")
        if env == "local":
            port = os.getenv("REDIS_DOCKER_PORT", "63790")
        else:
            port = self.redis_port

        return f"redis://:{self.redis_password}@{self.redis_host}:{port}"

    # Connection string builders
    def get_postgres_connection_string(self) -> str:
        """Build PostgreSQL connection string."""
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"

    def get_mongodb_connection_string(self) -> str:
        """Build MongoDB connection string."""
        return f"mongodb://{self.mongodb_user}:{self.mongodb_password}@{self.mongodb_host}:{self.mongodb_port}/?authSource=admin"

    def get_redis_connection_string(self) -> str:
        """Build Redis connection string."""
        return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}"

    def get_qdrant_config(self) -> Dict[str, Any]:
        """Get Qdrant configuration dictionary."""
        return {
            "url": self.get_docker_service_url("qdrant_http", self.qdrant_url),
            "api_key": self.qdrant_api_key,
            "collection_name": self.qdrant_collection
        }

    def get_ollama_config(self) -> Dict[str, Any]:
        """Get Ollama configuration dictionary."""
        return {
            "base_url": self.ollama_base_url,
            "model": self.ollama_model
        }


# Global instance
_service_config: ServiceConfig = None


def get_service_config() -> ServiceConfig:
    """Get the global service configuration instance."""
    global _service_config
    if _service_config is None:
        _service_config = ServiceConfig()
    return _service_config


# Convenience functions for backward compatibility
def get_postgres_connection_string() -> str:
    """Get PostgreSQL connection string."""
    return get_service_config().get_postgres_connection_string()


def get_mongodb_connection_string() -> str:
    """Get MongoDB connection string."""
    return get_service_config().get_mongodb_connection_string()


def get_redis_connection_string() -> str:
    """Get Redis connection string."""
    return get_service_config().get_redis_connection_string()


def get_qdrant_config() -> Dict[str, Any]:
    """Get Qdrant configuration."""
    return get_service_config().get_qdrant_config()


def get_ollama_config() -> Dict[str, Any]:
    """Get Ollama configuration."""
    return get_service_config().get_ollama_config()