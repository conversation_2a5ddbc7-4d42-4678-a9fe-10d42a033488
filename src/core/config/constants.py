"""
Centralized configuration constants for the AI Law Firm backend.
This is the single source of truth for all backend configuration values.
"""

import os
from typing import Dict, Any

# Server Configuration
SERVER_CONFIG = {
    "host": os.getenv("HOST", "0.0.0.0"),
    "port": int(os.getenv("PORT", "8000")),
    "debug": os.getenv("DEBUG", "false").lower() in ("true", "1", "yes"),
    "reload": os.getenv("RELOAD", "true").lower() in ("true", "1", "yes"),
}

# Database Configuration
DATABASE_CONFIG = {
    "postgres": {
        "host": os.getenv("POSTGRES_HOST", "localhost"),
        "port": int(os.getenv("POSTGRES_PORT", "5432")),
        "database": os.getenv("POSTGRES_DB", "ai_law_firm"),
        "user": os.getenv("POSTGRES_USER", "ai_law_user"),
        "password": os.getenv("POSTGRES_PASSWORD", "ai_law_password_2024"),
    },
    "mongodb": {
        "host": os.getenv("MONGODB_HOST", "localhost"),
        "port": int(os.getenv("MONGODB_PORT", "27017")),
        "database": os.getenv("MONGODB_DB", "ai_law_firm"),
        "user": os.getenv("MONGODB_USER", "ai_law_admin"),
        "password": os.getenv("MONGODB_PASSWORD", "ai_law_mongo_password_2024"),
    },
    "redis": {
        "host": os.getenv("REDIS_HOST", "localhost"),
        "port": int(os.getenv("REDIS_PORT", "6379")),
        "password": os.getenv("REDIS_PASSWORD", "ai_law_redis_password_2024"),
    },
    "qdrant": {
        "host": os.getenv("QDRANT_HOST", "localhost"),
        "port": int(os.getenv("QDRANT_PORT", "6333")),
        "api_key": os.getenv("QDRANT_API_KEY"),
    }
}

# Security Configuration
SECURITY_CONFIG = {
    "jwt_secret": os.getenv("JWT_SECRET_KEY", "dummy-jwt-secret-for-development"),
    "jwt_algorithm": "HS256",
    "jwt_access_token_expire_minutes": 30,
    "jwt_refresh_token_expire_days": 7,
}

# API Configuration
API_CONFIG = {
    "title": "AI Law Firm API",
    "version": "1.0.0",
    "description": "AI-powered legal document analysis and research platform",
    "cors_origins": ["*"],  # Configure appropriately for production
}

# Feature Flags
FEATURE_FLAGS = {
    "enable_caching": os.getenv("ENABLE_CACHING", "true").lower() in ("true", "1", "yes"),
    "enable_monitoring": os.getenv("ENABLE_MONITORING", "true").lower() in ("true", "1", "yes"),
    "enable_debug_logging": os.getenv("ENABLE_DEBUG_LOGGING", "false").lower() in ("true", "1", "yes"),
}

def get_database_url(db_type: str) -> str:
    """Generate database connection URL from configuration."""
    if db_type == "postgres":
        config = DATABASE_CONFIG["postgres"]
        return f"postgresql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
    elif db_type == "mongodb":
        config = DATABASE_CONFIG["mongodb"]
        return f"mongodb://{config['user']}:{config['password']}@{config['host']}:{config['port']}/?authSource=admin"
    elif db_type == "redis":
        config = DATABASE_CONFIG["redis"]
        return f"redis://:{config['password']}@{config['host']}:{config['port']}"
    else:
        raise ValueError(f"Unsupported database type: {db_type}")

# Export all configurations
__all__ = [
    "SERVER_CONFIG",
    "DATABASE_CONFIG",
    "SECURITY_CONFIG",
    "API_CONFIG",
    "FEATURE_FLAGS",
    "get_database_url",
]