"""
Main Page component for AI Law Firm.

This component provides the primary user interface for document analysis,
integrating all the modular components into a cohesive workflow.
"""

import streamlit as st
from typing import Optional

from core.config.settings import AppConfig
from ui.components.api_config import APIConfigurationComponent
from ui.components.document_uploader import DocumentUploaderComponent
from ui.components.analysis_selector import AnalysisSelectorComponent
from ui.components.results_display import ResultsDisplayComponent
from ui.components.error_handler import ErrorHandlerComponent


class MainPage:
    """
    Main page component that orchestrates the complete document analysis workflow.

    This component brings together all UI components in a logical flow:
    1. Configuration → 2. Document Upload → 3. Analysis Setup → 4. Results Display
    """

    def __init__(self, config: AppConfig):
        self.config = config

        # Initialize components
        self.api_config = APIConfigurationComponent(config)
        self.document_uploader = DocumentUploaderComponent(config.document)
        self.analysis_selector = AnalysisSelectorComponent()
        self.results_display = ResultsDisplayComponent()
        self.error_handler = ErrorHandlerComponent()

    def render(self):
        """Render the main page with all components."""
        # Header section
        self._render_header()

        # Configuration section
        api_state = self._render_configuration_section()

        # Main workflow (only if configured)
        if api_state.is_configured:
            self._render_main_workflow()
        else:
            self._render_configuration_prompt()

        # Footer
        self._render_footer()

    def _render_header(self):
        """Render page header."""
        st.title("⚖️ AI Legal Document Analysis Platform")
        st.markdown("*Powered by Multi-Agent AI Technology*")

        # Status bar
        col1, col2, col3 = st.columns(3)
        with col1:
            st.caption("🔧 Modular Architecture v2.0")
        with col2:
            st.caption("🤖 Multi-Agent Analysis")
        with col3:
            st.caption("📊 Real-time Processing")

    def _render_configuration_section(self):
        """Render API configuration in sidebar."""
        with st.sidebar:
            st.header("🔑 Service Configuration")
            api_state = self.api_config.render()
            return api_state

    def _render_configuration_prompt(self):
        """Render prompt for configuration."""
        st.info("👈 **Getting Started**")
        st.markdown("""
        To begin analyzing legal documents:

        1. **Configure AI Provider** in the sidebar
           - Choose OpenAI (cloud) or Ollama (local)
           - Enter your API credentials

        2. **Setup Vector Database**
           - Configure Qdrant connection
           - Ensure database is accessible

        3. **Upload Document**
           - Select a legal document (PDF, DOCX, etc.)
           - Wait for processing to complete

        4. **Run Analysis**
           - Choose analysis type
           - Execute and review results
        """)

        # Feature highlights
        st.subheader("🚀 Key Features")
        features = [
            "📑 **Contract Analysis** - Comprehensive review and risk assessment",
            "🔍 **Legal Research** - Case law and precedent research",
            "⚠️ **Risk Assessment** - Liability and compliance evaluation",
            "✅ **Compliance Check** - Regulatory requirement verification",
            "💭 **Custom Queries** - Flexible analysis with AI agents"
        ]

        for feature in features:
            st.markdown(feature)

    def _render_main_workflow(self):
        """Render the main analysis workflow."""
        # Document upload section
        st.header("📄 Document Analysis Workflow")

        # Step 1: Document Upload
        with st.expander("📤 Step 1: Upload Document", expanded=True):
            upload_state = self.document_uploader.render(
                on_document_processed=self._on_document_processed
            )

        # Step 2: Analysis Configuration (only if documents available)
        if upload_state.processed_files:
            with st.expander("🎯 Step 2: Configure Analysis", expanded=True):
                # Get document type for intelligent defaults
                latest_result = list(upload_state.processing_results.values())[-1]
                document_type = latest_result.document.metadata.document_type if latest_result.document else None

                analysis_config = self.analysis_selector.render(
                    document_type=document_type,
                    on_analysis_selected=self._on_analysis_selected
                )

        # Step 3: Results (if analysis completed)
        if hasattr(st.session_state, 'current_analysis_result'):
            with st.expander("📊 Step 3: Analysis Results", expanded=True):
                self._render_analysis_results()

    def _render_analysis_results(self):
        """Render analysis results."""
        result = st.session_state.current_analysis_result

        # Convert to display format
        from ui.components.results_display import AnalysisResult as DisplayResult

        display_result = DisplayResult(
            analysis_type=result.get('analysis_type', 'unknown'),
            query=result.get('query', 'Analysis query'),
            responses=result.get('responses', []),
            processing_time=result.get('processing_time', 0.0),
            cost_estimate=result.get('cost_estimate', 0.0)
        )

        self.results_display.render(display_result)

    def _render_footer(self):
        """Render page footer."""
        st.markdown("---")
        st.caption("© 2024 AI Legal Analysis Platform - Refactored Architecture v2.0")
        st.caption("*Built with Streamlit, Multi-Agent AI, and Modern Software Engineering Practices*")

    def _on_document_processed(self, result):
        """Handle document processing completion."""
        try:
            if result.success:
                st.success(f"✅ Document processed successfully: {result.document.metadata.filename}")
                st.info("🎯 **Next:** Configure your analysis type above")
            else:
                st.error("❌ Document processing failed")
                # Show errors
                for error in result.errors:
                    st.error(error)

        except Exception as e:
            self.error_handler.handle_error(e)

    def _on_analysis_selected(self, analysis_config):
        """Handle analysis configuration selection."""
        try:
            st.info(f"🎯 **Analysis Selected:** {analysis_config.display_name}")
            st.write(f"**Description:** {analysis_config.description}")
            st.write(f"**AI Agents:** {', '.join(analysis_config.agents)}")

            # Store for execution
            st.session_state.selected_analysis_config = analysis_config

            # Show execution button
            if st.button("🚀 Execute Analysis", type="primary"):
                self._execute_analysis(analysis_config)

        except Exception as e:
            self.error_handler.handle_error(e)

    def _execute_analysis(self, analysis_config):
        """Execute the selected analysis."""
        try:
            with st.spinner("🔍 Analyzing document..."):
                # This would integrate with the orchestration service
                # For now, create a mock result
                import time
                time.sleep(2)  # Simulate processing

                mock_result = {
                    'analysis_type': analysis_config.analysis_type,
                    'query': analysis_config.default_query,
                    'responses': [
                        type('MockResponse', (), {
                            'content': f"Analysis completed for {analysis_config.display_name}. This is a mock response showing the analysis workflow.",
                            'confidence_score': 0.85,
                            'processing_time': 2.1,
                            'model_used': 'gpt-4o'
                        })()
                    ],
                    'processing_time': 2.1,
                    'cost_estimate': 0.05
                }

                st.session_state.current_analysis_result = mock_result
                st.success("✅ Analysis completed!")
                st.rerun()  # Refresh to show results

        except Exception as e:
            self.error_handler.handle_error(e)

    def get_page_status(self) -> dict:
        """Get current page status."""
        return {
            "configured": getattr(st.session_state, 'api_configured', False),
            "documents_uploaded": len(getattr(st.session_state, 'processed_files', [])),
            "analysis_selected": hasattr(st.session_state, 'selected_analysis_config'),
            "results_available": hasattr(st.session_state, 'current_analysis_result')
        }