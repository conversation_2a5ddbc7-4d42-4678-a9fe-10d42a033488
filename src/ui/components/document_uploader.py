"""
Document Uploader Component for AI Law Firm.

This component provides a reusable interface for uploading and processing
legal documents with progress tracking and validation.
"""

import streamlit as st
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
from dataclasses import dataclass
import tempfile
import os

from core.document.processor import DocumentProcessor
from core.document.types import DocumentProcessingRequest, DocumentProcessingResult
from core.config.settings import DocumentConfig
from utils.logging import get_logger


@dataclass
class UploadState:
    """State management for document uploads."""
    uploaded_file: Optional[Any] = None
    processed_files: set = None
    current_processing: Optional[str] = None
    processing_results: Dict[str, DocumentProcessingResult] = None

    def __post_init__(self):
        if self.processed_files is None:
            self.processed_files = set()
        if self.processing_results is None:
            self.processing_results = {}


class DocumentUploaderComponent:
    """
    Reusable component for document upload and processing.

    Features:
    - Multi-format document support
    - Progress tracking
    - File validation
    - Processing status management
    - Error handling and recovery
    """

    SUPPORTED_FORMATS = ['pdf', 'docx', 'txt', 'html', 'md']
    MAX_FILE_SIZE_MB = 50  # Maximum file size

    def __init__(self, config: DocumentConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.state = UploadState()
        self.processor = DocumentProcessor(config)

    def render(
        self,
        on_document_processed: Optional[Callable] = None,
        allow_multiple: bool = False
    ) -> UploadState:
        """
        Render the document uploader component.

        Args:
            on_document_processed: Callback function called when document is processed
            allow_multiple: Whether to allow multiple file uploads

        Returns:
            UploadState: Current upload state
        """
        st.header("📄 Document Upload")

        # File uploader
        uploaded_files = self._render_file_uploader(allow_multiple)

        if uploaded_files:
            # Process uploaded files
            for uploaded_file in uploaded_files:
                self._process_uploaded_file(uploaded_file, on_document_processed)

        # Display processing status
        self._render_processing_status()

        # Display processed documents
        self._render_processed_documents()

        return self.state

    def _render_file_uploader(self, allow_multiple: bool) -> List[Any]:
        """Render the file uploader interface."""
        # File type filter
        file_types = self.SUPPORTED_FORMATS.copy()

        # Add uppercase variants
        file_types.extend([fmt.upper() for fmt in self.SUPPORTED_FORMATS])

        uploaded_files = st.file_uploader(
            "Upload Legal Document(s)",
            type=file_types,
            accept_multiple_files=allow_multiple,
            help=self._get_upload_help_text(),
            key="document_uploader"
        )

        if isinstance(uploaded_files, list):
            return uploaded_files
        elif uploaded_files:
            return [uploaded_files]
        else:
            return []

    def _get_upload_help_text(self) -> str:
        """Generate help text for file uploader."""
        supported_formats = ", ".join([f".{fmt}" for fmt in self.SUPPORTED_FORMATS])
        return (
            f"Supported formats: {supported_formats}\n"
            f"Maximum file size: {self.MAX_FILE_SIZE_MB}MB\n"
            f"Recommended: PDF for legal documents"
        )

    def _process_uploaded_file(
        self,
        uploaded_file: Any,
        on_document_processed: Optional[Callable]
    ):
        """Process a single uploaded file."""
        file_name = uploaded_file.name

        # Check if file already processed
        if file_name in self.state.processed_files:
            st.success(f"✅ {file_name} already processed")
            return

        # Validate file
        if not self._validate_uploaded_file(uploaded_file):
            return

        # Process file
        with st.spinner(f"Processing {file_name}..."):
            try:
                self.state.current_processing = file_name

                # Create processing request
                request = self._create_processing_request(uploaded_file)

                # Process document
                result = self.processor.process_document(request)

                # Store result
                self.state.processing_results[file_name] = result
                self.state.processed_files.add(file_name)

                # Display result
                if result.success:
                    st.success(f"✅ {file_name} processed successfully")

                    # Show processing summary
                    self._display_processing_summary(result)

                    # Notify callback
                    if on_document_processed:
                        on_document_processed(result)
                else:
                    st.error(f"❌ Failed to process {file_name}")
                    self._display_processing_errors(result)

            except Exception as e:
                st.error(f"❌ Error processing {file_name}: {str(e)}")
                self.logger.error(f"Document processing error for {file_name}: {str(e)}")

            finally:
                self.state.current_processing = None

    def _validate_uploaded_file(self, uploaded_file: Any) -> bool:
        """Validate uploaded file."""
        file_name = uploaded_file.name
        file_size = len(uploaded_file.getvalue())

        # Check file size
        max_size_bytes = self.MAX_FILE_SIZE_MB * 1024 * 1024
        if file_size > max_size_bytes:
            st.error(
                f"❌ {file_name} is too large ({file_size / (1024*1024):.1f}MB). "
                f"Maximum size: {self.MAX_FILE_SIZE_MB}MB"
            )
            return False

        # Check file extension
        file_ext = Path(file_name).suffix.lower().lstrip('.')
        if file_ext not in self.SUPPORTED_FORMATS:
            st.error(
                f"❌ {file_name} has unsupported format .{file_ext}. "
                f"Supported: {', '.join(self.SUPPORTED_FORMATS)}"
            )
            return False

        return True

    def _create_processing_request(self, uploaded_file: Any) -> DocumentProcessingRequest:
        """Create document processing request."""
        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{uploaded_file.name}") as temp_file:
            temp_file.write(uploaded_file.getvalue())
            temp_file_path = temp_file.name

        # Determine document type
        file_ext = Path(uploaded_file.name).suffix.lower().lstrip('.')
        from core.document.types import DocumentType
        document_type_map = {
            'pdf': DocumentType.PDF,
            'docx': DocumentType.DOCX,
            'txt': DocumentType.TXT,
            'html': DocumentType.HTML,
            'htm': DocumentType.HTML,
            'md': DocumentType.MARKDOWN,
            'markdown': DocumentType.MARKDOWN
        }
        document_type = document_type_map.get(file_ext, DocumentType.PDF)

        return DocumentProcessingRequest(
            file_path=temp_file_path,
            document_type=document_type,
            processing_options={
                "extract_metadata": True,
                "generate_chunks": True,
                "validate_content": True
            }
        )

    def _display_processing_summary(self, result: DocumentProcessingResult):
        """Display processing summary."""
        if result.document:
            doc = result.document

            with st.expander(f"📊 Processing Summary: {doc.metadata.filename}"):
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("Pages", doc.metadata.page_count or "N/A")
                    st.metric("Words", f"{doc.metadata.word_count:,}")

                with col2:
                    st.metric("File Size", f"{doc.metadata.file_size_bytes / 1024:.1f} KB")
                    st.metric("Processing Time", f"{result.processing_time:.2f}s")

                with col3:
                    st.metric("Quality Score", f"{doc.quality_score:.2f}")
                    st.metric("Chunks Created", len(doc.chunks))

                # Show warnings if any
                if result.warnings:
                    st.warning("⚠️ Warnings:")
                    for warning in result.warnings:
                        st.write(f"• {warning}")

    def _display_processing_errors(self, result: DocumentProcessingResult):
        """Display processing errors."""
        with st.expander(f"❌ Processing Errors: {result.request.file_path}"):
            for error in result.errors:
                st.error(error)

            if result.warnings:
                st.warning("Warnings:")
                for warning in result.warnings:
                    st.write(f"• {warning}")

    def _render_processing_status(self):
        """Render current processing status."""
        if self.state.current_processing:
            st.info(f"🔄 Currently processing: {self.state.current_processing}")

        # Show processing queue if multiple files
        if len(self.state.processing_results) > 0:
            st.subheader("Processing History")

            # Create summary table
            status_data = []
            for file_name, result in self.state.processing_results.items():
                status_data.append({
                    "File": file_name,
                    "Status": "✅ Success" if result.success else "❌ Failed",
                    "Size": f"{result.document.metadata.file_size_bytes / 1024:.1f} KB" if result.document else "N/A",
                    "Processing Time": f"{result.processing_time:.2f}s"
                })

            if status_data:
                st.table(status_data)

    def _render_processed_documents(self):
        """Render list of processed documents."""
        if self.state.processed_files:
            st.subheader("Processed Documents")

            for file_name in sorted(self.state.processed_files):
                result = self.state.processing_results.get(file_name)
                if result and result.success and result.document:
                    doc = result.document

                    with st.expander(f"📄 {file_name}"):
                        col1, col2 = st.columns(2)

                        with col1:
                            st.write(f"**Type:** {doc.metadata.document_type.value}")
                            st.write(f"**Pages:** {doc.metadata.page_count or 'N/A'}")
                            st.write(f"**Words:** {doc.metadata.word_count:,}")

                        with col2:
                            st.write(f"**Quality Score:** {doc.quality_score:.2f}")
                            st.write(f"**Processed:** {doc.processed_at.strftime('%Y-%m-%d %H:%M') if doc.processed_at else 'N/A'}")

                            # Show document preview
                            if len(doc.content) > 500:
                                st.text_area(
                                    "Preview",
                                    doc.content[:500] + "...",
                                    height=100,
                                    disabled=True,
                                    key=f"preview_{file_name}"
                                )

    def get_processed_documents(self) -> List[DocumentProcessingResult]:
        """Get list of successfully processed documents."""
        return [
            result for result in self.state.processing_results.values()
            if result.success and result.document
        ]

    def clear_processed_files(self):
        """Clear all processed files."""
        self.state.processed_files.clear()
        self.state.processing_results.clear()
        st.success("✅ Cleared all processed documents")

    def export_processing_results(self) -> Dict[str, Any]:
        """Export processing results for external use."""
        return {
            "processed_files": list(self.state.processed_files),
            "processing_results": {
                file_name: {
                    "success": result.success,
                    "processing_time": result.processing_time,
                    "document_id": result.document.document_id if result.document else None,
                    "errors": result.errors,
                    "warnings": result.warnings
                }
                for file_name, result in self.state.processing_results.items()
            }
        }