"""
Results Display Component for AI Law Firm.

This component provides comprehensive display and interaction
with legal document analysis results from multiple AI agents.
"""

import streamlit as st
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime

from core.agents.base import AgentResponse
from utils.logging import get_logger


@dataclass
class AnalysisResult:
    """Represents a complete analysis result."""
    analysis_type: str
    query: str
    responses: List[AgentResponse]
    processing_time: float
    cost_estimate: float
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class ResultsDisplayComponent:
    """
    Component for displaying and interacting with analysis results.

    Features:
    - Multi-tab result organization
    - Interactive result exploration
    - Export capabilities
    - Result comparison
    - Performance metrics display
    """

    def __init__(self):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    def render(
        self,
        result: AnalysisResult,
        show_export: bool = True,
        show_metrics: bool = True
    ):
        """
        Render the analysis results display.

        Args:
            result: Analysis result to display
            show_export: Whether to show export options
            show_metrics: Whether to show performance metrics
        """
        # Header with analysis summary
        self._render_result_header(result)

        # Performance metrics
        if show_metrics:
            self._render_performance_metrics(result)

        # Main results in tabs
        self._render_tabbed_results(result)

        # Export options
        if show_export:
            self._render_export_options(result)

    def _render_result_header(self, result: AnalysisResult):
        """Render result header with summary information."""
        st.header("📊 Analysis Results")

        # Analysis summary
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            analysis_icons = {
                "contract_review": "📑",
                "legal_research": "🔍",
                "risk_assessment": "⚠️",
                "compliance_check": "✅",
                "custom_query": "💭"
            }
            icon = analysis_icons.get(result.analysis_type, "📄")
            st.metric("Analysis Type", f"{icon} {result.analysis_type.replace('_', ' ').title()}")

        with col2:
            st.metric("AI Agents", len(result.responses))

        with col3:
            st.metric("Processing Time", f"{result.processing_time:.1f}s")

        with col4:
            cost_display = f"${result.cost_estimate:.3f}" if result.cost_estimate > 0 else "Free"
            st.metric("Estimated Cost", cost_display)

        # Query display
        with st.expander("🔍 Analysis Query", expanded=False):
            st.write(result.query)

        # Timestamp
        st.caption(f"Analysis completed at {result.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}")

    def _render_performance_metrics(self, result: AnalysisResult):
        """Render performance metrics."""
        st.subheader("📈 Performance Metrics")

        # Create metrics display
        metrics_data = []

        for i, response in enumerate(result.responses):
            agent_name = getattr(response, 'model_used', f'Agent {i+1}')
            confidence = getattr(response, 'confidence_score', 0.0)
            processing_time = getattr(response, 'processing_time', 0.0)
            token_count = getattr(response, 'token_usage', {}).get('total_tokens', 0) if hasattr(response, 'token_usage') else 0

            metrics_data.append({
                "Agent": agent_name,
                "Confidence": f"{confidence:.2f}",
                "Response Time": f"{processing_time:.2f}s",
                "Tokens": f"{token_count:,}" if token_count > 0 else "N/A"
            })

        if metrics_data:
            st.table(metrics_data)

    def _render_tabbed_results(self, result: AnalysisResult):
        """Render results in organized tabs."""
        st.subheader("📋 Detailed Results")

        # Create tabs for different views
        tab_names = ["Summary", "Detailed Analysis", "Key Points", "Recommendations"]
        tabs = st.tabs(tab_names)

        with tabs[0]:  # Summary
            self._render_summary_tab(result)

        with tabs[1]:  # Detailed Analysis
            self._render_detailed_tab(result)

        with tabs[2]:  # Key Points
            self._render_key_points_tab(result)

        with tabs[3]:  # Recommendations
            self._render_recommendations_tab(result)

    def _render_summary_tab(self, result: AnalysisResult):
        """Render summary tab with high-level overview."""
        st.markdown("### Executive Summary")

        # Generate summary from all responses
        summary_points = []

        for i, response in enumerate(result.responses):
            agent_name = getattr(response, 'model_used', f'Agent {i+1}')
            content_preview = response.content[:300] + "..." if len(response.content) > 300 else response.content

            summary_points.append(f"**{agent_name}:** {content_preview}")

        for point in summary_points:
            st.markdown(f"• {point}")

        # Overall confidence
        avg_confidence = sum(
            getattr(r, 'confidence_score', 0.0) for r in result.responses
        ) / len(result.responses) if result.responses else 0.0

        confidence_color = "🟢" if avg_confidence > 0.8 else "🟡" if avg_confidence > 0.6 else "🔴"
        st.metric("Overall Confidence", f"{confidence_color} {avg_confidence:.2f}")

    def _render_detailed_tab(self, result: AnalysisResult):
        """Render detailed analysis tab."""
        st.markdown("### Detailed Analysis by Agent")

        for i, response in enumerate(result.responses):
            agent_name = getattr(response, 'model_used', f'Agent {i+1}')
            confidence = getattr(response, 'confidence_score', 0.0)

            with st.expander(f"🤖 {agent_name} (Confidence: {confidence:.2f})", expanded=(i == 0)):
                # Agent metadata
                col1, col2, col3 = st.columns(3)

                with col1:
                    processing_time = getattr(response, 'processing_time', 0.0)
                    st.metric("Response Time", f"{processing_time:.2f}s")

                with col2:
                    if hasattr(response, 'token_usage') and response.token_usage:
                        tokens = response.token_usage.get('total_tokens', 0)
                        st.metric("Tokens Used", f"{tokens:,}")
                    else:
                        st.metric("Tokens Used", "N/A")

                with col3:
                    if hasattr(response, 'token_usage') and response.token_usage:
                        cost = response.token_usage.get('estimated_cost', 0.0)
                        st.metric("Cost", f"${cost:.4f}")
                    else:
                        st.metric("Cost", "N/A")

                # Main content
                st.markdown("**Analysis:**")
                st.markdown(response.content)

                # Additional metadata
                if hasattr(response, 'metadata') and response.metadata:
                    with st.expander("📊 Additional Information"):
                        st.json(response.metadata)

    def _render_key_points_tab(self, result: AnalysisResult):
        """Render key points extracted from all responses."""
        st.markdown("### Key Points & Insights")

        all_key_points = []

        # Extract key points from each response
        for response in result.responses:
            points = self._extract_key_points(response.content)
            all_key_points.extend(points)

        # Remove duplicates and display
        unique_points = list(set(all_key_points))

        if unique_points:
            for i, point in enumerate(unique_points, 1):
                st.markdown(f"{i}. {point}")
        else:
            st.info("No specific key points were identified in the analysis.")

    def _render_recommendations_tab(self, result: AnalysisResult):
        """Render recommendations extracted from all responses."""
        st.markdown("### Recommendations & Next Steps")

        all_recommendations = []

        # Extract recommendations from each response
        for response in result.responses:
            recs = self._extract_recommendations(response.content)
            all_recommendations.extend(recs)

        # Categorize and display recommendations
        if all_recommendations:
            # Group by priority (simple heuristic)
            high_priority = [r for r in all_recommendations if any(word in r.lower() for word in ['critical', 'urgent', 'immediately', 'high priority'])]
            medium_priority = [r for r in all_recommendations if r not in high_priority and any(word in r.lower() for word in ['consider', 'recommend', 'suggest', 'should'])]
            low_priority = [r for r in all_recommendations if r not in high_priority and r not in medium_priority]

            if high_priority:
                st.markdown("#### 🔥 High Priority")
                for rec in high_priority:
                    st.error(f"• {rec}")

            if medium_priority:
                st.markdown("#### ⚠️ Medium Priority")
                for rec in medium_priority:
                    st.warning(f"• {rec}")

            if low_priority:
                st.markdown("#### ℹ️ General Recommendations")
                for rec in low_priority:
                    st.info(f"• {rec}")
        else:
            st.info("No specific recommendations were identified in the analysis.")

    def _render_export_options(self, result: AnalysisResult):
        """Render export options for results."""
        st.subheader("📤 Export Results")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📄 Export as PDF", use_container_width=True):
                self._export_as_pdf(result)

        with col2:
            if st.button("📝 Export as Text", use_container_width=True):
                self._export_as_text(result)

        with col3:
            if st.button("📊 Export as JSON", use_container_width=True):
                self._export_as_json(result)

    def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points from analysis content."""
        points = []

        # Look for bullet points, numbered lists, and key phrases
        lines = content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check for bullet points or numbered items
            if (line.startswith(('•', '-', '*')) or
                (line[0].isdigit() and line[1:3] in ['. ', ') ']) or
                any(phrase in line.lower() for phrase in [
                    'key point', 'important', 'notably', 'significantly',
                    'critical', 'essential', 'primary', 'main'
                ])):
                # Clean up the point
                if line.startswith(('•', '-', '*')):
                    point = line[1:].strip()
                elif line[0].isdigit() and line[1:3] in ['. ', ') ']:
                    point = line[2:].strip()
                else:
                    point = line.strip()

                if len(point) > 10:  # Avoid very short points
                    points.append(point)

        return points[:10]  # Limit to top 10 points

    def _extract_recommendations(self, content: str) -> List[str]:
        """Extract recommendations from analysis content."""
        recommendations = []

        # Look for recommendation patterns
        lines = content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check for recommendation indicators
            if any(word in line.lower() for word in [
                'recommend', 'suggest', 'advise', 'consider',
                'should', 'would recommend', 'advisable', 'proposed'
            ]):
                recommendations.append(line)

        return recommendations[:8]  # Limit to top 8 recommendations

    def _export_as_pdf(self, result: AnalysisResult):
        """Export results as PDF (placeholder implementation)."""
        st.info("📄 PDF export functionality would be implemented here")
        # In a real implementation, this would use a PDF generation library

    def _export_as_text(self, result: AnalysisResult):
        """Export results as text file."""
        # Combine all results into text format
        text_content = f"""
LEGAL DOCUMENT ANALYSIS REPORT
Generated: {result.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}

ANALYSIS TYPE: {result.analysis_type.replace('_', ' ').title()}
PROCESSING TIME: {result.processing_time:.2f} seconds
ESTIMATED COST: ${result.cost_estimate:.3f}

ORIGINAL QUERY:
{result.query}

{'='*50}

"""

        for i, response in enumerate(result.responses):
            agent_name = getattr(response, 'model_used', f'Agent {i+1}')
            text_content += f"""
AGENT {i+1}: {agent_name}
{'-'*30}
{response.content}

"""

        # Download button
        st.download_button(
            label="📝 Download Text Report",
            data=text_content,
            file_name=f"legal_analysis_{result.timestamp.strftime('%Y%m%d_%H%M%S')}.txt",
            mime="text/plain",
            use_container_width=True
        )

    def _export_as_json(self, result: AnalysisResult):
        """Export results as JSON."""
        # Convert result to JSON-serializable format
        json_data = {
            "analysis_type": result.analysis_type,
            "query": result.query,
            "timestamp": result.timestamp.isoformat(),
            "processing_time": result.processing_time,
            "cost_estimate": result.cost_estimate,
            "responses": []
        }

        for i, response in enumerate(result.responses):
            response_data = {
                "agent_id": i + 1,
                "model_used": getattr(response, 'model_used', f'Agent {i+1}'),
                "content": response.content,
                "confidence_score": getattr(response, 'confidence_score', 0.0),
                "processing_time": getattr(response, 'processing_time', 0.0),
                "metadata": getattr(response, 'metadata', {}),
                "sources": getattr(response, 'sources', []),
                "token_usage": getattr(response, 'token_usage', {}) if hasattr(response, 'token_usage') else {}
            }
            json_data["responses"].append(response_data)

        import json
        json_string = json.dumps(json_data, indent=2, default=str)

        # Download button
        st.download_button(
            label="📊 Download JSON Data",
            data=json_string,
            file_name=f"legal_analysis_{result.timestamp.strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json",
            use_container_width=True
        )

    def compare_results(self, results: List[AnalysisResult]):
        """Compare multiple analysis results."""
        if len(results) < 2:
            st.warning("Need at least 2 results to compare")
            return

        st.header("🔄 Results Comparison")

        # Comparison table
        comparison_data = []
        for result in results:
            avg_confidence = sum(
                getattr(r, 'confidence_score', 0.0) for r in result.responses
            ) / len(result.responses) if result.responses else 0.0

            comparison_data.append({
                "Analysis Type": result.analysis_type.replace('_', ' ').title(),
                "Query": result.query[:50] + "..." if len(result.query) > 50 else result.query,
                "Agents": len(result.responses),
                "Avg Confidence": f"{avg_confidence:.2f}",
                "Processing Time": f"{result.processing_time:.1f}s",
                "Cost": f"${result.cost_estimate:.3f}",
                "Timestamp": result.timestamp.strftime('%H:%M:%S')
            })

        st.table(comparison_data)

        # Side-by-side comparison
        if len(results) == 2:
            col1, col2 = st.columns(2)

            with col1:
                st.subheader(f"Analysis 1: {results[0].analysis_type.replace('_', ' ').title()}")
                st.write(results[0].query)

            with col2:
                st.subheader(f"Analysis 2: {results[1].analysis_type.replace('_', ' ').title()}")
                st.write(results[1].query)