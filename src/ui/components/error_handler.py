"""
Error Handler Component for AI Law Firm.

This component provides comprehensive error handling and user-friendly
error display throughout the application.
"""

import streamlit as st
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import traceback

from utils.exceptions import AILawFirmError, ErrorContext
from utils.logging import get_logger


@dataclass
class ErrorInfo:
    """Structured error information."""
    error_type: str
    message: str
    details: Optional[str] = None
    context: Optional[ErrorContext] = None
    timestamp: datetime = None
    user_action: Optional[str] = None
    recovery_suggestion: Optional[str] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class ErrorHandlerComponent:
    """
    Component for handling and displaying errors in the UI.

    Features:
    - User-friendly error messages
    - Error categorization and prioritization
    - Recovery suggestions
    - Error reporting and logging
    - Graceful degradation
    """

    def __init__(self):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.error_history: list[ErrorInfo] = []

    def handle_error(
        self,
        error: Exception,
        context: Optional[ErrorContext] = None,
        show_ui: bool = True,
        log_error: bool = True
    ) -> ErrorInfo:
        """
        Handle an error and optionally display it in the UI.

        Args:
            error: The exception that occurred
            context: Additional context about where the error occurred
            show_ui: Whether to display the error in the UI
            log_error: Whether to log the error

        Returns:
            ErrorInfo: Structured error information
        """
        # Create error info
        error_info = self._create_error_info(error, context)

        # Log the error
        if log_error:
            self._log_error(error_info)

        # Store in history
        self.error_history.append(error_info)

        # Display in UI
        if show_ui:
            self._display_error(error_info)

        return error_info

    def _create_error_info(self, error: Exception, context: Optional[ErrorContext]) -> ErrorInfo:
        """Create structured error information."""
        error_type = type(error).__name__

        # Get user-friendly message
        user_message = self._get_user_friendly_message(error, error_type)

        # Get recovery suggestion
        recovery_suggestion = self._get_recovery_suggestion(error_type, error)

        # Get user action
        user_action = self._get_user_action(error_type)

        # Get error details (for debugging)
        details = self._get_error_details(error)

        return ErrorInfo(
            error_type=error_type,
            message=user_message,
            details=details,
            context=context,
            user_action=user_action,
            recovery_suggestion=recovery_suggestion
        )

    def _get_user_friendly_message(self, error: Exception, error_type: str) -> str:
        """Get user-friendly error message."""
        # Custom messages for known error types
        error_messages = {
            "AIProviderError": "There was an issue with the AI service. Please check your API configuration.",
            "RateLimitError": "The AI service is currently busy. Please wait a moment and try again.",
            "TokenLimitError": "The document is too long for the current AI model. Please try a shorter document.",
            "ValidationError": "The provided information could not be validated. Please check your input.",
            "ConnectionError": "Unable to connect to the required service. Please check your internet connection.",
            "TimeoutError": "The operation timed out. Please try again.",
            "FileNotFoundError": "The requested file could not be found.",
            "PermissionError": "You don't have permission to access this resource.",
            "ValueError": "Invalid input provided. Please check your data and try again.",
            "KeyError": "Required information is missing. Please provide all necessary details.",
        }

        # Return custom message or generic fallback
        return error_messages.get(error_type, f"An unexpected error occurred: {str(error)}")

    def _get_recovery_suggestion(self, error_type: str, error: Exception) -> Optional[str]:
        """Get recovery suggestion based on error type."""
        suggestions = {
            "AIProviderError": "Check your API key and ensure the service is available.",
            "RateLimitError": "Wait a few minutes before trying again, or consider upgrading your API plan.",
            "TokenLimitError": "Try breaking your document into smaller sections or use a different AI model.",
            "ValidationError": "Review the input requirements and ensure all fields are correctly filled.",
            "ConnectionError": "Check your internet connection and try refreshing the page.",
            "TimeoutError": "The operation may take longer than expected. Please try again.",
            "FileNotFoundError": "Ensure the file exists and the path is correct.",
            "PermissionError": "Contact your administrator for the required permissions.",
        }

        return suggestions.get(error_type)

    def _get_user_action(self, error_type: str) -> Optional[str]:
        """Get suggested user action."""
        actions = {
            "AIProviderError": "Reconfigure API settings",
            "RateLimitError": "Wait and retry",
            "TokenLimitError": "Use shorter content",
            "ValidationError": "Correct input data",
            "ConnectionError": "Check connection",
            "TimeoutError": "Retry operation",
            "FileNotFoundError": "Select different file",
            "PermissionError": "Request permissions",
        }

        return actions.get(error_type, "Contact support")

    def _get_error_details(self, error: Exception) -> str:
        """Get detailed error information for debugging."""
        return "".join(traceback.format_exception(type(error), error, error.__traceback__))

    def _log_error(self, error_info: ErrorInfo):
        """Log error with appropriate level."""
        log_data = {
            "error_type": error_info.error_type,
            "message": error_info.message,
            "timestamp": error_info.timestamp.isoformat(),
            "context": error_info.context.__dict__ if error_info.context else None
        }

        # Log based on error severity
        if error_info.error_type in ["AIProviderError", "ConnectionError", "PermissionError"]:
            self.logger.error("Critical error occurred", extra=log_data)
        else:
            self.logger.warning("Error occurred", extra=log_data)

    def _display_error(self, error_info: ErrorInfo):
        """Display error in the Streamlit UI."""
        # Choose display method based on error type
        if error_info.error_type in ["AIProviderError", "RateLimitError", "TokenLimitError"]:
            self._display_warning_error(error_info)
        elif error_info.error_type in ["ValidationError", "ValueError"]:
            self._display_info_error(error_info)
        else:
            self._display_critical_error(error_info)

    def _display_critical_error(self, error_info: ErrorInfo):
        """Display critical error with full details."""
        with st.error(f"🚨 **{error_info.error_type}**"):
            st.write(error_info.message)

            if error_info.recovery_suggestion:
                st.write(f"**💡 Suggestion:** {error_info.recovery_suggestion}")

            if error_info.user_action:
                st.write(f"**🔧 Action:** {error_info.user_action}")

            # Show details in expandable section
            with st.expander("🔍 Technical Details"):
                if error_info.details:
                    st.code(error_info.details, language="text")
                if error_info.context:
                    st.json(error_info.context.__dict__)

    def _display_warning_error(self, error_info: ErrorInfo):
        """Display warning-level error."""
        with st.warning(f"⚠️ **{error_info.error_type}**"):
            st.write(error_info.message)

            if error_info.recovery_suggestion:
                st.write(f"**💡 Try:** {error_info.recovery_suggestion}")

    def _display_info_error(self, error_info: ErrorInfo):
        """Display info-level error."""
        with st.info(f"ℹ️ **{error_info.error_type}**"):
            st.write(error_info.message)

            if error_info.recovery_suggestion:
                st.write(f"**💡 Tip:** {error_info.recovery_suggestion}")

    def display_error_history(self, max_entries: int = 10):
        """Display recent error history."""
        if not self.error_history:
            st.info("✅ No recent errors")
            return

        st.subheader("📋 Recent Error History")

        # Show last N errors
        recent_errors = self.error_history[-max_entries:]

        for i, error_info in enumerate(reversed(recent_errors), 1):
            with st.expander(f"Error {i}: {error_info.error_type} - {error_info.timestamp.strftime('%H:%M:%S')}", expanded=False):
                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**Type:** {error_info.error_type}")
                    st.write(f"**Time:** {error_info.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

                with col2:
                    if error_info.user_action:
                        st.write(f"**Action:** {error_info.user_action}")
                    if error_info.context:
                        st.write(f"**Component:** {error_info.context.component}")

                st.write(f"**Message:** {error_info.message}")

                if error_info.recovery_suggestion:
                    st.info(f"**Recovery:** {error_info.recovery_suggestion}")

    def clear_error_history(self):
        """Clear the error history."""
        self.error_history.clear()
        st.success("✅ Error history cleared")

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of errors."""
        if not self.error_history:
            return {"total_errors": 0, "error_types": {}, "most_recent": None}

        error_types = {}
        for error in self.error_history:
            error_types[error.error_type] = error_types.get(error.error_type, 0) + 1

        return {
            "total_errors": len(self.error_history),
            "error_types": error_types,
            "most_recent": self.error_history[-1] if self.error_history else None,
            "time_range": {
                "oldest": self.error_history[0].timestamp if self.error_history else None,
                "newest": self.error_history[-1].timestamp if self.error_history else None
            }
        }

    def export_error_report(self) -> str:
        """Export error history as text report."""
        if not self.error_history:
            return "No errors to report"

        report_lines = [
            "AI LAW FIRM ERROR REPORT",
            "=" * 50,
            f"Generated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}",
            f"Total Errors: {len(self.error_history)}",
            "",
            "ERROR SUMMARY:",
            "-" * 20
        ]

        # Error type summary
        error_types = {}
        for error in self.error_history:
            error_types[error.error_type] = error_types.get(error.error_type, 0) + 1

        for error_type, count in error_types.items():
            report_lines.append(f"{error_type}: {count}")

        report_lines.extend([
            "",
            "DETAILED ERROR LOG:",
            "-" * 20
        ])

        # Individual errors
        for i, error in enumerate(self.error_history, 1):
            report_lines.extend([
                f"Error {i}:",
                f"  Type: {error.error_type}",
                f"  Time: {error.timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
                f"  Message: {error.message}",
            ])

            if error.recovery_suggestion:
                report_lines.append(f"  Recovery: {error.recovery_suggestion}")

            if error.context:
                report_lines.append(f"  Component: {error.context.component}")
                report_lines.append(f"  Operation: {error.context.operation}")

            report_lines.append("")

        return "\n".join(report_lines)

    def create_error_boundary(self, operation_name: str):
        """
        Create an error boundary context manager.

        Usage:
            with error_handler.create_error_boundary("document_processing"):
                # Your code here
                process_document()
        """
        from contextlib import contextmanager

        @contextmanager
        def error_boundary():
            try:
                yield
            except Exception as e:
                context = ErrorContext(
                    operation=operation_name,
                    component="ui_error_boundary"
                )
                self.handle_error(e, context)

        return error_boundary()