"""
Analysis Selector Component for AI Law Firm.

This component provides an interface for selecting and configuring
different types of legal document analysis with intelligent agent routing.
"""

import streamlit as st
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum

from core.agents.base import Agent<PERSON><PERSON>
from utils.logging import get_logger


class AnalysisType(str, Enum):
    """Available analysis types."""
    CONTRACT_REVIEW = "contract_review"
    LEGAL_RESEARCH = "legal_research"
    RISK_ASSESSMENT = "risk_assessment"
    COMPLIANCE_CHECK = "compliance_check"
    CUSTOM_QUERY = "custom_query"


@dataclass
class AnalysisConfig:
    """Configuration for a specific analysis type."""
    analysis_type: AnalysisType
    display_name: str
    description: str
    icon: str
    agents: List[str]
    default_query: Optional[str] = None
    priority: int = 1
    estimated_cost: str = "Low"
    estimated_time: str = "2-5 min"


class AnalysisSelectorComponent:
    """
    Component for selecting and configuring legal document analysis.

    Features:
    - Analysis type selection with descriptions
    - Agent assignment visualization
    - Cost and time estimates
    - Custom query support
    - Intelligent defaults based on document type
    """

    def __init__(self):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.analysis_configs = self._initialize_analysis_configs()

    def render(
        self,
        document_type: Optional[str] = None,
        on_analysis_selected: Optional[Callable] = None
    ) -> Optional[AnalysisConfig]:
        """
        Render the analysis selector component.

        Args:
            document_type: Type of document being analyzed (for intelligent defaults)
            on_analysis_selected: Callback function called when analysis is selected

        Returns:
            AnalysisConfig: Selected analysis configuration, or None
        """
        st.header("🔍 Analysis Configuration")

        # Analysis type selection
        selected_type = self._render_analysis_type_selector(document_type)

        if selected_type:
            config = self.analysis_configs[selected_type]

            # Display analysis details
            self._render_analysis_details(config)

            # Custom query input (if applicable)
            custom_query = self._render_custom_query_input(config)

            # Analysis execution
            if self._render_analysis_execution(config, custom_query):
                if on_analysis_selected:
                    # Add custom query to config if provided
                    if custom_query:
                        config.default_query = custom_query
                    on_analysis_selected(config)
                return config

        return None

    def _initialize_analysis_configs(self) -> Dict[AnalysisType, AnalysisConfig]:
        """Initialize analysis type configurations."""
        return {
            AnalysisType.CONTRACT_REVIEW: AnalysisConfig(
                analysis_type=AnalysisType.CONTRACT_REVIEW,
                display_name="Contract Review",
                description="Comprehensive contract analysis focusing on terms, obligations, and potential issues",
                icon="📑",
                agents=["Legal Researcher", "Contract Analyst"],
                default_query="Review this contract and identify key terms, obligations, and potential issues. Focus on payment terms, termination clauses, liability limitations, and any unusual provisions.",
                priority=1,
                estimated_cost="Medium",
                estimated_time="3-7 min"
            ),

            AnalysisType.LEGAL_RESEARCH: AnalysisConfig(
                analysis_type=AnalysisType.LEGAL_RESEARCH,
                display_name="Legal Research",
                description="Research relevant cases, statutes, and legal precedents related to the document",
                icon="🔍",
                agents=["Legal Researcher"],
                default_query="Research relevant cases, statutes, and legal precedents related to this document. Identify key legal principles, recent developments, and potential implications.",
                priority=2,
                estimated_cost="High",
                estimated_time="5-10 min"
            ),

            AnalysisType.RISK_ASSESSMENT: AnalysisConfig(
                analysis_type=AnalysisType.RISK_ASSESSMENT,
                display_name="Risk Assessment",
                description="Analyze potential legal risks, liabilities, and compliance issues",
                icon="⚠️",
                agents=["Contract Analyst", "Legal Strategist"],
                default_query="Conduct a comprehensive risk assessment of this document. Identify potential legal risks, compliance issues, financial exposures, and strategic concerns. Provide mitigation recommendations.",
                priority=1,
                estimated_cost="High",
                estimated_time="4-8 min"
            ),

            AnalysisType.COMPLIANCE_CHECK: AnalysisConfig(
                analysis_type=AnalysisType.COMPLIANCE_CHECK,
                display_name="Compliance Check",
                description="Check document compliance with applicable laws and regulations",
                icon="✅",
                agents=["Legal Researcher", "Contract Analyst", "Legal Strategist"],
                default_query="Check this document for compliance with applicable laws and regulations. Review for GDPR, consumer protection, anti-discrimination, and industry-specific compliance requirements.",
                priority=2,
                estimated_cost="Medium",
                estimated_time="3-6 min"
            ),

            AnalysisType.CUSTOM_QUERY: AnalysisConfig(
                analysis_type=AnalysisType.CUSTOM_QUERY,
                display_name="Custom Analysis",
                description="Perform custom analysis using all available AI agents",
                icon="💭",
                agents=["Legal Researcher", "Contract Analyst", "Legal Strategist"],
                default_query=None,
                priority=3,
                estimated_cost="Variable",
                estimated_time="Variable"
            )
        }

    def _render_analysis_type_selector(self, document_type: Optional[str]) -> Optional[AnalysisType]:
        """Render analysis type selection interface."""
        st.subheader("Select Analysis Type")

        # Get recommended analysis types based on document type
        recommended_types = self._get_recommended_analysis_types(document_type)

        # Create options with recommendations
        options = []
        for analysis_type in AnalysisType:
            config = self.analysis_configs[analysis_type]
            option_text = f"{config.icon} {config.display_name}"

            if analysis_type in recommended_types:
                option_text += " ⭐ (Recommended)"

            options.append(option_text)

        # Analysis type selection
        selected_option = st.selectbox(
            "Choose analysis type:",
            options=options,
            help="Select the type of analysis you want to perform on your document"
        )

        if selected_option:
            # Extract analysis type from selected option
            for analysis_type in AnalysisType:
                if analysis_type.value in selected_option.lower().replace(" ", "_"):
                    return analysis_type

        return None

    def _get_recommended_analysis_types(self, document_type: Optional[str]) -> List[AnalysisType]:
        """Get recommended analysis types based on document type."""
        if not document_type:
            return [AnalysisType.CONTRACT_REVIEW, AnalysisType.RISK_ASSESSMENT]

        doc_type_lower = document_type.lower()

        if "contract" in doc_type_lower or "agreement" in doc_type_lower:
            return [AnalysisType.CONTRACT_REVIEW, AnalysisType.RISK_ASSESSMENT, AnalysisType.COMPLIANCE_CHECK]
        elif "brief" in doc_type_lower or "complaint" in doc_type_lower:
            return [AnalysisType.LEGAL_RESEARCH, AnalysisType.RISK_ASSESSMENT]
        elif "regulation" in doc_type_lower or "statute" in doc_type_lower:
            return [AnalysisType.LEGAL_RESEARCH, AnalysisType.COMPLIANCE_CHECK]
        else:
            return [AnalysisType.CONTRACT_REVIEW, AnalysisType.LEGAL_RESEARCH]

    def _render_analysis_details(self, config: AnalysisConfig):
        """Render detailed information about selected analysis."""
        st.subheader(f"{config.icon} {config.display_name}")

        # Description
        st.info(f"📋 **Description**: {config.description}")

        # Agent information
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("🤖 AI Agents", len(config.agents))
            agent_list = "\n".join([f"• {agent}" for agent in config.agents])
            st.caption(agent_list)

        with col2:
            st.metric("💰 Estimated Cost", config.estimated_cost)

        with col3:
            st.metric("⏱️ Estimated Time", config.estimated_time)

        # Default query preview (if applicable)
        if config.default_query:
            with st.expander("🔍 Default Analysis Query"):
                st.write(config.default_query)

        # Analysis capabilities
        with st.expander("🎯 Analysis Capabilities"):
            capabilities = self._get_analysis_capabilities(config.analysis_type)
            for capability in capabilities:
                st.write(f"• {capability}")

    def _get_analysis_capabilities(self, analysis_type: AnalysisType) -> List[str]:
        """Get list of capabilities for analysis type."""
        capabilities_map = {
            AnalysisType.CONTRACT_REVIEW: [
                "Key terms identification",
                "Obligation analysis",
                "Risk clause detection",
                "Fairness assessment",
                "Negotiation points"
            ],
            AnalysisType.LEGAL_RESEARCH: [
                "Case law research",
                "Statutory interpretation",
                "Precedent analysis",
                "Legal citation validation",
                "Current law updates"
            ],
            AnalysisType.RISK_ASSESSMENT: [
                "Financial risk analysis",
                "Legal liability assessment",
                "Compliance risk evaluation",
                "Mitigation strategies",
                "Priority risk ranking"
            ],
            AnalysisType.COMPLIANCE_CHECK: [
                "Regulatory compliance",
                "Industry standard review",
                "Legal requirement verification",
                "Gap analysis",
                "Remediation recommendations"
            ],
            AnalysisType.CUSTOM_QUERY: [
                "Flexible analysis scope",
                "Multi-agent collaboration",
                "Custom focus areas",
                "Tailored recommendations",
                "Comprehensive coverage"
            ]
        }

        return capabilities_map.get(analysis_type, [])

    def _render_custom_query_input(self, config: AnalysisConfig) -> Optional[str]:
        """Render custom query input if applicable."""
        if config.analysis_type == AnalysisType.CUSTOM_QUERY:
            st.subheader("💭 Custom Query")

            custom_query = st.text_area(
                "Enter your specific analysis query:",
                height=100,
                placeholder="Describe what specific aspects of the document you want analyzed...",
                help="Provide detailed instructions for the AI agents to follow during analysis"
            )

            if custom_query:
                # Query validation
                if len(custom_query.strip()) < 10:
                    st.warning("⚠️ Please provide a more detailed query (at least 10 characters)")
                elif len(custom_query.strip()) > 2000:
                    st.error("❌ Query is too long (maximum 2000 characters)")
                    return None
                else:
                    st.success("✅ Query looks good!")

            return custom_query if custom_query and len(custom_query.strip()) >= 10 else None

        return None

    def _render_analysis_execution(self, config: AnalysisConfig, custom_query: Optional[str]) -> bool:
        """Render analysis execution controls."""
        st.subheader("🚀 Execute Analysis")

        # Validation checks
        can_execute = self._validate_analysis_execution(config, custom_query)

        if can_execute:
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                execute_button = st.button(
                    f"🔍 Start {config.display_name}",
                    type="primary",
                    use_container_width=True
                )

            with col2:
                if st.button("📋 Preview Query", use_container_width=True):
                    self._show_query_preview(config, custom_query)

            with col3:
                if st.button("ℹ️ Help", use_container_width=True):
                    self._show_analysis_help(config)

            return execute_button
        else:
            st.warning("⚠️ Please complete the required information above to proceed")
            return False

    def _validate_analysis_execution(self, config: AnalysisConfig, custom_query: Optional[str]) -> bool:
        """Validate that analysis can be executed."""
        if config.analysis_type == AnalysisType.CUSTOM_QUERY:
            return custom_query is not None and len(custom_query.strip()) >= 10
        else:
            return True

    def _show_query_preview(self, config: AnalysisConfig, custom_query: Optional[str]):
        """Show preview of the analysis query."""
        with st.expander("🔍 Query Preview", expanded=True):
            if config.analysis_type == AnalysisType.CUSTOM_QUERY and custom_query:
                st.write("**Custom Query:**")
                st.info(custom_query)
            elif config.default_query:
                st.write("**Analysis Query:**")
                st.info(config.default_query)

            st.write("**Assigned Agents:**")
            for agent in config.agents:
                st.write(f"• {agent}")

    def _show_analysis_help(self, config: AnalysisConfig):
        """Show help information for the analysis type."""
        with st.expander("ℹ️ Analysis Help", expanded=True):
            help_text = self._get_analysis_help_text(config.analysis_type)
            st.markdown(help_text)

    def _get_analysis_help_text(self, analysis_type: AnalysisType) -> str:
        """Get help text for analysis type."""
        help_texts = {
            AnalysisType.CONTRACT_REVIEW: """
            **Contract Review** analyzes legal agreements for:
            - Key terms and conditions
            - Potential risks and liabilities
            - Unfavorable clauses
            - Negotiation opportunities
            - Compliance with best practices

            **Tip**: Focus on payment terms, termination clauses, and liability limitations.
            """,

            AnalysisType.LEGAL_RESEARCH: """
            **Legal Research** provides:
            - Relevant case law and precedents
            - Statutory interpretation
            - Current legal developments
            - Citation validation
            - Practical implications

            **Tip**: Be specific about the legal areas or jurisdictions of interest.
            """,

            AnalysisType.RISK_ASSESSMENT: """
            **Risk Assessment** evaluates:
            - Financial and legal exposures
            - Compliance risks
            - Operational impacts
            - Mitigation strategies
            - Priority ranking of risks

            **Tip**: Consider both immediate and long-term risk factors.
            """,

            AnalysisType.COMPLIANCE_CHECK: """
            **Compliance Check** verifies:
            - Regulatory requirements
            - Industry standards
            - Legal obligations
            - Documentation requirements
            - Remediation needs

            **Tip**: Specify relevant regulations or industry standards.
            """,

            AnalysisType.CUSTOM_QUERY: """
            **Custom Analysis** allows:
            - Flexible analysis scope
            - Specific focus areas
            - Tailored questions
            - Multi-agent collaboration
            - Comprehensive coverage

            **Tip**: Provide detailed, specific instructions for best results.
            """
        }

        return help_texts.get(analysis_type, "Select an analysis type for help information.")

    def get_available_analysis_types(self) -> List[AnalysisConfig]:
        """Get list of all available analysis configurations."""
        return list(self.analysis_configs.values())

    def get_analysis_config(self, analysis_type: AnalysisType) -> Optional[AnalysisConfig]:
        """Get configuration for specific analysis type."""
        return self.analysis_configs.get(analysis_type)