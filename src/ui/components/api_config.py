"""
API Configuration Component for AI Law Firm.

This component provides a reusable interface for configuring API credentials
and external service connections in the Streamlit sidebar.
"""

import streamlit as st
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass

from core.config.settings import AppConfig, AIProvider
from utils.logging import get_logger


@dataclass
class APIConfigState:
    """State management for API configuration."""
    openai_api_key: Optional[str] = None
    qdrant_api_key: Optional[str] = None
    qdrant_url: Optional[str] = None
    ollama_base_url: Optional[str] = None
    is_configured: bool = False
    connection_status: Dict[str, bool] = None

    def __post_init__(self):
        if self.connection_status is None:
            self.connection_status = {
                "openai": False,
                "qdrant": False,
                "ollama": False
            }


class APIConfigurationComponent:
    """
    Reusable component for API configuration management.

    Features:
    - Dynamic provider selection
    - Secure credential input
    - Connection testing
    - Configuration persistence
    - Real-time validation
    """

    def __init__(self, config: AppConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.state = APIConfigState()

    def render(self, on_config_change: Optional[Callable] = None) -> APIConfigState:
        """
        Render the API configuration component in the sidebar.

        Args:
            on_config_change: Callback function called when configuration changes

        Returns:
            APIConfigState: Current configuration state
        """
        with st.sidebar:
            st.header("🔑 API Configuration")

            # Provider selection
            selected_provider = self._render_provider_selection()

            # Provider-specific configuration
            if selected_provider == AIProvider.OPENAI:
                self._render_openai_config()
            elif selected_provider == AIProvider.OLLAMA:
                self._render_ollama_config()

            # Vector database configuration
            self._render_vector_db_config()

            # Connection testing
            self._render_connection_testing()

            # Configuration summary
            self._render_config_summary()

            # Notify parent of configuration changes
            if on_config_change and self._has_config_changed():
                on_config_change(self.state)

        return self.state

    def _render_provider_selection(self) -> AIProvider:
        """Render AI provider selection dropdown."""
        provider_options = {
            AIProvider.OPENAI: "OpenAI (Cloud)",
            AIProvider.OLLAMA: "Ollama (Local)"
        }

        selected_provider = st.selectbox(
            "AI Provider",
            options=list(provider_options.keys()),
            format_func=lambda x: provider_options[x],
            help="Select your preferred AI provider"
        )

        return selected_provider

    def _render_openai_config(self):
        """Render OpenAI-specific configuration."""
        st.subheader("OpenAI Configuration")

        # API Key input
        openai_key = st.text_input(
            "OpenAI API Key",
            type="password",
            value=self.state.openai_api_key or "",
            help="Enter your OpenAI API key",
            key="openai_api_key"
        )

        if openai_key:
            self.state.openai_api_key = openai_key
            self.state.connection_status["openai"] = self._test_openai_connection(openai_key)

        # Model selection
        if self.state.connection_status["openai"]:
            model_options = ["gpt-5-nano", "gpt-5-mini", "gpt-5", "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"]
            selected_model = st.selectbox(
                "Model",
                options=model_options,
                index=model_options.index("gpt-4o-mini"),  # Default to cost-effective model
                help="Select the OpenAI model to use"
            )

            # Cost information
            self._display_cost_info(selected_model)

    def _render_ollama_config(self):
        """Render Ollama-specific configuration."""
        st.subheader("Ollama Configuration")

        # Base URL input
        ollama_url = st.text_input(
            "Ollama Base URL",
            value=self.state.ollama_base_url or "http://localhost:11434",
            help="Enter your Ollama server URL",
            key="ollama_base_url"
        )

        if ollama_url:
            self.state.ollama_base_url = ollama_url
            self.state.connection_status["ollama"] = self._test_ollama_connection(ollama_url)

        # Model selection
        if self.state.connection_status["ollama"]:
            try:
                # This would integrate with Ollama to get available models
                model_options = ["llama3.2:3b", "llama3.1:8b", "codellama:7b", "mistral:7b"]
                selected_model = st.selectbox(
                    "Model",
                    options=model_options,
                    index=0,  # Default to llama3.2:3b
                    help="Select the Ollama model to use"
                )

                st.info("💡 **Local AI**: No API costs, fully private")
            except Exception as e:
                st.warning(f"Could not load model list: {str(e)}")

    def _render_vector_db_config(self):
        """Render vector database configuration."""
        st.subheader("Vector Database")

        # Qdrant configuration
        qdrant_url = st.text_input(
            "Qdrant URL",
            value=self.state.qdrant_url or "",
            help="Enter your Qdrant instance URL",
            key="qdrant_url"
        )

        qdrant_key = st.text_input(
            "Qdrant API Key",
            type="password",
            value=self.state.qdrant_api_key or "",
            help="Enter your Qdrant API key",
            key="qdrant_api_key"
        )

        if qdrant_url and qdrant_key:
            self.state.qdrant_url = qdrant_url
            self.state.qdrant_api_key = qdrant_key
            self.state.connection_status["qdrant"] = self._test_qdrant_connection(qdrant_url, qdrant_key)

    def _render_connection_testing(self):
        """Render connection testing interface."""
        st.subheader("Connection Status")

        # Test connections button
        if st.button("🔍 Test Connections", key="test_connections"):
            with st.spinner("Testing connections..."):
                self._test_all_connections()

        # Display connection status
        self._display_connection_status()

    def _render_config_summary(self):
        """Render configuration summary."""
        st.subheader("Configuration Summary")

        # Overall status
        is_fully_configured = all(self.state.connection_status.values())

        if is_fully_configured:
            st.success("✅ All services configured and connected")
            self.state.is_configured = True
        else:
            st.warning("⚠️ Some services need configuration")
            self.state.is_configured = False

        # Detailed status
        status_details = []
        if self.state.connection_status["openai"]:
            status_details.append("OpenAI: ✅ Connected")
        elif self.state.openai_api_key:
            status_details.append("OpenAI: ❌ Connection failed")

        if self.state.connection_status["ollama"]:
            status_details.append("Ollama: ✅ Connected")
        elif self.state.ollama_base_url:
            status_details.append("Ollama: ❌ Connection failed")

        if self.state.connection_status["qdrant"]:
            status_details.append("Qdrant: ✅ Connected")
        elif self.state.qdrant_url and self.state.qdrant_api_key:
            status_details.append("Qdrant: ❌ Connection failed")

        if status_details:
            with st.expander("Connection Details"):
                for detail in status_details:
                    st.write(detail)

    def _test_openai_connection(self, api_key: str) -> bool:
        """Test OpenAI API connection."""
        try:
            # Import here to avoid circular imports
            from infrastructure.ai_providers.openai_provider import OpenAIProvider
            from core.config.settings import OpenAIConfig

            config = OpenAIConfig(api_key=api_key)
            provider = OpenAIProvider(config)

            # Quick health check
            result = provider.health_check()
            return result.get("healthy", False)

        except Exception as e:
            self.logger.error(f"OpenAI connection test failed: {str(e)}")
            return False

    def _test_ollama_connection(self, base_url: str) -> bool:
        """Test Ollama connection."""
        try:
            # Import here to avoid circular imports
            from infrastructure.ai_providers.ollama_provider import OllamaProvider
            from core.config.settings import OllamaConfig

            config = OllamaConfig(base_url=base_url, model="llama3.2:3b")
            provider = OllamaProvider(config)

            # Quick health check
            result = provider.health_check()
            return result.get("healthy", False)

        except Exception as e:
            self.logger.error(f"Ollama connection test failed: {str(e)}")
            return False

    def _test_qdrant_connection(self, url: str, api_key: str) -> bool:
        """Test Qdrant connection."""
        try:
            # This would integrate with Qdrant client
            # For now, return True if both URL and key are provided
            return bool(url and api_key)
        except Exception as e:
            self.logger.error(f"Qdrant connection test failed: {str(e)}")
            return False

    def _test_all_connections(self):
        """Test all configured connections."""
        if self.state.openai_api_key:
            self.state.connection_status["openai"] = self._test_openai_connection(self.state.openai_api_key)

        if self.state.ollama_base_url:
            self.state.connection_status["ollama"] = self._test_ollama_connection(self.state.ollama_base_url)

        if self.state.qdrant_url and self.state.qdrant_api_key:
            self.state.connection_status["qdrant"] = self._test_qdrant_connection(
                self.state.qdrant_url, self.state.qdrant_api_key
            )

    def _display_connection_status(self):
        """Display connection status indicators."""
        col1, col2, col3 = st.columns(3)

        with col1:
            if self.state.connection_status["openai"]:
                st.success("OpenAI: ✅")
            elif self.state.openai_api_key:
                st.error("OpenAI: ❌")
            else:
                st.info("OpenAI: Not configured")

        with col2:
            if self.state.connection_status["ollama"]:
                st.success("Ollama: ✅")
            elif self.state.ollama_base_url:
                st.error("Ollama: ❌")
            else:
                st.info("Ollama: Not configured")

        with col3:
            if self.state.connection_status["qdrant"]:
                st.success("Qdrant: ✅")
            elif self.state.qdrant_url and self.state.qdrant_api_key:
                st.error("Qdrant: ❌")
            else:
                st.info("Qdrant: Not configured")

    def _display_cost_info(self, model: str):
        """Display cost information for selected model."""
        cost_info = {
            "gpt-5-nano": {"input": 0.00005, "output": 0.0002},
            "gpt-5-mini": {"input": 0.0001, "output": 0.0004},
            "gpt-5": {"input": 0.002, "output": 0.008},
            "gpt-4o": {"input": 0.005, "output": 0.015},
            "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015}
        }

        if model in cost_info:
            costs = cost_info[model]
            st.info(
                f"💰 **Cost**: ${costs['input']}/1K input, "
                f"${costs['output']}/1K output tokens"
            )

    def _has_config_changed(self) -> bool:
        """Check if configuration has changed."""
        # Simple implementation - in practice, you'd compare with previous state
        return True

    def get_config_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary."""
        return {
            "openai_api_key": self.state.openai_api_key,
            "qdrant_api_key": self.state.qdrant_api_key,
            "qdrant_url": self.state.qdrant_url,
            "ollama_base_url": self.state.ollama_base_url,
            "is_configured": self.state.is_configured,
            "connection_status": self.state.connection_status.copy()
        }

    def load_from_session_state(self):
        """Load configuration from Streamlit session state."""
        if hasattr(st.session_state, 'openai_api_key'):
            self.state.openai_api_key = st.session_state.openai_api_key
        if hasattr(st.session_state, 'qdrant_api_key'):
            self.state.qdrant_api_key = st.session_state.qdrant_api_key
        if hasattr(st.session_state, 'qdrant_url'):
            self.state.qdrant_url = st.session_state.qdrant_url
        if hasattr(st.session_state, 'ollama_base_url'):
            self.state.ollama_base_url = st.session_state.ollama_base_url

    def save_to_session_state(self):
        """Save configuration to Streamlit session state."""
        st.session_state.openai_api_key = self.state.openai_api_key
        st.session_state.qdrant_api_key = self.state.qdrant_api_key
        st.session_state.qdrant_url = self.state.qdrant_url
        st.session_state.ollama_base_url = self.state.ollama_base_url