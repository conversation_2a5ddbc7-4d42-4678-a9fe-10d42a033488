"""
UI Components module for AI Law Firm.

This module provides modular, reusable Streamlit components for the legal
document analysis system, following the separation of concerns principle.
"""

from .components.api_config import APIConfigurationComponent
from .components.document_uploader import DocumentUploaderComponent
from .components.analysis_selector import AnalysisSelectorComponent
from .components.results_display import ResultsDisplayComponent
from .components.error_handler import ErrorHandlerComponent
from .pages.main import MainPage
from .app import LegalAnalysisApp

__all__ = [
    "APIConfigurationComponent",
    "DocumentUploaderComponent",
    "AnalysisSelectorComponent",
    "ResultsDisplayComponent",
    "ErrorHandlerComponent",
    "MainPage",
    "LegalAnalysisApp",
]