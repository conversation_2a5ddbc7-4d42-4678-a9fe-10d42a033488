"""
Main Application for AI Law Firm.

This module provides the refactored Streamlit application that uses
modular components and service orchestration for a clean, maintainable UI.
"""

import streamlit as st
from typing import Optional
from datetime import datetime

from core.config.settings import AppConfig, get_config
from core.services.orchestration_service import OrchestrationService, AnalysisRequest, AnalysisResult
from ui.components.api_config import APIConfigurationComponent
from ui.components.document_uploader import DocumentUploaderComponent
from ui.components.analysis_selector import AnalysisSelectorComponent
from ui.components.results_display import ResultsDisplayComponent
from ui.components.error_handler import ErrorHandlerComponent
from utils.logging import get_logger


class LegalAnalysisApp:
    """
    Main Streamlit application for legal document analysis.

    Features:
    - Modular component architecture
    - Service orchestration
    - Comprehensive error handling
    - Session state management
    - Performance monitoring
    """

    def __init__(self, config: Optional[AppConfig] = None):
        self.config = config or get_config()
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # Initialize components
        self.api_config = APIConfigurationComponent(self.config)
        self.document_uploader = DocumentUploaderComponent(self.config.document)
        self.analysis_selector = AnalysisSelectorComponent()
        self.results_display = ResultsDisplayComponent()
        self.error_handler = ErrorHandlerComponent()

        # Initialize service
        self.orchestration_service = OrchestrationService(self.config)

        # Session state keys
        self.SESSION_KEYS = {
            'app_initialized': False,
            'current_analysis': None,
            'analysis_history': [],
            'last_error': None
        }

    def run(self):
        """Run the main application."""
        st.set_page_config(
            page_title="AI Legal Analysis Platform",
            layout="wide",
            initial_sidebar_state="expanded"
        )

        # Initialize session state
        self._initialize_session_state()

        # Initialize application
        if not st.session_state.app_initialized:
            self._initialize_app()

        # Main application layout
        self._render_main_layout()

    def _initialize_session_state(self):
        """Initialize session state variables."""
        for key, default_value in self.SESSION_KEYS.items():
            if key not in st.session_state:
                st.session_state[key] = default_value

    def _initialize_app(self):
        """Initialize the application and its components."""
        try:
            with st.spinner("Initializing AI Legal Analysis Platform..."):
                # Initialize orchestration service
                success = self.orchestration_service.initialize()
                if not success:
                    raise Exception("Failed to initialize orchestration service")

                st.session_state.app_initialized = True
                st.success("✅ Platform initialized successfully!")

        except Exception as e:
            self.error_handler.handle_error(e, show_ui=True)
            st.error("❌ Failed to initialize platform. Please refresh and try again.")

    def _render_main_layout(self):
        """Render the main application layout."""
        # Header
        self._render_header()

        # Sidebar
        self._render_sidebar()

        # Main content area
        self._render_main_content()

        # Footer
        self._render_footer()

    def _render_header(self):
        """Render application header."""
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.title("⚖️ AI Legal Analysis Platform")
            st.caption("Powered by Multi-Agent AI Technology")

        with col2:
            # Service status indicator
            status = self._get_service_status()
            if status["healthy"]:
                st.success("🟢 System Online")
            else:
                st.error("🔴 System Issues")

        with col3:
            # Current time
            st.caption(f"Last updated: {datetime.utcnow().strftime('%H:%M:%S UTC')}")

    def _render_sidebar(self):
        """Render sidebar with configuration and controls."""
        with st.sidebar:
            st.header("🔧 Configuration")

            # API Configuration
            api_state = self.api_config.render()

            # Service status
            self._render_service_status()

            # Application controls
            self._render_app_controls()

    def _render_service_status(self):
        """Render service status information."""
        st.subheader("📊 Service Status")

        try:
            service_status = self.orchestration_service.get_service_status()

            col1, col2 = st.columns(2)

            with col1:
                st.metric("Active Requests", service_status.get("active_requests", 0))
                st.metric("Total Analyses", service_status.get("total_analyses", 0))

            with col2:
                avg_time = service_status.get("average_processing_time", 0)
                st.metric("Avg. Time", f"{avg_time:.1f}s")
                total_cost = service_status.get("total_cost", 0)
                st.metric("Total Cost", f"${total_cost:.2f}")

            # Pipeline status
            pipeline_status = service_status.get("pipeline_status", {})
            if pipeline_status:
                with st.expander("Pipeline Details"):
                    idle_agents = pipeline_status.get("agent_pool_status", {}).get("idle_agents", 0)
                    busy_agents = pipeline_status.get("agent_pool_status", {}).get("busy_agents", 0)
                    st.metric("Idle Agents", idle_agents)
                    st.metric("Busy Agents", busy_agents)

        except Exception as e:
            st.error(f"Status check failed: {str(e)}")

    def _render_app_controls(self):
        """Render application control buttons."""
        st.subheader("🎛️ Controls")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 Refresh Status", use_container_width=True):
                st.rerun()

        with col2:
            if st.button("🗑️ Clear History", use_container_width=True):
                self._clear_analysis_history()

        # Error history toggle
        if st.checkbox("Show Error History"):
            self.error_handler.display_error_history()

    def _render_main_content(self):
        """Render main content area."""
        # Check if services are configured
        if not self._are_services_configured():
            self._render_configuration_required()
            return

        # Document upload section
        st.header("📄 Document Analysis")

        # Document uploader
        upload_state = self.document_uploader.render(
            on_document_processed=self._on_document_processed
        )

        # Analysis section (only if documents are available)
        if upload_state.processed_files:
            self._render_analysis_section(upload_state)
        else:
            st.info("👆 Please upload a legal document to begin analysis")

        # Results section
        if st.session_state.current_analysis:
            self._render_results_section()

    def _are_services_configured(self) -> bool:
        """Check if required services are configured."""
        # This would check API keys, database connections, etc.
        # For now, return True if app is initialized
        return st.session_state.app_initialized

    def _render_configuration_required(self):
        """Render configuration required message."""
        st.warning("⚠️ **Configuration Required**")
        st.info("Please configure your API credentials in the sidebar to begin using the platform.")
        st.markdown("""
        **Required Configuration:**
        - 🤖 AI Provider (OpenAI or Ollama)
        - 🗄️ Vector Database (Qdrant)
        - 🔑 API Keys and credentials
        """)

    def _render_analysis_section(self, upload_state):
        """Render analysis configuration section."""
        st.subheader("🎯 Analysis Configuration")

        # Get the most recent processed document
        if upload_state.processing_results:
            latest_result = list(upload_state.processing_results.values())[-1]
            document_type = latest_result.document.metadata.document_type if latest_result.document else None
        else:
            document_type = None

        # Analysis selector
        analysis_config = self.analysis_selector.render(
            document_type=document_type,
            on_analysis_selected=self._on_analysis_selected
        )

        # Analysis execution status
        if st.session_state.current_analysis:
            self._render_analysis_progress()

    def _render_results_section(self):
        """Render analysis results section."""
        st.header("📊 Analysis Results")

        current_analysis = st.session_state.current_analysis

        # Convert to AnalysisResult format for display
        from ui.components.results_display import AnalysisResult as DisplayResult

        display_result = DisplayResult(
            analysis_type=current_analysis.request.analysis_type,
            query=current_analysis.request.custom_query or f"Analysis of {current_analysis.request.analysis_type.replace('_', ' ').title()}",
            responses=current_analysis.responses,
            processing_time=current_analysis.processing_time,
            cost_estimate=current_analysis.cost_estimate
        )

        # Display results
        self.results_display.render(display_result)

    def _render_analysis_progress(self):
        """Render analysis progress indicator."""
        st.subheader("🔄 Analysis in Progress")

        # Progress bar
        progress_bar = st.progress(0.0, text="Initializing analysis...")

        # Status messages
        status_container = st.empty()

        # Simulate progress (in real implementation, this would be driven by actual progress)
        import time
        for i in range(101):
            progress_bar.progress(i / 100.0)
            if i < 30:
                status_container.text("🔍 Analyzing document content...")
            elif i < 70:
                status_container.text("🤖 Consulting AI agents...")
            elif i < 90:
                status_container.text("📝 Synthesizing results...")
            else:
                status_container.text("✅ Finalizing analysis...")

            time.sleep(0.02)  # Simulate processing time

        progress_bar.empty()
        status_container.empty()

    def _render_footer(self):
        """Render application footer."""
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.caption("© 2024 AI Legal Analysis Platform")

        with col2:
            st.caption("Built with Streamlit & Multi-Agent AI")

        with col3:
            # Version info
            st.caption("Version 2.0.0 (Refactored)")

    def _on_document_processed(self, result):
        """Handle document processing completion."""
        try:
            self.logger.info(f"Document processed: {result.document.metadata.filename if result.document else 'Unknown'}")

            # Store processing result for analysis
            st.session_state.last_processed_document = result

            st.success("✅ Document processed and ready for analysis!")

        except Exception as e:
            self.error_handler.handle_error(e)

    def _on_analysis_selected(self, analysis_config):
        """Handle analysis configuration selection."""
        try:
            self.logger.info(f"Analysis selected: {analysis_config.analysis_type}")

            # Get the processed document
            if not hasattr(st.session_state, 'last_processed_document'):
                st.error("No document available for analysis")
                return

            document_result = st.session_state.last_processed_document

            # Create analysis request
            request = AnalysisRequest(
                document_content=document_result.document.content,
                analysis_type=analysis_config.analysis_type,
                custom_query=analysis_config.default_query,
                document_type=document_result.document.metadata.document_type,
                user_id="web_user",  # In real implementation, get from session
                session_id="web_session"
            )

            # Execute analysis
            with st.spinner("🔍 Analyzing document..."):
                result = self.orchestration_service.analyze_document(request)

            # Store result
            st.session_state.current_analysis = result

            # Add to history
            st.session_state.analysis_history.append(result)

            if result.success:
                st.success("✅ Analysis completed successfully!")
            else:
                st.error(f"❌ Analysis failed: {result.error_message}")

        except Exception as e:
            self.error_handler.handle_error(e)

    def _clear_analysis_history(self):
        """Clear analysis history."""
        st.session_state.analysis_history.clear()
        st.session_state.current_analysis = None
        if hasattr(st.session_state, 'last_processed_document'):
            delattr(st.session_state, 'last_processed_document')

        self.orchestration_service.clear_analysis_history()
        st.success("✅ Analysis history cleared")

    def _get_service_status(self) -> dict:
        """Get overall service health status."""
        try:
            health_check = self.orchestration_service.health_check()
            return health_check
        except Exception:
            return {"healthy": False, "error": "Health check failed"}