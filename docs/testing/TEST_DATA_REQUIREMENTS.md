# 🧪 Test Data Requirements & Management

## Overview

This document outlines the test data requirements, management strategies, and generation procedures for the AI Law Firm testing framework.

## Test Data Categories

### 1. Sample Legal Documents

#### Contract Documents
**Purpose**: Test contract analysis and review functionality
**Requirements**:
- **Format**: PDF, DOCX
- **Size Range**: 1-100 pages
- **Types**:
  - Employment agreements
  - Service contracts
  - NDA agreements
  - Partnership agreements
  - Lease agreements
  - Vendor contracts
- **Content Requirements**:
  - Standard legal clauses (termination, payment, liability)
  - Varied complexity levels
  - Both well-drafted and problematic contracts
  - Different jurisdictions (US states, international)

#### Legal Briefs and Memoranda
**Purpose**: Test legal research and citation extraction
**Requirements**:
- **Format**: PDF, DOCX
- **Size Range**: 5-50 pages
- **Types**:
  - Appellate briefs
  - Legal memoranda
  - Motion papers
  - Court filings
- **Content Requirements**:
  - Case citations (US Supreme Court, Federal, State)
  - Statutory references
  - Legal arguments and analysis
  - Factual recitations

#### Court Documents
**Purpose**: Test case law analysis and precedent identification
**Requirements**:
- **Format**: PDF
- **Size Range**: 10-200 pages
- **Types**:
  - Court opinions
  - Settlement agreements
  - Pleadings
  - Judicial decisions
- **Content Requirements**:
  - Complete case citations
  - Legal reasoning
  - Judicial analysis
  - Case outcomes

### 2. Mock API Responses

#### AI Provider Responses
**Purpose**: Simulate AI model responses for testing
**Requirements**:
```json
{
  "contract_analysis": {
    "response": "Detailed analysis text",
    "key_terms": ["Array of identified terms"],
    "risks": ["Array of identified risks"],
    "recommendations": ["Array of recommendations"],
    "confidence_score": 0.85
  },
  "legal_research": {
    "response": "Research findings text",
    "citations": ["Case citations"],
    "statutes": ["Statutory references"],
    "precedents": ["Relevant precedents"],
    "relevance_score": 0.92
  },
  "risk_assessment": {
    "overall_risk": "Medium",
    "risk_factors": [
      {
        "type": "Legal",
        "severity": "High",
        "probability": 0.7,
        "description": "Detailed risk description"
      }
    ],
    "mitigation_strategies": ["Strategy 1", "Strategy 2"]
  }
}
```

#### External Service Responses
**Purpose**: Mock external API calls (search, databases)
**Requirements**:
- DuckDuckGo search results
- Legal database queries
- Regulatory API responses
- Error response scenarios

### 3. Configuration Test Data

#### Environment Configurations
**Purpose**: Test different deployment configurations
**Requirements**:
```yaml
# development.yaml
environment: development
debug: true
log_level: DEBUG
ai_provider: openai
model: gpt-4o
vector_db: qdrant_local

# production.yaml
environment: production
debug: false
log_level: WARNING
ai_provider: openai
model: gpt-4o
vector_db: qdrant_cloud

# local.yaml
environment: local
debug: true
log_level: INFO
ai_provider: ollama
model: llama3.2:3b
vector_db: qdrant_local
```

#### API Credentials
**Purpose**: Test authentication and authorization
**Requirements**:
- Valid test API keys
- Expired/invalid keys
- Rate-limited scenarios
- Permission variations

## Test Data Generation Strategy

### Automated Generation Scripts

#### Sample Document Generation
```python
#!/usr/bin/env python3
"""
Generate synthetic legal documents for testing.
"""
import random
from typing import Dict, List
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

class LegalDocumentGenerator:
    """Generate synthetic legal documents."""

    def __init__(self):
        self.contract_templates = {
            "employment": self._get_employment_template(),
            "service": self._get_service_template(),
            "nda": self._get_nda_template()
        }

    def generate_contract(self, contract_type: str, pages: int = 5) -> bytes:
        """Generate a contract PDF."""
        if contract_type not in self.contract_templates:
            raise ValueError(f"Unknown contract type: {contract_type}")

        template = self.contract_templates[contract_type]

        # Create PDF
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)

        # Add content
        self._add_contract_content(c, template, pages)

        c.save()
        buffer.seek(0)
        return buffer.getvalue()

    def _add_contract_content(self, canvas, template: Dict, pages: int):
        """Add contract content to PDF."""
        y_position = 750

        for page in range(pages):
            if page > 0:
                canvas.showPage()
                y_position = 750

            # Add header
            canvas.setFont("Helvetica-Bold", 14)
            canvas.drawString(50, y_position, template["title"])
            y_position -= 30

            # Add clauses
            canvas.setFont("Helvetica", 11)
            for clause in template["clauses"][:10]:  # Limit clauses per page
                if y_position < 50:
                    break

                canvas.drawString(50, y_position, f"{clause['number']}. {clause['title']}")
                y_position -= 15

                # Add clause content
                content_lines = self._wrap_text(clause["content"], 100)
                for line in content_lines[:3]:  # Limit content lines
                    if y_position < 50:
                        break
                    canvas.drawString(70, y_position, line)
                    y_position -= 12

    def _get_employment_template(self) -> Dict:
        """Get employment contract template."""
        return {
            "title": "EMPLOYMENT AGREEMENT",
            "clauses": [
                {
                    "number": "1",
                    "title": "Position and Duties",
                    "content": "Employee shall serve as [Position] and shall perform duties commensurate with such position as reasonably assigned by the Company."
                },
                {
                    "number": "2",
                    "title": "Compensation",
                    "content": "Employee shall receive an annual base salary of $[Amount], payable in accordance with the Company's standard payroll practices."
                },
                # ... more clauses
            ]
        }
```

#### Mock Response Generation
```python
#!/usr/bin/env python3
"""
Generate mock AI responses for testing.
"""
import json
import random
from typing import Dict, Any

class MockResponseGenerator:
    """Generate realistic mock responses for testing."""

    def __init__(self):
        self.response_templates = self._load_templates()

    def generate_contract_analysis(self, contract_type: str) -> Dict[str, Any]:
        """Generate mock contract analysis response."""
        template = self.response_templates["contract_analysis"]

        return {
            "response": template["response"].format(contract_type=contract_type),
            "key_terms": random.sample(template["key_terms"], random.randint(3, 6)),
            "risks": random.sample(template["risks"], random.randint(2, 4)),
            "recommendations": random.sample(template["recommendations"], random.randint(2, 5)),
            "confidence_score": round(random.uniform(0.7, 0.95), 2)
        }

    def generate_legal_research(self, query_topic: str) -> Dict[str, Any]:
        """Generate mock legal research response."""
        template = self.response_templates["legal_research"]

        return {
            "response": template["response"].format(topic=query_topic),
            "citations": random.sample(template["citations"], random.randint(2, 5)),
            "statutes": random.sample(template["statutes"], random.randint(1, 3)),
            "precedents": random.sample(template["precedents"], random.randint(2, 4)),
            "relevance_score": round(random.uniform(0.8, 0.98), 2)
        }

    def _load_templates(self) -> Dict:
        """Load response templates."""
        return {
            "contract_analysis": {
                "response": "This {contract_type} contract contains standard terms including termination clauses, payment terms, and liability limitations. Key findings indicate proper legal structure with some areas for potential improvement.",
                "key_terms": [
                    "Termination: 30-day notice required",
                    "Payment: Due within 15 days of invoice",
                    "Liability: Capped at contract value",
                    "Confidentiality: Standard NDA terms",
                    "Governing Law: [Jurisdiction] law applies",
                    "Dispute Resolution: Arbitration required"
                ],
                "risks": [
                    "Medium: Termination clause could be interpreted narrowly",
                    "Low: Standard payment terms with reasonable deadlines",
                    "High: Liability cap may be insufficient for some scenarios",
                    "Low: Confidentiality terms are comprehensive"
                ],
                "recommendations": [
                    "Consider adding a force majeure clause",
                    "Review liability cap in light of contract value",
                    "Add specific performance remedies",
                    "Include data protection provisions",
                    "Consider adding an entire agreement clause"
                ]
            },
            "legal_research": {
                "response": "Research on {topic} reveals relevant case law and statutory authority. The analysis indicates established legal principles with some developing areas of law.",
                "citations": [
                    "Smith v. Jones, 123 U.S. 456 (2020)",
                    "Johnson v. Company, 789 F. Supp. 101 (2019)",
                    "Williams v. Corporation, 456 F.3d 789 (5th Cir. 2018)",
                    "Brown v. Entity, 321 A.2d 654 (Del. 2017)",
                    "Davis v. Organization, 987 N.E.2d 321 (Ill. 2016)"
                ],
                "statutes": [
                    "42 U.S.C. § 1983: Civil Rights Actions",
                    "UCC § 2-207: Battle of the Forms",
                    "Cal. Civ. Code § 1688: Contract Termination"
                ],
                "precedents": [
                    "Established precedent for contract interpretation",
                    "Developing case law on digital contracts",
                    "Supreme Court guidance on arbitration clauses",
                    "Circuit court rulings on employment contracts"
                ]
            }
        }
```

### Manual Test Data Curation

#### Document Selection Criteria
1. **Diversity**: Various document types, lengths, and complexity levels
2. **Realism**: Actual legal documents with realistic content
3. **Edge Cases**: Documents with unusual clauses, formatting issues, or legal complexities
4. **Jurisdictional Coverage**: Documents from different US states and international jurisdictions
5. **Time Range**: Documents from different time periods to test temporal analysis

#### Quality Assurance
1. **Legal Review**: Have legal experts review sample documents for accuracy
2. **Anonymization**: Remove any sensitive or identifying information
3. **Standardization**: Ensure consistent formatting and metadata
4. **Version Control**: Track document versions and changes over time

## Test Data Storage and Management

### Directory Structure
```
tests/fixtures/
├── sample_documents/
│   ├── contracts/
│   │   ├── employment/
│   │   ├── service/
│   │   ├── nda/
│   │   └── lease/
│   ├── legal_briefs/
│   │   ├── appellate/
│   │   ├── memoranda/
│   │   └── motions/
│   └── court_documents/
│       ├── opinions/
│       ├── pleadings/
│       └── settlements/
├── mock_responses/
│   ├── ai_providers/
│   │   ├── openai/
│   │   └── ollama/
│   └── external_services/
│       ├── duckduckgo/
│       └── legal_databases/
├── configurations/
│   ├── environments/
│   └── credentials/
└── performance_data/
    ├── small_documents/
    ├── medium_documents/
    └── large_documents/
```

### Data Versioning
```python
# test_data_version.json
{
  "version": "1.0.0",
  "last_updated": "2024-01-15",
  "documents": {
    "contracts": {
      "count": 25,
      "types": ["employment", "service", "nda", "lease"],
      "avg_size": "15 pages"
    },
    "legal_briefs": {
      "count": 15,
      "types": ["appellate", "memoranda", "motions"],
      "avg_size": "25 pages"
    }
  },
  "checksums": {
    "sample_contract.pdf": "abc123...",
    "legal_brief.docx": "def456..."
  }
}
```

## Data Generation Pipeline

### Automated Pipeline
```python
#!/usr/bin/env python3
"""
Automated test data generation pipeline.
"""
import schedule
import time
from pathlib import Path
from generators.document_generator import LegalDocumentGenerator
from generators.response_generator import MockResponseGenerator

class TestDataPipeline:
    """Automated test data generation and maintenance."""

    def __init__(self, output_dir: Path):
        self.output_dir = output_dir
        self.doc_generator = LegalDocumentGenerator()
        self.response_generator = MockResponseGenerator()

    def generate_weekly_data(self):
        """Generate new test data weekly."""
        print("Generating weekly test data...")

        # Generate new contract samples
        self._generate_contract_samples()

        # Generate new mock responses
        self._generate_mock_responses()

        # Update performance test data
        self._update_performance_data()

        # Validate generated data
        self._validate_generated_data()

        print("Weekly test data generation complete.")

    def _generate_contract_samples(self):
        """Generate new contract samples."""
        contract_types = ["employment", "service", "nda", "partnership"]

        for contract_type in contract_types:
            # Generate 2-3 new samples per type
            for i in range(random.randint(2, 3)):
                pdf_content = self.doc_generator.generate_contract(
                    contract_type=contract_type,
                    pages=random.randint(3, 10)
                )

                filename = f"{contract_type}_sample_{i+1}.pdf"
                filepath = self.output_dir / "contracts" / filename

                with open(filepath, "wb") as f:
                    f.write(pdf_content)

    def _generate_mock_responses(self):
        """Generate new mock responses."""
        topics = ["contract_breach", "employment_law", "intellectual_property"]

        for topic in topics:
            # Generate contract analysis response
            contract_response = self.response_generator.generate_contract_analysis(topic)
            self._save_response(contract_response, f"contract_{topic}.json")

            # Generate legal research response
            research_response = self.response_generator.generate_legal_research(topic)
            self._save_response(research_response, f"research_{topic}.json")

    def _update_performance_data(self):
        """Update performance test data."""
        # Generate larger documents for performance testing
        for size in ["large", "xlarge"]:
            if size == "large":
                pages = random.randint(50, 100)
            else:
                pages = random.randint(100, 200)

            pdf_content = self.doc_generator.generate_contract(
                contract_type="comprehensive",
                pages=pages
            )

            filename = f"performance_{size}_contract.pdf"
            filepath = self.output_dir / "performance" / filename

            with open(filepath, "wb") as f:
                f.write(pdf_content)

    def _validate_generated_data(self):
        """Validate generated test data."""
        # Check file sizes
        # Verify PDF validity
        # Ensure content diversity
        pass

    def _save_response(self, response: Dict, filename: str):
        """Save mock response to file."""
        import json
        filepath = self.output_dir / "mock_responses" / filename

        with open(filepath, "w") as f:
            json.dump(response, f, indent=2)

def main():
    """Run the test data generation pipeline."""
    pipeline = TestDataPipeline(Path("tests/fixtures"))

    # Run immediately
    pipeline.generate_weekly_data()

    # Schedule weekly generation
    schedule.every().week.do(pipeline.generate_weekly_data)

    while True:
        schedule.run_pending()
        time.sleep(3600)  # Check every hour

if __name__ == "__main__":
    main()
```

## Data Privacy and Security

### Anonymization Requirements
1. **Personal Information**: Remove all PII from sample documents
2. **Company Names**: Replace with generic or fictional names
3. **Addresses**: Use placeholder addresses
4. **Contact Information**: Remove phone numbers, emails
5. **Financial Data**: Use rounded or fictional amounts

### Access Control
1. **Repository Access**: Restrict access to sensitive test data
2. **Encryption**: Encrypt sensitive mock credentials
3. **Audit Logging**: Track access to test data
4. **Retention Policy**: Define data retention periods

### Compliance
1. **Legal Compliance**: Ensure test data doesn't violate laws
2. **Ethical Use**: Use data ethically and responsibly
3. **Licensing**: Include appropriate licensing for sample documents

## Performance and Scalability

### Data Generation Performance
- **Target Generation Time**: < 30 minutes for full dataset
- **Storage Requirements**: < 1GB for complete test suite
- **Memory Usage**: < 2GB during generation
- **Parallel Processing**: Support concurrent data generation

### Test Execution Performance
- **Unit Test Suite**: < 5 minutes
- **Integration Tests**: < 15 minutes
- **Full Test Suite**: < 30 minutes
- **Performance Tests**: < 10 minutes

## Maintenance and Updates

### Regular Maintenance Tasks
1. **Weekly Data Refresh**: Generate new sample documents
2. **Monthly Review**: Review and update mock responses
3. **Quarterly Audit**: Audit test data for relevance and accuracy
4. **Annual Update**: Major update of test data and scenarios

### Quality Metrics
1. **Data Diversity**: Ensure broad coverage of scenarios
2. **Realism Score**: Rate how realistic test data is
3. **Coverage Effectiveness**: Measure how well data covers edge cases
4. **Maintenance Burden**: Track time spent maintaining test data

This comprehensive test data management strategy ensures the AI Law Firm has robust, realistic, and maintainable test data throughout its development lifecycle.