# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI-powered legal document analysis system that provides specialized legal team simulation using multiple AI agents. The project has two main implementations:

1. **Cloud-based version** (`legal_agent_team.py`) - Uses OpenAI GPT-4o with Qdrant Cloud
2. **Local version** (`local_ai_legal_agent_team/`) - Uses local Ollama models with local Qdrant

## Development Commands

### Running the Applications

**Cloud Version:**
```bash
streamlit run legal_agent_team.py
```

**Local Version:**
```bash
# Using the provided startup script (recommended)
cd local_ai_legal_agent_team
./start_legal_agent.sh

# Or manually
streamlit run local_ai_legal_agent_team/local_legal_agent.py --server.port 8080
```

### Dependencies Installation

```bash
# Root project dependencies
pip install -r requirements.txt

# Local version dependencies
pip install -r local_ai_legal_agent_team/requirements.txt
```

### Testing and Debugging

```bash
# Simple connection test
streamlit run local_ai_legal_agent_team/test_simple.py

# Debug legal agent components
streamlit run local_ai_legal_agent_team/debug_legal.py

# Basic HTTP server test
python local_ai_legal_agent_team/test_server.py
```

## Architecture Overview

### Agent System Architecture
The system implements a multi-agent legal team with specialized roles:

- **Legal Researcher**: Searches for legal cases and precedents using DuckDuckGo tools
- **Contract Analyst**: Specializes in contract review and clause analysis  
- **Legal Strategist**: Develops comprehensive legal strategies and recommendations
- **Team Lead**: Coordinates between agents and ensures comprehensive responses

### Core Components

**Knowledge Management:**
- Uses `PDFKnowledgeBase` from the Agno framework for document processing
- Implements document chunking for efficient retrieval
- Vector database storage for semantic search

**Vector Database:**
- Cloud version: Qdrant Cloud with OpenAI embeddings
- Local version: Local Qdrant instance with Ollama embeddings

**AI Models:**
- Cloud version: OpenAI GPT-4o (`gpt-4.1`) 
- Local version: Ollama Llama 3.2 3B model (`llama3.2:3b`)

### Key Technical Details

**Document Processing Pipeline:**
1. PDF upload via Streamlit interface
2. Document chunking (1000 chars, 200 overlap)  
3. Vector embedding generation
4. Storage in Qdrant collection
5. Agent initialization with knowledge base access

**Analysis Types:**
- Contract Review (Contract Analyst)
- Legal Research (Legal Researcher) 
- Risk Assessment (Contract Analyst + Legal Strategist)
- Compliance Check (All agents)
- Custom Query (All agents)

**Session Management:**
- Streamlit session state for API keys, vector DB, and agent team
- File processing tracking to prevent duplicate processing
- Dynamic agent initialization after document upload

## Infrastructure Requirements

**Local Version Prerequisites:**
- Docker (for local Qdrant instance)
- Ollama installation with Llama 3.2 3B model
- Qdrant Docker container running on port 6333

**Cloud Version Prerequisites:**
- OpenAI API key
- Qdrant Cloud instance (API key + URL)

## Configuration Notes

The local version automatically manages Qdrant through Docker and supports multiple port configurations (8080, 8090) for redundancy. The startup script handles process management and provides multiple access URLs for flexibility.

Both versions use the Agno framework for agent orchestration, knowledge base management, and vector database operations.