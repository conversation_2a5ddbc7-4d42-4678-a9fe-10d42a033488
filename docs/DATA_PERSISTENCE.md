# 🗄️ AI Law Firm - Data Persistence Architecture

## Overview

The AI Law Firm system implements a comprehensive multi-database architecture designed to handle the complex data persistence requirements of legal document analysis, user management, and system interactions.

## 🏗️ Architecture Overview

### Multi-Database Design

The system uses four specialized databases, each optimized for specific data types and access patterns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │    MongoDB      │    │     Redis       │    │    Qdrant       │
│                 │    │                 │    │                 │    │                 │
│ • User accounts │    │ • Document      │    │ • Sessions      │    │ • Vector        │
│ • Sessions      │    │   content       │    │ • Cache         │    │   embeddings    │
│ • API keys      │    │ • Analysis      │    │ • Rate limits   │    │ • Similarity    │
│ • Audit logs    │    │   results       │    │ • Performance   │    │   search        │
│ • Usage stats   │    │ • Metadata      │    │   metrics       │    │                 │
│ • Document      │    │                 │    │                 │    │                 │
│   metadata      │    │                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow Architecture

```
User Request → Orchestration Service → Data Persistence Service
                                      │
                                      ├── PostgreSQL Repository
                                      ├── MongoDB Repository
                                      ├── Redis Cache
                                      └── Qdrant Integration
```

## 🐘 PostgreSQL - Relational Data

### Purpose
Handles structured, relational data that requires ACID transactions, complex queries, and referential integrity.

### Key Tables

#### Users
```sql
CREATE TABLE users (
    user_id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE,
    full_name VARCHAR(255),
    hashed_password VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE
);
```

#### Analysis Sessions
```sql
CREATE TABLE analysis_sessions (
    session_id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(user_id),
    document_id UUID REFERENCES documents(document_id),
    analysis_type VARCHAR(50) NOT NULL,
    custom_query TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    total_cost DECIMAL(10,4) DEFAULT 0,
    error_message TEXT
);
```

#### Usage Statistics
```sql
CREATE TABLE usage_stats (
    stat_id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(user_id),
    date DATE NOT NULL,
    documents_processed INTEGER DEFAULT 0,
    analyses_performed INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    api_calls INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
);
```

### Features
- **ACID Transactions**: Ensures data consistency
- **Complex Queries**: JOINs, aggregations, analytics
- **Referential Integrity**: Foreign key constraints
- **Indexing**: Optimized for query performance
- **Audit Trail**: Complete change history

## 🍃 MongoDB - Unstructured Data

### Purpose
Stores document content, analysis results, and complex metadata that doesn't fit relational schemas.

### Collections

#### Documents
```javascript
{
  "_id": "doc_123456",
  "document_id": "uuid-here",
  "content": "Full document text content...",
  "chunks": ["chunk 1", "chunk 2", "chunk 3"],
  "metadata": {
    "document_type": "contract",
    "filename": "employment_agreement.pdf",
    "processing_status": "completed"
  },
  "processing_metadata": {
    "stored_at": "2024-01-15T10:30:00Z",
    "storage_version": "1.0"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Analysis Results
```javascript
{
  "_id": "result_123456",
  "result_id": "uuid-here",
  "session_id": "session_uuid",
  "agent_name": "Legal Researcher",
  "query": "Analyze this contract for potential risks",
  "response": "Comprehensive analysis result...",
  "sources": [
    {"type": "case_law", "reference": "Smith v. Jones"},
    {"type": "statute", "reference": "42 U.S.C. § 1983"}
  ],
  "metadata": {
    "confidence_score": 0.92,
    "processing_time": 2.3,
    "model_used": "gpt-4o"
  },
  "created_at": "2024-01-15T10:30:00Z"
}
```

### Features
- **Flexible Schema**: Handle varying document structures
- **Full-Text Search**: Text search across document content
- **GridFS**: Store large files and attachments
- **Aggregation Pipeline**: Complex data processing
- **Geospatial Queries**: Location-based legal data

## 🔴 Redis - Caching & Sessions

### Purpose
Provides high-performance caching, session management, and temporary data storage.

### Key Patterns

#### Sessions
```
Key: session:{session_id}
Value: {
  "session_id": "uuid",
  "user_id": "uuid",
  "data": {...},
  "created_at": "2024-01-15T10:30:00Z",
  "last_accessed": "2024-01-15T10:30:00Z"
}
TTL: 3600 seconds
```

#### Cache
```
Key: cache:document:{document_id}
Value: {
  "document_id": "uuid",
  "filename": "contract.pdf",
  "user_id": "uuid",
  "processing_status": "completed",
  "created_at": "2024-01-15T10:30:00Z"
}
TTL: 1800 seconds
```

#### Rate Limiting
```
Key: ratelimit:user:{user_id}
Value: Sorted Set of request timestamps
TTL: 3600 seconds
```

### Features
- **Sub-millisecond latency**: In-memory operations
- **TTL support**: Automatic expiration
- **Pub/Sub**: Real-time notifications
- **Atomic operations**: Thread-safe updates
- **Distributed locking**: Prevent race conditions

## 🎯 Qdrant - Vector Database

### Purpose
Handles vector embeddings for semantic search and document similarity matching.

### Collections

#### Legal Documents
```python
collection_name = "legal_documents"
vectors_config = {
    "content": models.VectorParams(
        size=1536,  # OpenAI embedding dimension
        distance=models.Distance.COSINE
    )
}

payload = {
    "document_id": "uuid",
    "user_id": "uuid",
    "filename": "contract.pdf",
    "document_type": "contract",
    "chunk_index": 0,
    "content": "This agreement is made between...",
    "metadata": {
        "page_number": 1,
        "section": "preamble"
    }
}
```

### Features
- **Vector similarity search**: Semantic document matching
- **Hybrid search**: Combine vector and keyword search
- **Filtering**: Metadata-based result filtering
- **Batch operations**: Efficient bulk processing
- **Real-time updates**: Live index updates

## 🐳 Docker Setup

### Port Mapping Strategy

To avoid conflicts with existing services, we use a dedicated port block:

```yaml
# docker/docker-compose.yml
services:
  postgres:
    ports:
      - "54320:5432"  # PostgreSQL

  mongodb:
    ports:
      - "27017:27017"  # MongoDB (standard port)

  redis:
    ports:
      - "63790:6379"   # Redis

  qdrant:
    ports:
      - "63330:6333"   # Qdrant gRPC
      - "63331:6334"   # Qdrant REST
```

### Environment Configuration

```bash
# docker/.env
POSTGRES_DB=ai_law_firm
POSTGRES_USER=ai_law_user
POSTGRES_PASSWORD=ai_law_password_2024
POSTGRES_HOST=localhost
POSTGRES_PORT=54320

MONGO_HOST=localhost
MONGO_PORT=27017
REDIS_HOST=localhost
REDIS_PORT=63790
QDRANT_HOST=localhost
QDRANT_PORT=63330
```

## 🚀 Quick Start

### 1. Start Database Services

```bash
cd docker
docker-compose up -d
```

### 2. Initialize Schema

```bash
# PostgreSQL schema is auto-initialized via docker/init/postgres/01_init_schema.sql
# MongoDB indexes are created automatically on first connection
```

### 3. Configure Application

```python
from src.core.services.data_persistence_service import DataPersistenceService
from src.core.config.settings import get_config

# Initialize persistence service
config = get_config()
persistence = DataPersistenceService(config)
await persistence.initialize()
```

### 4. Basic Usage

```python
# Store a document
document_id = await persistence.store_document(
    document=Document(...),
    content="Document content...",
    chunks=["chunk1", "chunk2"],
    metadata={"type": "contract"}
)

# Run analysis
session_id = await persistence.create_analysis_session(
    AnalysisSession(user_id=user_id, document_id=document_id, ...)
)

# Get results
results = await persistence.get_session_results(session_id)
```

## 📊 Data Export & Import

### Export Formats

```python
from src.utils.data_export import DataExportService

export_service = DataExportService(persistence)

# Export user data as JSON
json_data = await export_service.export_user_data(user_id, format="json")

# Export as ZIP archive
zip_data = await export_service.export_user_data(user_id, format="zip")

# Export system data
system_data = await export_service.export_system_data(include_user_data=True)
```

### Import Data

```python
from src.utils.data_export import DataImportService

import_service = DataImportService(persistence)

# Import from JSON
results = await import_service.import_user_data(json_data, "json")

# Import from ZIP
results = await import_service.import_user_data(zip_data, "zip")
```

### Automated Backups

```python
from src.utils.data_export import DataBackupService

backup_service = DataBackupService(persistence)

# Create full backup
backup_path = await backup_service.create_backup("full")

# Create user backup
user_backup = await backup_service.create_backup("user", user_id)

# List available backups
backups = await backup_service.list_backups()

# Restore from backup
restore_results = await backup_service.restore_backup(backup_path)
```

## 📈 Monitoring & Analytics

### System Health

```python
# Get comprehensive system stats
stats = await persistence.get_system_stats()

# Check individual database health
postgres_health = await persistence.postgres.health_check()
mongodb_health = await persistence.mongodb.health_check()
redis_health = await persistence.redis.health_check()
```

### Usage Analytics

```python
# Get user usage statistics
user_stats = await persistence.get_user_stats(user_id, days=30)

# Get system-wide usage
system_usage = await persistence.get_system_stats()
```

### Performance Metrics

```python
# Cache performance metrics
await persistence.redis.cache_performance_metrics(
    operation="document_analysis",
    metrics={
        "processing_time": 2.3,
        "tokens_used": 1500,
        "cost": 0.05
    }
)

# Get performance history
metrics = await persistence.redis.get_performance_metrics(
    operation="document_analysis",
    limit=10
)
```

## 🔒 Security Considerations

### Data Encryption
- **API Keys**: Encrypted in PostgreSQL using application key
- **Sensitive Metadata**: Encrypted fields in MongoDB
- **Session Data**: Encrypted in Redis with TTL

### Access Control
- **Database Users**: Separate credentials for each database
- **Network Security**: Docker networks isolate services
- **Audit Logging**: All data access logged in PostgreSQL

### Backup Security
- **Encrypted Backups**: All exports encrypted
- **Access Logging**: Backup operations audited
- **Retention Policies**: Automatic cleanup of old backups

## 🔧 Maintenance & Operations

### Database Maintenance

```bash
# PostgreSQL maintenance
docker exec -it ai_law_firm_postgres psql -U ai_law_user -d ai_law_firm

# MongoDB maintenance
docker exec -it ai_law_firm_mongodb mongosh -u ai_law_admin -p ai_law_mongo_password_2024

# Redis maintenance
docker exec -it ai_law_firm_redis redis-cli -p 63790 -a ai_law_redis_password_2024
```

### Backup Operations

```python
# Automated daily backups
import asyncio
from src.utils.data_export import DataBackupService

async def daily_backup():
    backup_service = DataBackupService(persistence)

    # Create system backup
    backup_path = await backup_service.create_backup("full")

    # Cleanup old backups (30 days retention)
    deleted = await backup_service.cleanup_old_backups(30)

    print(f"Backup created: {backup_path}, {deleted} old backups cleaned")

# Schedule with cron or similar
```

### Performance Optimization

```python
# Optimize PostgreSQL
await persistence.postgres.execute("""
    VACUUM ANALYZE;
    REINDEX DATABASE ai_law_firm;
""")

# Optimize MongoDB
await persistence.mongodb.database.command("compact", "documents")
await persistence.mongodb.database.command("compact", "analysis_results")

# Clear old Redis keys
await persistence.redis.redis.flushdb()  # Careful with this!
```

## 🚨 Troubleshooting

### Common Issues

#### PostgreSQL Connection Issues
```bash
# Check if container is running
docker ps | grep postgres

# Check logs
docker logs ai_law_firm_postgres

# Test connection
docker exec -it ai_law_firm_postgres psql -U ai_law_user -d ai_law_firm -c "SELECT 1;"
```

#### MongoDB Connection Issues
```bash
# Check MongoDB status
docker exec -it ai_law_firm_mongodb mongosh --eval "db.adminCommand('ping')"

# Check MongoDB logs
docker logs ai_law_firm_mongodb
```

#### Redis Connection Issues
```bash
# Test Redis connection
docker exec -it ai_law_firm_redis redis-cli -p 63790 -a ai_law_redis_password_2024 ping
```

### Performance Issues

#### Slow Queries
```python
# Enable query logging in PostgreSQL
await persistence.postgres.execute("""
    ALTER DATABASE ai_law_firm SET log_statement = 'all';
    ALTER DATABASE ai_law_firm SET log_duration = 'on';
""")

# Check MongoDB slow queries
db.system.profile.find().sort({ts: -1}).limit(5)
```

#### Memory Issues
```python
# Check Redis memory usage
redis_info = await persistence.redis.redis.info("memory")

# Check PostgreSQL memory
postgres_stats = await persistence.postgres.fetchval("""
    SELECT pg_size_pretty(pg_database_size('ai_law_firm'));
""")
```

## 📚 API Reference

### DataPersistenceService

#### Core Methods
- `initialize()` - Initialize all database connections
- `shutdown()` - Close all database connections
- `health_check()` - Comprehensive system health check

#### User Management
- `create_user(user)` - Create new user
- `get_user(user_id)` - Get user by ID
- `get_user_by_email(email)` - Get user by email

#### Document Management
- `store_document(document, content, chunks, metadata)` - Store complete document
- `get_document(document_id)` - Retrieve document with content
- `update_document_status(document_id, status, error_message)` - Update processing status

#### Analysis Management
- `create_analysis_session(session)` - Create analysis session
- `store_analysis_result(result, response, sources, metadata)` - Store analysis result
- `get_session_results(session_id)` - Get all results for session

#### Analytics
- `get_system_stats()` - Get comprehensive system statistics
- `get_user_stats(user_id, days)` - Get user usage statistics
- `record_usage(user_id, ...)` - Record usage metrics

### Export/Import Services

#### DataExportService
- `export_user_data(user_id, format, include_content)` - Export user data
- `export_system_data(include_user_data, format)` - Export system data

#### DataImportService
- `import_user_data(data, format, conflict_resolution)` - Import user data

#### DataBackupService
- `create_backup(backup_type, user_id, include_content)` - Create backup
- `restore_backup(backup_path, restore_type)` - Restore from backup
- `list_backups()` - List available backups

## 🎯 Best Practices

### Data Modeling
1. **Use appropriate database** for each data type
2. **Normalize relational data** in PostgreSQL
3. **Denormalize for performance** in MongoDB
4. **Cache frequently accessed data** in Redis
5. **Vectorize text data** for semantic search

### Performance Optimization
1. **Index strategically** - balance read vs write performance
2. **Use connection pooling** - avoid connection overhead
3. **Implement caching layers** - reduce database load
4. **Batch operations** - minimize network round trips
5. **Monitor query performance** - identify and optimize slow queries

### Security
1. **Encrypt sensitive data** at rest and in transit
2. **Use parameterized queries** to prevent SQL injection
3. **Implement proper authentication** and authorization
4. **Regular security audits** and updates
5. **Secure backup storage** and transmission

### Backup & Recovery
1. **Automated backups** with retention policies
2. **Test restore procedures** regularly
3. **Multiple backup locations** for redundancy
4. **Encrypt backup data** for security
5. **Document recovery procedures** clearly

This comprehensive data persistence architecture ensures the AI Law Firm system can handle complex legal document analysis workflows while maintaining data integrity, performance, and scalability.