# 👨‍⚖️ AI Law Firm - Multi-Agent Legal Document Analysis System

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Streamlit](https://img.shields.io/badge/Streamlit-1.40+-red.svg)](https://streamlit.io)

A sophisticated AI-powered legal document analysis system that simulates a full-service legal team using multiple specialized AI agents. The system provides comprehensive legal analysis, research, and strategic recommendations through an intelligent multi-agent architecture.

## 🌟 Key Features

### 🤖 Specialized AI Agent Team
- **Legal Researcher**: Advanced legal research with web search integration, case law analysis, and precedent identification
- **Contract Analyst**: Deep contract review, clause analysis, risk identification, and compliance checking
- **Legal Strategist**: Strategic legal planning, risk assessment, and actionable recommendations
- **Team Lead**: Intelligent coordination and synthesis of all agent analyses into comprehensive reports

### 📄 Comprehensive Analysis Types
- **Contract Review**: Thorough contract analysis with clause-by-clause breakdown
- **Legal Research**: Automated research with case citations and legal precedent identification
- **Risk Assessment**: Multi-dimensional risk analysis combining legal, financial, and operational perspectives
- **Compliance Check**: Regulatory compliance verification across multiple jurisdictions
- **Custom Analysis**: Flexible query-based analysis for specific legal questions

### 🏗️ Flexible Deployment Options
- **Cloud Version**: OpenAI GPT-4 + Qdrant Cloud for enterprise-grade performance
- **Local Version**: Ollama + Local Qdrant for privacy-focused, offline operation
- **Hybrid Mode**: Configurable deployment supporting both paradigms

## 📋 Table of Contents

- [Quick Start](#-quick-start)
- [Architecture](#-architecture)
- [Installation](#-installation)
- [Configuration](#-configuration)
- [Usage](#-usage)
- [Development](#-development)
- [Testing](#-testing)
- [Deployment](#-deployment)
- [Contributing](#-contributing)
- [License](#-license)

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- For Cloud Version: OpenAI API key, Qdrant Cloud account
- For Local Version: Docker, Ollama with Llama 3.2 model

### Cloud Version (Recommended)
```bash
# Clone and setup
git clone <repository-url>
cd ai-law-firm
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Run the application
streamlit run src/ui/app.py
```

### Local Version (Privacy-Focused)
```bash
# Setup local infrastructure
docker run -p 6333:6333 qdrant/qdrant
ollama pull llama3.2:3b

# Run the application
cd local_ai_legal_agent_team
./start_legal_agent.sh
```

Visit `http://localhost:8501` to access the application.

## 🏛️ Architecture

The AI Law Firm implements a sophisticated multi-agent architecture designed for scalability, maintainability, and extensibility.

### System Components

```mermaid
graph TB
    A[Streamlit UI] --> B[Analysis Orchestrator]
    B --> C[Agent Manager]
    B --> D[Document Processor]
    B --> E[Knowledge Base]

    C --> F[Legal Researcher]
    C --> G[Contract Analyst]
    C --> H[Legal Strategist]
    C --> I[Team Lead]

    D --> J[Document Chunker]
    D --> K[Vector Store]

    E --> K
    E --> L[Search Engine]

    F --> M[AI Provider]
    G --> M
    H --> M
    I --> M

    M --> N[OpenAI]
    M --> O[Ollama]
```

### Key Design Principles
- **Modular Architecture**: Clear separation of concerns with pluggable components
- **Provider Abstraction**: Unified interfaces for different AI and vector database providers
- **Configuration-Driven**: Environment-based configuration management
- **Testable Design**: Dependency injection and interface-based architecture

For detailed architectural information, see [ARCHITECTURE.md](ARCHITECTURE.md).

## 📦 Installation

### Option 1: Cloud Version

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-law-firm
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API credentials
   ```

### Option 2: Local Version

1. **Install Ollama**
   ```bash
   # macOS
   brew install ollama

   # Linux
   curl -fsSL https://ollama.ai/install.sh | sh

   # Pull required model
   ollama pull llama3.2:3b
   ```

2. **Setup Qdrant locally**
   ```bash
   docker run -p 6333:6333 -p 6334:6334 \
     -v $(pwd)/qdrant_storage:/qdrant/storage \
     qdrant/qdrant
   ```

3. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# OpenAI Configuration (Cloud Version)
OPENAI_API_KEY=your_openai_api_key_here

# Qdrant Configuration
QDRANT_URL=https://your-qdrant-instance.cloud.qdrant.io
QDRANT_API_KEY=your_qdrant_api_key_here

# Application Settings
APP_ENV=development
LOG_LEVEL=INFO
MAX_DOCUMENT_SIZE=50MB

# Local Configuration (Local Version)
OLLAMA_BASE_URL=http://localhost:11434
LOCAL_QDRANT_URL=http://localhost:6333
```

### Configuration Files

The system supports multiple configuration environments:
- `config/default.yaml` - Base configuration
- `config/development.yaml` - Development settings
- `config/production.yaml` - Production settings
- `config/local.yaml` - Local development settings

## 💡 Usage

### Basic Workflow

1. **Access the Application**
   - Open your browser to `http://localhost:8501`
   - The application will load the main interface

2. **Configure API Credentials** (Cloud Version)
   - Navigate to the sidebar configuration panel
   - Enter your OpenAI API key
   - Enter your Qdrant credentials
   - Test the connection

3. **Upload Document**
   - Use the file uploader to select a PDF document
   - Supported formats: PDF (up to 50MB)
   - The system will process and index the document

4. **Select Analysis Type**
   - Choose from predefined analysis types:
     - Contract Review
     - Legal Research
     - Risk Assessment
     - Compliance Check
     - Custom Query

5. **Configure Analysis** (Optional)
   - Add custom queries or specific requirements
   - Select focus areas or jurisdictions

6. **Run Analysis**
   - Click "Analyze" to start the multi-agent analysis
   - View progress in real-time
   - Results are presented in organized tabs

### Analysis Types

#### Contract Review
- Comprehensive contract analysis
- Key terms and obligations identification
- Risk and liability assessment
- Clause-by-clause breakdown

#### Legal Research
- Automated legal research
- Case law and precedent identification
- Citation and source verification
- Relevant statute analysis

#### Risk Assessment
- Multi-dimensional risk analysis
- Legal, financial, and operational risks
- Mitigation strategy recommendations
- Probability and impact assessment

#### Compliance Check
- Regulatory compliance verification
- Industry-specific requirement checking
- Jurisdiction coverage analysis
- Gap analysis and recommendations

#### Custom Query
- Flexible query-based analysis
- Specific legal questions
- Targeted research requests
- Custom analysis parameters

## 🛠️ Development

### Project Structure
```
AI-Law-Firm/
├── src/                          # Source code
│   ├── core/                     # Core business logic
│   ├── infrastructure/           # External integrations
│   ├── ui/                       # User interface
│   └── utils/                    # Shared utilities
├── tests/                        # Test suite
├── docs/                         # Documentation
├── config/                       # Configuration files
├── scripts/                      # Utility scripts
└── docker/                       # Docker configurations
```

### Development Setup

1. **Setup development environment**
   ```bash
   # Install development dependencies
   pip install -r requirements-dev.txt

   # Setup pre-commit hooks
   pre-commit install

   # Run development server
   streamlit run src/ui/app.py --server.headless true
   ```

2. **Code Quality**
   ```bash
   # Run linting
   flake8 src/

   # Run type checking
   mypy src/

   # Format code
   black src/
   isort src/
   ```

### Adding New Features

1. **Agent Development**
   - Create new agent class in `src/core/agents/`
   - Implement required interfaces
   - Add configuration support
   - Write comprehensive tests

2. **Analysis Types**
   - Define analysis type in `src/core/analysis/types.py`
   - Implement analysis logic in orchestrator
   - Add UI components
   - Update configuration

3. **Provider Integration**
   - Create provider class in `src/infrastructure/`
   - Implement provider interface
   - Add configuration schema
   - Write integration tests

## 🧪 Testing

The project includes comprehensive testing coverage:

### Test Structure
```
tests/
├── unit/                         # Unit tests
├── integration/                  # Integration tests
├── ui/                           # UI component tests
├── fixtures/                     # Test data and fixtures
└── conftest.py                   # Test configuration
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/ui/

# Run performance tests
pytest tests/performance/
```

### Test Coverage Goals
- **Unit Tests**: 80%+ coverage for core business logic
- **Integration Tests**: End-to-end workflow coverage
- **UI Tests**: Component interaction coverage
- **Performance Tests**: Load and stress testing

## 🚀 Deployment

### Docker Deployment

1. **Build the application**
   ```bash
   docker build -t ai-law-firm .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

### Cloud Deployment

#### AWS
```bash
# Using AWS ECS
aws ecs create-service --cluster ai-law-firm \
  --service-name ai-law-firm-service \
  --task-definition ai-law-firm-task \
  --desired-count 1
```

#### Google Cloud
```bash
# Using Google Cloud Run
gcloud run deploy ai-law-firm \
  --source . \
  --platform managed \
  --region us-central1
```

### Production Checklist
- [ ] Environment variables configured
- [ ] SSL/TLS certificates installed
- [ ] Database backups configured
- [ ] Monitoring and alerting setup
- [ ] Load balancer configured
- [ ] Security groups configured
- [ ] API rate limiting enabled

## 🤝 Contributing

We welcome contributions to the AI Law Firm project!

### Development Process

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes**
   - Follow the existing code style
   - Add tests for new functionality
   - Update documentation as needed

4. **Run the test suite**
   ```bash
   pytest
   ```

5. **Commit your changes**
   ```bash
   git commit -m "Add your feature description"
   ```

6. **Push to your branch**
   ```bash
   git push origin feature/your-feature-name
   ```

7. **Create a Pull Request**

### Code Standards
- **Python**: PEP 8 style guide
- **Documentation**: Google-style docstrings
- **Testing**: pytest framework with 80%+ coverage
- **Git**: Conventional commit messages

### Areas for Contribution
- New AI agent specializations
- Additional analysis types
- UI/UX improvements
- Performance optimizations
- Documentation enhancements
- Test coverage improvements

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Agno Framework**: For the excellent agent orchestration platform
- **Qdrant**: For the powerful vector database technology
- **OpenAI**: For access to state-of-the-art language models
- **Ollama**: For enabling local AI model deployment
- **Streamlit**: For the intuitive web application framework

## 📞 Support

- **Documentation**: [Full Documentation](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/discussions)
- **Email**: <EMAIL>

---

**AI Law Firm** - Transforming legal document analysis through intelligent multi-agent systems.
