#!/usr/bin/env python3
"""
Test script demonstrating Intelligent Model Selection for Enhanced Legal Agents.

This script showcases how each of the 45 specialized legal agents can dynamically
select and switch between AI models based on different priorities:

🎯 Best Answer: Prioritizes quality for complex legal analysis
💰 Least Cost: Minimizes API costs while maintaining acceptable quality
⚡ Speed: Fastest response for time-sensitive queries
⚖️ Balanced: Optimal quality-cost-speed trade-off
🧠 Adaptive: Learns from performance and user feedback

Each agent intelligently chooses the optimal model for their specific legal domain
and task requirements.
"""
import sys
import os
import asyncio
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

# Skip validation for testing
os.environ["SKIP_CONFIG_VALIDATION"] = "true"

from src.core.agents.enhanced_legal_agent import EnhancedLegalAgent, AgentModelPreferences
from src.core.agents.model_selector import IntelligentModelSelector, ModelSelectionStrategy
from src.core.agents.legal_domain_detector import LegalDomain
from src.core.agents.base import AgentRole, AgentCapability
from src.infrastructure.ai_providers.openai_provider import OpenAIProvider
from src.core.config.settings import OpenAIConfig

async def demonstrate_intelligent_model_selection():
    """Demonstrate intelligent model selection across different legal domains."""
    print("🎯 AI Law Firm - Intelligent Model Selection for 45 Legal Agents")
    print("=" * 70)

    # Initialize model selector
    model_selector = IntelligentModelSelector()

    print(f"✅ Initialized with {len(model_selector.models)} AI models")
    print()

    # Create enhanced legal agents for different domains
    agents = await create_specialized_agents(model_selector)

    print(f"✅ Created {len(agents)} enhanced legal agents")
    print()

    # Test scenarios for different optimization strategies
    test_scenarios = [
        {
            "name": "Complex Constitutional Law Analysis",
            "query": "Analyze the constitutionality of a state law requiring social media platforms to remove content within 24 hours of receiving a complaint, considering First Amendment protections, due process requirements, and potential equal protection violations.",
            "strategy": ModelSelectionStrategy.BEST_ANSWER,
            "expected_agent": "Constitutional Law Expert"
        },
        {
            "name": "Urgent Criminal Defense Consultation",
            "query": "Client arrested for DUI, needs immediate advice on whether to take breathalyzer test, right to counsel, and bail options. Court appearance tomorrow morning.",
            "strategy": ModelSelectionStrategy.SPEED,
            "expected_agent": "Criminal Defense Attorney"
        },
        {
            "name": "Cost-Effective Contract Review",
            "query": "Review this 5-page service agreement for potential liability issues and unfair terms. Looking for basic risk assessment on standard commercial contract.",
            "strategy": ModelSelectionStrategy.LEAST_COST,
            "expected_agent": "Commercial Contracts Attorney"
        },
        {
            "name": "Balanced Employment Law Analysis",
            "query": "Employee claims constructive dismissal due to hostile work environment. Need analysis of harassment claims, retaliation concerns, and potential remedies under federal and state law.",
            "strategy": ModelSelectionStrategy.BALANCED,
            "expected_agent": "Employment Discrimination Lawyer"
        },
        {
            "name": "Adaptive IP Strategy Development",
            "query": "Competitor using similar technology - need comprehensive IP strategy including patent filing, trademark protection, licensing options, and potential litigation readiness.",
            "strategy": ModelSelectionStrategy.ADAPTIVE,
            "expected_agent": "Patent Attorney"
        }
    ]

    print("🧪 Testing Intelligent Model Selection Strategies:")
    print("-" * 70)

    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print("-" * 50)

        # Find appropriate agent
        selected_agent = find_agent_for_scenario(agents, scenario)

        if not selected_agent:
            print(f"❌ No suitable agent found for: {scenario['query'][:50]}...")
            continue

        print(f"🤖 Selected Agent: {selected_agent.name}")
        print(f"🏛️ Legal Domain: {selected_agent.legal_domain.value}")
        print(f"🎯 Strategy: {scenario['strategy'].value}")
        print(f"Query: {scenario['query'][:80]}...")

        # Process query with intelligent model selection
        start_time = asyncio.get_event_loop().time()

        response = await selected_agent.process_query(
            scenario['query'],
            model_strategy=scenario['strategy']
        )

        processing_time = asyncio.get_event_loop().time() - start_time

        # Display results
        print("
📊 Model Selection Results:"        print(f"   Selected Model: {response.model_used}")
        print(f"   Confidence: {response.confidence_score:.2f}")
        print(f"   Processing Time: {processing_time:.2f}s")
        print(f"   Expected Cost: ${response.metadata.get('expected_cost', 0):.4f}")

        # Show model selection reasoning
        selection_reasoning = response.metadata.get('selection_strategy', 'N/A')
        print(f"   Selection Logic: {str(selection_reasoning)[:100]}...")

        # Show alternative models considered
        alternatives = response.metadata.get('alternative_models', [])
        if alternatives:
            print(f"   Alternatives: {', '.join(alternatives[:2])}")

        print(f"\n💡 Response Preview: {response.content[:150]}...")

    print("\n" + "=" * 70)
    print("📈 Model Selection Strategy Analysis:")
    print("=" * 70)

    # Analyze model selection patterns
    await analyze_model_selection_patterns(agents, model_selector)

    print("\n" + "=" * 70)
    print("🎯 Agent-Specific Model Recommendations:")
    print("=" * 70)

    # Show model recommendations for different agents
    await demonstrate_agent_model_recommendations(agents)

    print("\n" + "=" * 70)
    print("💰 Cost Optimization Analysis:")
    print("=" * 70)

    # Demonstrate cost optimization
    await demonstrate_cost_optimization(agents)

    print("\n" + "=" * 70)
    print("⚡ Performance Analytics:")
    print("=" * 70)

    # Show performance analytics
    await demonstrate_performance_analytics(agents)

    print("\n" + "=" * 70)
    print("🎉 Intelligent Model Selection - Complete!")
    print("=" * 70)
    print("✅ Each of 45 legal agents can dynamically select optimal models")
    print("✅ Best Answer prioritization for complex legal analysis")
    print("✅ Least Cost optimization for budget-conscious queries")
    print("✅ Speed optimization for time-sensitive matters")
    print("✅ Balanced approach for everyday legal work")
    print("✅ Adaptive learning from performance data")
    print("✅ Real-time cost tracking and optimization")
    print("✅ Domain-specific model specialization")
    print()
    print("🚀 Legal agents now intelligently choose the perfect AI model for every task!")


async def create_specialized_agents(model_selector):
    """Create enhanced legal agents for different domains."""
    # Mock AI provider for testing
    ai_provider = OpenAIProvider(OpenAIConfig(api_key="test-key"))

    agents = []

    # Create agents for key legal domains
    agent_configs = [
        {
            "name": "Constitutional Law Expert",
            "domain": LegalDomain.CONSTITUTIONAL_LAW,
            "capabilities": [AgentCapability.LEGAL_RESEARCH, AgentCapability.STRATEGY_DEVELOPMENT],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.BEST_ANSWER,
                quality_threshold=0.9,
                cost_sensitivity="low"
            )
        },
        {
            "name": "Criminal Defense Attorney",
            "domain": LegalDomain.CRIMINAL_LAW,
            "capabilities": [AgentCapability.LEGAL_RESEARCH, AgentCapability.STRATEGY_DEVELOPMENT],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.SPEED,
                speed_priority="high",
                quality_threshold=0.8
            )
        },
        {
            "name": "Commercial Contracts Attorney",
            "domain": LegalDomain.CONTRACT_LAW,
            "capabilities": [AgentCapability.CONTRACT_REVIEW, AgentCapability.RISK_ASSESSMENT],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.LEAST_COST,
                cost_sensitivity="high",
                quality_threshold=0.7
            )
        },
        {
            "name": "Employment Discrimination Lawyer",
            "domain": LegalDomain.EMPLOYMENT_LAW,
            "capabilities": [AgentCapability.LEGAL_RESEARCH, AgentCapability.STRATEGY_DEVELOPMENT],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.BALANCED,
                quality_threshold=0.8
            )
        },
        {
            "name": "Patent Attorney",
            "domain": LegalDomain.INTELLECTUAL_PROPERTY,
            "capabilities": [AgentCapability.LEGAL_RESEARCH, AgentCapability.STRATEGY_DEVELOPMENT],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.ADAPTIVE,
                quality_threshold=0.85,
                adaptive_learning=True
            )
        },
        {
            "name": "Family Law Mediator",
            "domain": LegalDomain.FAMILY_LAW,
            "capabilities": [AgentCapability.LEGAL_RESEARCH, AgentCapability.STRATEGY_DEVELOPMENT],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.BALANCED,
                quality_threshold=0.8
            )
        },
        {
            "name": "Corporate Tax Attorney",
            "domain": LegalDomain.TAX_LAW,
            "capabilities": [AgentCapability.LEGAL_RESEARCH, AgentCapability.COMPLIANCE_CHECKING],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.BEST_ANSWER,
                quality_threshold=0.9
            )
        },
        {
            "name": "Environmental Compliance Attorney",
            "domain": LegalDomain.ENVIRONMENTAL_LAW,
            "capabilities": [AgentCapability.REGULATORY_COMPLIANCE, AgentCapability.RISK_ASSESSMENT],
            "preferences": AgentModelPreferences(
                default_strategy=ModelSelectionStrategy.BALANCED,
                quality_threshold=0.8
            )
        }
    ]

    for config in agent_configs:
        agent = EnhancedLegalAgent(
            name=config["name"],
            role=AgentRole.LEGAL_SPECIALIST,
            capabilities=config["capabilities"],
            ai_provider=ai_provider,
            legal_domain=config["domain"],
            model_selector=model_selector,
            preferences=config["preferences"]
        )

        # Initialize agent
        if await agent.initialize():
            agents.append(agent)
        else:
            print(f"❌ Failed to initialize {config['name']}")

    return agents


def find_agent_for_scenario(agents, scenario):
    """Find the most appropriate agent for a test scenario."""
    query = scenario['query'].lower()

    # Simple keyword matching for demo
    agent_mappings = {
        "constitutional": "Constitutional Law Expert",
        "criminal": "Criminal Defense Attorney",
        "contract": "Commercial Contracts Attorney",
        "employment": "Employment Discrimination Lawyer",
        "patent": "Patent Attorney",
        "family": "Family Law Mediator",
        "tax": "Corporate Tax Attorney",
        "environmental": "Environmental Compliance Attorney"
    }

    for keyword, agent_name in agent_mappings.items():
        if keyword in query:
            return next((a for a in agents if a.name == agent_name), None)

    # Default to first agent if no match
    return agents[0] if agents else None


async def analyze_model_selection_patterns(agents, model_selector):
    """Analyze patterns in model selection across agents."""
    print("📊 Model Selection Pattern Analysis:")
    print("-" * 40)

    # Get selection analytics
    analytics = model_selector.get_selection_analytics()

    print(f"Total Model Selections: {analytics['total_selections']}")
    print(f"Average Confidence: {analytics['average_confidence']:.2f}")
    print(f"Total Estimated Cost: ${analytics['total_estimated_cost']:.2f}")
    print(f"Average Cost per Selection: ${analytics['average_cost_per_selection']:.4f}")

    if analytics['most_popular_model']:
        print(f"Most Popular Model: {analytics['most_popular_model']}")

    if analytics['most_used_strategy']:
        print(f"Most Used Strategy: {analytics['most_used_strategy']}")

    print("
Strategy Distribution:"    for strategy, count in analytics['strategy_distribution'].items():
        percentage = (count / analytics['total_selections']) * 100
        print(".1f")

    print("
Model Usage Distribution:"    for model, count in analytics['model_usage_distribution'].items():
        percentage = (count / analytics['total_selections']) * 100
        print(".1f")


async def demonstrate_agent_model_recommendations(agents):
    """Demonstrate model recommendations for different agents."""
    print("🤖 Agent-Specific Model Recommendations:")
    print("-" * 45)

    strategies = [
        (ModelSelectionStrategy.BEST_ANSWER, "Best Answer"),
        (ModelSelectionStrategy.LEAST_COST, "Least Cost"),
        (ModelSelectionStrategy.SPEED, "Speed")
    ]

    for agent in agents[:3]:  # Show first 3 agents
        print(f"\n{agent.name} ({agent.legal_domain.value}):")

        for strategy, name in strategies:
            recommendations = agent.get_model_recommendations(strategy)
            if recommendations:
                top_rec = recommendations[0]
                print(f"  {name}: {top_rec['display_name']} "
                      f"(Quality: {top_rec['quality_score']:.2f}, "
                      f"Cost: ${top_rec['cost_per_1k']:.4f})")


async def demonstrate_cost_optimization(agents):
    """Demonstrate cost optimization across different scenarios."""
    print("💰 Cost Optimization Analysis:")
    print("-" * 30)

    # Simulate different cost scenarios
    scenarios = [
        {"name": "Complex Legal Research", "tokens": 3000, "priority": "quality"},
        {"name": "Simple Contract Review", "tokens": 800, "priority": "cost"},
        {"name": "Urgent Legal Consultation", "tokens": 1200, "priority": "speed"},
        {"name": "Routine Compliance Check", "tokens": 500, "priority": "balance"}
    ]

    for scenario in scenarios:
        print(f"\n{scenario['name']} ({scenario['tokens']} tokens):")

        # Estimate costs for different strategies
        cost_estimates = {}

        for agent in agents[:2]:  # Use first 2 agents as examples
            for strategy in [ModelSelectionStrategy.BEST_ANSWER,
                           ModelSelectionStrategy.LEAST_COST,
                           ModelSelectionStrategy.SPEED]:
                # Get model recommendations
                recommendations = agent.get_model_recommendations(strategy)
                if recommendations:
                    top_model = recommendations[0]
                    cost = (scenario['tokens'] / 1000) * top_model['cost_per_1k']
                    strategy_key = f"{agent.name[:20]} - {strategy.value}"

                    if strategy_key not in cost_estimates:
                        cost_estimates[strategy_key] = cost

        # Show cost comparison
        for strategy_key, cost in sorted(cost_estimates.items(), key=lambda x: x[1]):
            print(".4f")


async def demonstrate_performance_analytics(agents):
    """Demonstrate performance analytics for agents."""
    print("⚡ Agent Performance Analytics:")
    print("-" * 35)

    for agent in agents[:3]:  # Show first 3 agents
        analytics = agent.get_performance_analytics()

        if isinstance(analytics, dict) and "total_tasks" in analytics:
            print(f"\n{agent.name}:")
            print(f"  Tasks Processed: {analytics['total_tasks']}")
            print(f"  Avg Processing Time: {analytics['average_processing_time']:.2f}s")
            print(f"  Avg Confidence: {analytics['average_confidence']:.2f}")
            print(f"  Total Cost: ${analytics['total_cost']:.4f}")

            if analytics.get('most_used_model'):
                print(f"  Preferred Model: {analytics['most_used_model']}")
        else:
            print(f"\n{agent.name}: No performance data available yet")


if __name__ == "__main__":
    asyncio.run(demonstrate_intelligent_model_selection())