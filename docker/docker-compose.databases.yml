version: '3.8'

services:
  # PostgreSQL - Relational data (users, sessions, metadata)
  postgres:
    image: postgres:15-alpine
    container_name: AI-LAW-FIRM-PostgreSQL
    restart: unless-stopped
    environment:
      POSTGRES_DB: ai_law_firm
      POSTGRES_USER: ai_law_user
      POSTGRES_PASSWORD: ai_law_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=en_US.UTF-8 --lc-ctype=en_US.UTF-8"
    ports:
      - "54320:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init/postgres:/docker-entrypoint-initdb.d
    networks:
      - AI-LAW-FIRM-Network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_law_user -d ai_law_firm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB - Unstructured data (documents, analysis results)
  mongodb:
    image: mongo:7-jammy
    container_name: AI-LAW-FIRM-MongoDB
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ai_law_admin
      MONGO_INITDB_ROOT_PASSWORD: ai_law_mongo_password_2024
      MONGO_INITDB_DATABASE: ai_law_firm
    ports:
      - "27019:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init/mongodb:/docker-entrypoint-initdb.d
    networks:
      - AI-LAW-FIRM-Network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis - Caching and session management
  redis:
    image: redis:7-alpine
    container_name: AI-LAW-FIRM-Redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ai_law_redis_password_2024
    ports:
      - "63790:6379"
    volumes:
      - redis_data:/data
    networks:
      - AI-LAW-FIRM-Network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Qdrant - Vector database (embeddings and similarity search)
  qdrant:
    image: qdrant/qdrant:v1.7.4
    container_name: AI-LAW-FIRM-Qdrant
    restart: unless-stopped
    ports:
      - "63330:6333"
      - "63331:6334"
    volumes:
      - qdrant_data:/qdrant/storage
      - ./config/qdrant:/qdrant/config
    networks:
      - AI-LAW-FIRM-Network
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6334
      - QDRANT__SERVICE__GRPC_PORT=6333
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6334/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local

networks:
  AI-LAW-FIRM-Network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16