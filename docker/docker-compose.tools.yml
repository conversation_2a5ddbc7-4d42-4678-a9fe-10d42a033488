version: '3.8'

services:
  # PgAdmin - PostgreSQL management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ai_law_firm_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "50500:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - AI-LAW-FIRM-Network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - tools

  # Mongo Express - MongoDB management (optional)
  mongo-express:
    image: mongo-express:latest
    container_name: ai_law_firm_mongo_express
    restart: unless-stopped
    ports:
      - 8081:8081
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ai_law_admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: ai_law_mongo_password_2024
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: express_password_2024
    networks:
      - AI-LAW-FIRM-Network
    depends_on:
      mongodb:
        condition: service_healthy
    profiles:
      - tools

  # Redis Commander - Redis management (optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ai_law_firm_redis_commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:63790:0:ai_law_redis_password_2024
    ports:
      - 8082:8081
    networks:
      - AI-LAW-FIRM-Network
    depends_on:
      redis:
        condition: service_healthy
    profiles:
      - tools

volumes:
  pgadmin_data:
    driver: local

networks:
  AI-LAW-FIRM-Network:
    external: true