version: '3.8'

services:
  # PostgreSQL - Relational data (users, sessions, metadata)
  postgres:
    image: postgres:15-alpine
    container_name: ai_law_firm_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ai_law_firm
      POSTGRES_USER: ai_law_user
      POSTGRES_PASSWORD: ai_law_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=en_US.UTF-8 --lc-ctype=en_US.UTF-8"
    ports:
      - "54320:5432"  # Free port block starting at 54320
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init/postgres:/docker-entrypoint-initdb.d
    networks:
      - ai_law_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_law_user -d ai_law_firm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB - Unstructured data (documents, analysis results)
  mongodb:
    image: mongo:7-jammy
    container_name: ai_law_firm_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ai_law_admin
      MONGO_INITDB_ROOT_PASSWORD: ai_law_mongo_password_2024
      MONGO_INITDB_DATABASE: ai_law_firm
    ports:
      - "27019:27017"  # Free port to avoid conflicts
    volumes:
      - mongodb_data:/data/db
      - ./init/mongodb:/docker-entrypoint-initdb.d
    networks:
      - ai_law_network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis - Caching and session management
  redis:
    image: redis:7-alpine
    container_name: ai_law_firm_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ai_law_redis_password_2024
    ports:
      - "63790:6379"  # Free port in our block
    volumes:
      - redis_data:/data
    networks:
      - ai_law_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Qdrant - Vector database (embeddings and similarity search)
  qdrant:
    image: qdrant/qdrant:v1.7.4
    container_name: ai_law_firm_qdrant
    restart: unless-stopped
    ports:
      - "63330:6333"  # Free port in our block
      - "63331:6334"  # REST API port
    volumes:
      - qdrant_data:/qdrant/storage
      - ./config/qdrant:/qdrant/config
    networks:
      - ai_law_network
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6334
      - QDRANT__SERVICE__GRPC_PORT=6333
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6334/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PgAdmin - PostgreSQL management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ai_law_firm_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "50500:80"  # Free port in our block
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - ai_law_network
    depends_on:
      postgres:
        condition: service_healthy

  # Mongo Express - MongoDB management (optional)
  mongo-express:
    image: mongo-express:latest
    container_name: ai_law_firm_mongo_express
    restart: unless-stopped
    ports:
      - 8081:8081
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ai_law_admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: ai_law_mongo_password_2024
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: express_password_2024
    networks:
      - ai_law_network
    depends_on:
      mongodb:
        condition: service_healthy

  # Redis Commander - Redis management (optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ai_law_firm_redis_commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:63790:0:ai_law_redis_password_2024
    ports:
      - 8082:8081
    networks:
      - ai_law_network
    depends_on:
      redis:
        condition: service_healthy

volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  ai_law_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16