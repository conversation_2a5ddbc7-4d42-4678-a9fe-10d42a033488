# Docker Setup for AI Law Firm

## Architecture Overview

This Docker setup provides a resilient, modular architecture for the AI Law Firm database infrastructure.

## Compose Files

### 1. `docker-compose.databases.yml` - Core Databases
Contains the essential database services:
- **PostgreSQL** (port 54320) - Relational data
- **MongoDB** (port 27019) - Document storage
- **Redis** (port 63790) - Caching and sessions
- **Qdrant** (ports 63330/63331) - Vector database

### 2. `docker-compose.tools.yml` - Management Tools
Contains optional management interfaces:
- **PgAdmin** (port 50500) - PostgreSQL web interface
- **Mongo Express** (port 8081) - MongoDB web interface
- **Redis Commander** (port 8082) - Redis web interface

### 3. `docker-compose.yml` - Full Stack (Legacy)
Contains all services in one file (for backward compatibility)

## Usage

### Start Core Databases Only
```bash
# Start just the databases
docker-compose -f docker-compose.databases.yml up -d

# Stop databases
docker-compose -f docker-compose.databases.yml down
```

### Start Management Tools
```bash
# Start databases first
docker-compose -f docker-compose.databases.yml up -d

# Then start tools (requires databases to be running)
docker-compose -f docker-compose.tools.yml --profile tools up -d

# Stop tools
docker-compose -f docker-compose.tools.yml --profile tools down
```

### Start Everything Together
```bash
# Start all services
docker-compose -f docker-compose.databases.yml -f docker-compose.tools.yml --profile tools up -d

# Stop all services
docker-compose -f docker-compose.databases.yml -f docker-compose.tools.yml --profile tools down
```

## Resilience Features

### Health Checks
- All databases have health checks
- Management tools wait for databases to be healthy before starting
- Automatic restart policies prevent single points of failure

### Network Isolation
- All services communicate through `ai_law_network`
- Tools can be started/stopped independently of databases

### Data Persistence
- All databases use named volumes for data persistence
- Data survives container restarts and updates

## Production Considerations

For production deployment, consider:
- Using Docker Swarm or Kubernetes for orchestration
- Implementing proper backup strategies
- Setting up monitoring and alerting
- Using managed database services (AWS RDS, MongoDB Atlas, etc.)
- Implementing proper security measures (SSL, authentication, etc.)

## Troubleshooting

### Common Issues
1. **Port conflicts**: Change ports in compose files if needed
2. **Permission issues**: Ensure Docker has proper permissions
3. **Data persistence**: Check volume mounts if data is lost

### Health Check Commands
```bash
# Check service health
docker-compose -f docker-compose.databases.yml ps

# View logs
docker-compose -f docker-compose.databases.yml logs [service_name]

# Restart specific service
docker-compose -f docker-compose.databases.yml restart [service_name]