const { chromium } = require('playwright');

async function runAIDemo() {
  console.log('🚀 Starting AI Law Firm Demo...');

  // Create test file for upload demo
  const fs = require('fs');
  const testFileContent = `LEGAL CONTRACT TEMPLATE

This Agreement is made on [DATE] between:

PARTY A: [Party A Name], located at [Address]

PARTY B: [Party B Name], located at [Address]

WHEREAS, the parties desire to enter into a business relationship;

NOW, THEREFORE, the parties agree as follows:

1. SERVICES: Party A shall provide [services] to Party B.

2. COMPENSATION: Party B shall pay Party A $[amount] for services rendered.

3. TERM: This agreement shall commence on [start date] and continue until [end date].

4. CONFIDENTIALITY: Both parties agree to maintain confidentiality of proprietary information.

5. GOVERNING LAW: This agreement shall be governed by the laws of [jurisdiction].

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first above written.

PARTY A: ___________________________    PARTY B: ___________________________
`;

  if (!fs.existsSync('./test-files')) {
    fs.mkdirSync('./test-files');
  }
  fs.writeFileSync('./test-files/sample-contract.txt', testFileContent);

  // Launch browser with video recording
  const browser = await chromium.launch({
    headless: false,
    args: ['--window-size=1920,1080']
  });

  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 },
    recordVideo: {
      dir: './demo-videos/',
      size: { width: 1920, height: 1080 }
    }
  });

  const page = await context.newPage();

  try {
    console.log('📺 Navigating to AI Law Firm...');
    await page.goto('http://localhost:3000');

    // Wait for page to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    console.log('🎯 Starting Feature Demo...');

    // 1. DASHBOARD OVERVIEW
    try {
      console.log('📊 Demonstrating Dashboard...');
      await demonstrateDashboard(page);
    } catch (error) {
      console.log('⚠️  Dashboard demo failed, continuing...');
    }

    // 2. LEGAL CHAT INTERFACE
    try {
      console.log('💬 Demonstrating Legal Chat...');
      await demonstrateLegalChat(page);
    } catch (error) {
      console.log('⚠️  Legal Chat demo failed, continuing...');
    }

    // 3. DOCUMENT UPLOAD
    try {
      console.log('📁 Demonstrating Document Upload...');
      await demonstrateDocumentUpload(page);
    } catch (error) {
      console.log('⚠️  Document Upload demo failed, continuing...');
    }

    // 4. AGENT MANAGEMENT
    try {
      console.log('🤖 Demonstrating Agent Management...');
      await demonstrateAgentManagement(page);
    } catch (error) {
      console.log('⚠️  Agent Management demo failed, continuing...');
    }

    // 5. DOCUMENT COMPARISON
    try {
      console.log('🔍 Demonstrating Document Comparison...');
      await demonstrateDocumentComparison(page);
    } catch (error) {
      console.log('⚠️  Document Comparison demo failed, continuing...');
    }

    // 6. MONITORING DASHBOARD
    try {
      console.log('📈 Demonstrating Monitoring...');
      await demonstrateMonitoring(page);
    } catch (error) {
      console.log('⚠️  Monitoring demo failed, continuing...');
    }

    console.log('✅ Demo completed successfully!');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  } finally {
    await context.close();
    await browser.close();
  }
}

async function demonstrateDashboard(page) {
  console.log('🏠 On Dashboard - Showing main navigation and overview');

  // Take screenshot of dashboard
  await page.screenshot({ path: 'screenshots/dashboard.png', fullPage: true });

  // Navigate through sidebar menu items
  const menuItems = [
    { selector: 'a[href="/"]', name: 'Dashboard' },
    { selector: 'a[href="/chat"]', name: 'Legal Chat' },
    { selector: 'a[href="/upload"]', name: 'Upload Documents' },
    { selector: 'a[href="/documents"]', name: 'Document Library' },
    { selector: 'a[href="/compare"]', name: 'Compare Documents' },
    { selector: 'a[href="/agents"]', name: 'Agent Management' },
    { selector: 'a[href="/monitoring"]', name: 'Monitoring' },
    { selector: 'a[href="/settings"]', name: 'Settings' }
  ];

  for (const item of menuItems) {
    try {
      await page.hover(item.selector);
      await page.waitForTimeout(500);
      console.log(`   ✓ Highlighted ${item.name}`);
    } catch (e) {
      console.log(`   - ${item.name} not found (expected in some layouts)`);
    }
  }

  await page.waitForTimeout(3000);
}

async function demonstrateLegalChat(page) {
  console.log('💬 Testing Legal Chat Interface...');

  // Navigate to chat
  try {
    await page.click('a[href="/chat"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Take screenshot of chat interface
    await page.screenshot({ path: 'screenshots/chat-interface.png', fullPage: true });

    // Test model selection dropdown
    console.log('   🔧 Testing model selection...');
    try {
      // Look for the model selector button (flex items-center with ChevronDown)
      await page.click('button:has-text("Select Model")');
      await page.waitForTimeout(1000);
      await page.screenshot({ path: 'screenshots/model-dropdown.png' });

      // Select a different model
      await page.click('text="Claude 3 Opus"');
      await page.waitForTimeout(1000);
    } catch (e) {
      console.log('   - Model selector not found, trying alternative...');
      // Try alternative selectors
      const modelSelectors = [
        'button[class*="bg-gray-100"]',
        '.model-dropdown button',
        'button:has-text("GPT-4o Mini")'
      ];

      for (const selector of modelSelectors) {
        try {
          await page.click(selector);
          await page.waitForTimeout(1000);
          await page.screenshot({ path: 'screenshots/model-dropdown-alt.png' });
          break;
        } catch (e2) {
          continue;
        }
      }
    }

    // Test typing in chat input
    console.log('   ✍️  Testing chat input...');
    const chatInput = await page.locator('textarea[placeholder*="legal question"]').first();
    if (await chatInput.count() === 0) {
      // Try alternative selector for the chat input
      const altInput = await page.locator('textarea').first();
      await altInput.fill('What are the key elements of a valid contract?');
    } else {
      await chatInput.fill('What are the key elements of a valid contract?');
    }
    await page.waitForTimeout(2000);

    // Take screenshot of filled input
    await page.screenshot({ path: 'screenshots/chat-input-filled.png' });

    // Test document selector
    console.log('   📄 Testing document selection...');
    try {
      // Look for document icon button
      await page.click('button[class*="text-gray-400"]:has(svg)');
      await page.waitForTimeout(1000);
      await page.screenshot({ path: 'screenshots/document-selector.png' });
    } catch (e) {
      console.log('   - Document selector button not found');
      // Try alternative selector
      try {
        await page.click('button:has-text("Attach")');
        await page.waitForTimeout(1000);
      } catch (e2) {
        console.log('   - Alternative document selector not found');
      }
    }

    await page.waitForTimeout(3000);

  } catch (error) {
    console.log('   ⚠️  Chat interface navigation failed, continuing...');
  }
}

async function demonstrateDocumentUpload(page) {
  console.log('📁 Testing Document Upload...');

  try {
    await page.click('a[href="/upload"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Take screenshot of upload interface
    await page.screenshot({ path: 'screenshots/upload-interface.png', fullPage: true });

    // Test drag and drop area
    console.log('   🎯 Testing upload area interaction...');
    const uploadArea = await page.locator('.border-2.border-dashed').first();
    if (await uploadArea.count() > 0) {
      await uploadArea.hover();
      await page.waitForTimeout(1000);
      await page.screenshot({ path: 'screenshots/upload-hover.png' });
    } else {
      console.log('   - Upload area not found, taking general screenshot');
      await page.screenshot({ path: 'screenshots/upload-interface-alt.png' });
    }

    // Test file input
    console.log('   📎 Testing file input...');
    try {
      const fileInput = await page.locator('input[type="file"]').first();
      await fileInput.setInputFiles('./test-files/sample-contract.txt');
      await page.waitForTimeout(2000);
      console.log('   ✓ Test file uploaded successfully');
    } catch (e) {
      console.log('   - File input not accessible (expected in demo environment)');
    }

    await page.waitForTimeout(3000);

  } catch (error) {
    console.log('   ⚠️  Upload interface navigation failed, continuing...');
  }
}

async function demonstrateAgentManagement(page) {
  console.log('🤖 Testing Agent Management...');

  try {
    await page.click('a[href="/agents"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Take screenshot of agent dashboard
    await page.screenshot({ path: 'screenshots/agent-dashboard.png', fullPage: true });

    // Look for agent statistics
    console.log('   📊 Checking agent statistics...');
    try {
      const stats = await page.locator('.bg-white.shadow.rounded-lg.p-4').all();
      console.log(`   ✓ Found ${stats.length} statistic cards`);
    } catch (e) {
      console.log('   - Statistics cards not found, trying grid layout...');
      try {
        const gridStats = await page.locator('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 .bg-white').all();
        console.log(`   ✓ Found ${gridStats.length} statistic cards in grid`);
      } catch (e2) {
        console.log('   - Grid statistics not found either');
      }
    }

    // Test refresh functionality
    console.log('   🔄 Testing refresh functionality...');
    try {
      await page.click('button:has-text("Refresh")');
      await page.waitForTimeout(2000);
      console.log('   ✓ Refresh button clicked');
    } catch (e) {
      console.log('   - Refresh button not found');
    }

    await page.waitForTimeout(3000);

  } catch (error) {
    console.log('   ⚠️  Agent management navigation failed, continuing...');
  }
}

async function demonstrateDocumentComparison(page) {
  console.log('🔍 Testing Document Comparison...');

  try {
    await page.click('a[href="/compare"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Take screenshot of comparison interface
    await page.screenshot({ path: 'screenshots/comparison-interface.png', fullPage: true });

    // Look for comparison controls
    console.log('   🎛️  Checking comparison controls...');
    try {
      const buttons = await page.locator('button').all();
      console.log(`   ✓ Found ${buttons.length} interactive elements`);
    } catch (e) {
      console.log('   - Comparison controls not found');
    }

    await page.waitForTimeout(3000);

  } catch (error) {
    console.log('   ⚠️  Document comparison navigation failed, continuing...');
  }
}

async function demonstrateMonitoring(page) {
  console.log('📈 Testing Monitoring Dashboard...');

  try {
    await page.click('a[href="/monitoring"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Take screenshot of monitoring dashboard
    await page.screenshot({ path: 'screenshots/monitoring-dashboard.png', fullPage: true });

    // Look for monitoring metrics
    console.log('   📊 Checking monitoring metrics...');
    try {
      const metrics = await page.locator('.bg-white.shadow.rounded-lg.p-6').all();
      console.log(`   ✓ Found ${metrics.length} monitoring panels`);
    } catch (e) {
      console.log('   - Monitoring panels not found, trying alternative layout...');
      try {
        const altMetrics = await page.locator('.grid.grid-cols-1.lg\\:grid-cols-2 .bg-white').all();
        console.log(`   ✓ Found ${altMetrics.length} monitoring panels in grid`);
      } catch (e2) {
        console.log('   - Alternative monitoring panels not found');
      }
    }

    // Test refresh functionality
    console.log('   🔄 Testing monitoring refresh...');
    try {
      await page.click('button:has-text("Refresh")');
      await page.waitForTimeout(2000);
      console.log('   ✓ Monitoring refresh clicked');
    } catch (e) {
      console.log('   - Monitoring refresh button not found');
    }

    await page.waitForTimeout(3000);

  } catch (error) {
    console.log('   ⚠️  Monitoring dashboard navigation failed, continuing...');
  }
}

// Create directories for outputs
const fs = require('fs');
if (!fs.existsSync('./screenshots')) {
  fs.mkdirSync('./screenshots');
}
if (!fs.existsSync('./demo-videos')) {
  fs.mkdirSync('./demo-videos');
}
if (!fs.existsSync('./test-files')) {
  fs.mkdirSync('./test-files');
}

// Run the demo
runAIDemo().catch(console.error);