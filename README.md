# 🤖 AI Law Firm - Enterprise Legal Document Analysis Platform

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://postgresql.org)
[![MongoDB](https://img.shields.io/badge/MongoDB-7+-green.svg)](https://mongodb.com)
[![Redis](https://img.shields.io/badge/Redis-7+-red.svg)](https://redis.io)
[![Qdrant](https://img.shields.io/badge/Qdrant-v1.7+-purple.svg)](https://qdrant.tech)

> **Enterprise-grade AI-powered legal document analysis with comprehensive data persistence**

## 📋 Table of Contents

- [🚀 Quick Start](#-quick-start)
- [🏗️ Architecture Overview](#️-architecture-overview)
- [🗄️ Data Persistence](#️-data-persistence)
- [🐳 Docker Setup](#-docker-setup)
- [📊 Features](#-features)
- [🔧 Installation](#-installation)
- [📚 Documentation](#-documentation)
- [🧪 Testing](#-testing)
- [🚀 Deployment](#-deployment)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)

## 🚀 Quick Start

### One-Command Setup

```bash
# Clone the repository
git clone <repository-url>
cd ai-law-firm

# Start all services
cd docker && docker-compose up -d

# Run automated setup
python ../scripts/setup_databases.py

# Start the application
python legal_agent_team_refactored.py
```

### Manual Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Configure environment
cp docker/.env.example docker/.env
# Edit docker/.env with your settings

# Start databases
cd docker && docker-compose up -d

# Initialize databases
python ../scripts/setup_databases.py

# Run the application
python legal_agent_team_refactored.py
```

## 🏗️ Architecture Overview

The AI Law Firm is a **modular, enterprise-grade** legal document analysis platform built with:

- **Multi-Agent AI System**: Specialized legal agents for different analysis types
- **Multi-Database Architecture**: PostgreSQL + MongoDB + Redis + Qdrant
- **Modular Design**: Clean separation of concerns with service layers
- **Production-Ready**: Comprehensive error handling, monitoring, and security

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │  Orchestration   │    │  AI Providers   │
│   (Streamlit)   │◄──►│   Services      │◄──►│  (OpenAI, etc.) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  PostgreSQL     │    │    MongoDB      │    │     Redis       │
│  (Relational)   │    │  (Documents)    │    │   (Cache)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │    Qdrant       │
                    │  (Vectors)      │
                    └─────────────────┘
```

## 🗄️ Data Persistence

**Complete data capture** of every document, question, and answer with enterprise-grade persistence:

### Databases Used
- **PostgreSQL**: User accounts, sessions, metadata, audit logs, usage statistics
- **MongoDB**: Document content, analysis results, complex unstructured data
- **Redis**: Session management, caching, performance metrics, rate limiting
- **Qdrant**: Vector embeddings for semantic search and document similarity

### Key Features
- ✅ **Every document uploaded** → Stored with metadata, content, and vector embeddings
- ✅ **Every question asked** → Full query history with context and metadata
- ✅ **Every answer given** → Complete AI responses with confidence scores and sources
- ✅ **Automated backups** → Multiple formats (JSON, CSV, ZIP) with retention policies
- ✅ **Data export/import** → Migration and portability capabilities
- ✅ **Real-time analytics** → Usage statistics and performance monitoring

## 🐳 Docker Setup

### Conflict-Free Port Configuration

```yaml
# docker/docker-compose.yml
services:
  postgres:
    ports: ["54320:5432"]  # PostgreSQL
  redis:
    ports: ["63790:6379"]  # Redis
  qdrant:
    ports: ["63330:6333", "63331:6334"]  # Qdrant gRPC/REST
```

### Management Tools Included
- **PgAdmin**: PostgreSQL management interface
- **Mongo Express**: MongoDB web-based admin
- **Redis Commander**: Redis management interface

## 📊 Features

### 🤖 AI-Powered Analysis
- **Multi-Agent System**: Specialized legal agents (Researcher, Analyst, Strategist)
- **Intelligent Routing**: Automatic agent selection based on document type and query
- **Cost Optimization**: Smart model selection and token management
- **Quality Assurance**: Cross-validation and confidence scoring

### 📄 Document Processing
- **Multi-Format Support**: PDF, DOCX, TXT with automatic text extraction
- **Smart Chunking**: Intelligent document segmentation for analysis
- **Metadata Extraction**: Automatic classification and tagging
- **Version Control**: Document history and change tracking

### 🔍 Advanced Search
- **Semantic Search**: Vector-based similarity matching
- **Full-Text Search**: Keyword and phrase searching
- **Hybrid Search**: Combine semantic and keyword search
- **Filtered Results**: Metadata-based result filtering

### 📊 Analytics & Reporting
- **Usage Statistics**: Comprehensive user and system analytics
- **Performance Metrics**: Response times, costs, and quality scores
- **Audit Trails**: Complete activity logging and compliance reporting
- **Export Capabilities**: Data export in multiple formats

### 🔒 Enterprise Security
- **Encrypted Data**: API keys and sensitive information encrypted
- **Role-Based Access**: User permissions and access control
- **Audit Logging**: Complete activity tracking and compliance
- **Secure Backups**: Encrypted backup storage and transmission

## 🔧 Installation

### Prerequisites
- Python 3.9+
- Docker & Docker Compose
- 8GB+ RAM recommended
- 20GB+ disk space

### Step-by-Step Installation

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd ai-law-firm
   ```

2. **Install Python Dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

3. **Configure Environment**
   ```bash
   cp docker/.env.example docker/.env
   # Edit docker/.env with your API keys and settings
   ```

4. **Start Database Services**
   ```bash
   cd docker
   docker-compose up -d
   ```

5. **Initialize Databases**
   ```bash
   python ../scripts/setup_databases.py
   ```

6. **Configure Application**
   ```python
   from src.core.services.data_persistence_service import DataPersistenceService
   from src.core.config.settings import get_config

   # Initialize the system
   config = get_config()
   persistence = DataPersistenceService(config)
   await persistence.initialize()
   ```

7. **Start Application**
   ```bash
   python legal_agent_team_refactored.py
   ```

## 📚 Documentation

### 📖 Complete Documentation Suite

```
docs/
├── README.md                    # Main project documentation
├── CLAUDE.md                    # Development guide
├── DATA_PERSISTENCE.md          # Database architecture guide
├── architecture/
│   ├── ARCHITECTURE.md          # System architecture overview
│   └── REFACTORING_SUMMARY.md   # Refactoring progress and plans
├── testing/
│   ├── TESTING_STRATEGY.md      # Testing philosophy and approach
│   ├── TEST_FRAMEWORK.md        # Test implementation details
│   └── TEST_DATA_REQUIREMENTS.md # Test data management
└── {api,deployment,development}/ # Additional documentation
```

### 🔗 Key Documentation Links

- **[Data Persistence Guide](docs/DATA_PERSISTENCE.md)** - Complete database architecture
- **[Architecture Overview](docs/architecture/ARCHITECTURE.md)** - System design and components
- **[Testing Strategy](docs/testing/TESTING_STRATEGY.md)** - Quality assurance approach
- **[API Documentation](docs/api/)** - Service interfaces and endpoints

## 🧪 Testing

### Test Coverage
- **Unit Tests**: 70-80% coverage of individual components
- **Integration Tests**: Component interaction and data flow
- **UI Tests**: Streamlit component testing
- **Performance Tests**: Load testing and benchmarking

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest tests/ui/        # UI tests only

# Run performance tests
pytest tests/performance/ --benchmark-only
```

### Test Data Management

The system includes comprehensive test data:
- Sample legal documents (contracts, briefs, court documents)
- Mock AI responses and API interactions
- Performance benchmarking datasets
- Automated test data generation utilities

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**
   ```bash
   export APP_ENV=production
   export DEBUG=false
   # Configure production database credentials
   ```

2. **Docker Production Setup**
   ```bash
   cd docker
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Load Balancing**
   - Use reverse proxy (nginx/caddy) for SSL termination
   - Configure horizontal scaling with multiple app instances
   - Set up Redis cluster for high availability

4. **Monitoring Setup**
   - Configure application metrics collection
   - Set up log aggregation and alerting
   - Implement health checks and auto-healing

### Scaling Considerations

- **Horizontal Scaling**: Stateless application design
- **Database Scaling**: Connection pooling and read replicas
- **Cache Scaling**: Redis cluster configuration
- **Storage Scaling**: Distributed file storage for documents

## 🤝 Contributing

### Development Workflow

1. **Fork and Clone**
   ```bash
   git clone <your-fork-url>
   cd ai-law-firm
   ```

2. **Set Up Development Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements-dev.txt
   ```

3. **Run Tests**
   ```bash
   pytest --cov=src
   ```

4. **Code Quality**
   ```bash
   black src/ tests/
   isort src/ tests/
   flake8 src/ tests/
   mypy src/
   ```

5. **Submit Pull Request**
   - Ensure all tests pass
   - Update documentation as needed
   - Follow commit message conventions

### Code Standards

- **Python**: PEP 8 with Black formatting
- **Documentation**: Google-style docstrings
- **Testing**: pytest with 80%+ coverage target
- **Git**: Conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Agno Framework**: Multi-agent orchestration
- **Qdrant**: Vector database for semantic search
- **Streamlit**: Web application framework
- **OpenAI**: AI model provider
- **PostgreSQL, MongoDB, Redis**: Database technologies

## 📞 Support

- **Documentation**: [Complete Documentation](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/discussions)

---

**Built with ❤️ for the legal technology community**

*Transforming legal document analysis with AI-powered intelligence and enterprise-grade reliability.*