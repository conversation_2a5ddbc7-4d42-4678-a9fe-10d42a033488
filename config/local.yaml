environment: local
debug: true
ai_provider: ollama
vector_database: qdrant_local

ollama:
  base_url: http://localhost:11434
  model: llama3.2:3b
  temperature: 0.1
  timeout: 60

qdrant_local:
  url: http://localhost:63331
  collection: legal_documents
  timeout: 30

postgres:
  host: localhost
  port: 54320
  db: ai_law_firm
  user: ai_law_user
  password: ai_law_password_2024

mongodb:
  host: localhost
  port: 27019
  db: ai_law_firm
  user: ai_law_admin
  password: ai_law_mongo_password_2024

redis:
  host: localhost
  port: 63790
  password: ai_law_redis_password_2024

document:
  max_size_mb: 50
  allowed_extensions:
    - pdf
    - docx
    - txt
  chunk_size: 1000
  chunk_overlap: 200
  supported_languages:
    - en

logging:
  level: DEBUG
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file_path: null
  max_file_size: 10485760
  backup_count: 5

security:
  secret_key: dummy-secret-key-for-development
  session_timeout: 3600
  max_login_attempts: 5
  rate_limit_requests: 100
  rate_limit_window: 60

model_roles:
  primary_analysis: llama3.2:3b
  secondary_review: llama3.2:3b
  final_polish: llama3.2:3b

agent_pool:
  min_agents: 3
  max_agents: 45
  concurrent_limit: 10
  agent_timeout: 300
  load_balancing: round_robin
