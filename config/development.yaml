environment: development
debug: true
ai_provider: openai
vector_database: qdrant_cloud
openai: !!python/object:__main__.OpenAIConfig
  api_key: your-openai-api-key-here
  temperature: 0.1
  max_tokens: 4000
  timeout: 30
qdrant_cloud: !!python/object:__main__.QdrantCloudConfig
  url: https://your-qdrant-instance.cloud.qdrant.io
  api_key: your-qdrant-api-key-here
  collection: legal_documents
  timeout: 30
document: !!python/object:__main__.DocumentConfig
  max_size_mb: 50
  allowed_extensions:
  - pdf
  - docx
  - txt
  chunk_size: 1000
  chunk_overlap: 200
  supported_languages:
  - en
logging: !!python/object:__main__.LoggingConfig
  level: DEBUG
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file_path: null
  max_file_size: 10485760
  backup_count: 5
security: !!python/object:__main__.SecurityConfig
  secret_key: 1f1bc7927318d973709e5f20be8f5d33aa41668ad7e0c3c1865eb90ba44e8035
  session_timeout: 3600
  max_login_attempts: 5
  rate_limit_requests: 100
  rate_limit_window: 60
