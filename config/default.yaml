environment: development
debug: false
ai_provider: openai
vector_database: qdrant_cloud

openai:
  api_key: your-openai-api-key-here
  temperature: 0.1
  max_tokens: 4000
  timeout: 30

qdrant_cloud:
  url: https://your-qdrant-instance.cloud.qdrant.io
  api_key: your-qdrant-api-key-here
  collection: legal_documents
  timeout: 30

document:
  max_size_mb: 50
  allowed_extensions:
    - pdf
    - docx
    - txt
  chunk_size: 1000
  chunk_overlap: 200
  supported_languages:
    - en

logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file_path: null
  max_file_size: 10485760
  backup_count: 5

security:
  secret_key: ec5e1273f3469a095471718b33c14f078154c05643fca0e2449352d804f14e23
  session_timeout: 3600
  max_login_attempts: 5
  rate_limit_requests: 100
  rate_limit_window: 60
