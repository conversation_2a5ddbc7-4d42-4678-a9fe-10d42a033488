# 🚀 AI Law Firm - Quick Start Guide

## 🎯 **Get Started in 3 Simple Steps**

---

## **Step 1: Start Everything Automatically**

```bash
# One-command startup (Recommended)
./start-dev.sh
```

This will:
- ✅ Start all Docker containers (PostgreSQL, MongoDB, Redis, Qdrant, PgAdmin, etc.)
- ✅ Launch the FastAPI backend server
- ✅ Start the React frontend development server
- ✅ Open your browser to the application

---

## **Step 2: Access Your AI Law Firm**

### **🌐 Web Interface**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

### **🐘 Database Management**
- **PgAdmin**: http://localhost:50500
- **Mongo Express**: http://localhost:8081
- **Redis Commander**: http://localhost:8082

---

## **Step 3: Try the Document Comparison Feature**

### **📤 Upload Documents**
1. Go to http://localhost:3000
2. Click the document upload area
3. Upload 2 legal documents (PDF, DOCX, TXT, etc.)

### **🔍 Compare Documents**
1. Select exactly 2 documents in the chat interface
2. Look for the green "Ready to compare!" message
3. Click the green "Compare" button
4. Enjoy WinMerge-style document comparison!

---

## 📋 **Manual Startup (Alternative)**

If you prefer to start services individually:

### **🐳 Start Databases**
```bash
cd docker
docker-compose -f docker-compose.databases.yml up -d
```

### **🔧 Start Backend**
```bash
cd ../src
python main.py
```

### **🎨 Start Frontend**
```bash
cd ../frontend
npm install  # Only needed first time
npm start
```

---

## 🔧 **System Requirements**

### **✅ Prerequisites**
- **Docker & Docker Compose** (latest versions)
- **Node.js** (v16 or higher)
- **Python** (v3.8 or higher)
- **4GB RAM** minimum (8GB recommended)
- **Modern web browser** (Chrome, Firefox, Safari, Edge)

### **📦 Dependencies**
- **Backend**: FastAPI, PostgreSQL, MongoDB, Redis, Qdrant
- **Frontend**: React, TypeScript, Tailwind CSS
- **AI**: OpenAI API or Ollama (local AI)

---

## 🎮 **Quick Demo Commands**

### **View System Status**
```bash
# Check all containers
cd docker && docker-compose ps

# View logs
cd docker && docker-compose logs -f

# Check backend health
curl http://localhost:8000/api/v1/health
```

### **Demo Document Comparison**
```bash
# Run the comparison demo
./demo-comparison.sh
```

### **Stop Everything**
```bash
# Stop all services
cd docker && docker-compose -f docker-compose.databases.yml -f docker-compose.tools.yml --profile tools down

# Kill background processes
pkill -f "python main.py"
pkill -f "npm start"
```

---

## 🚨 **Troubleshooting**

### **❌ "Permission denied"**
```bash
# Make scripts executable
chmod +x start-dev.sh
chmod +x demo-comparison.sh
```

### **❌ "Docker not running"**
```bash
# Start Docker service
sudo systemctl start docker  # Linux
# or open Docker Desktop on Mac/Windows
```

### **❌ "Port already in use"**
```bash
# Kill processes using ports
sudo lsof -ti:3000,8000,54320,27019,63790,63330 | xargs kill -9

# Or change ports in docker-compose.yml
```

### **❌ "Module not found"**
```bash
# Install backend dependencies
cd src && pip install -r ../requirements.txt

# Install frontend dependencies
cd ../frontend && npm install
```

### **❌ "AI service unavailable"**
```bash
# Set up OpenAI API key
export OPENAI_API_KEY="your-api-key-here"

# Or use local Ollama
# Install Ollama and run: ollama serve
```

---

## 📚 **What You Can Do Right Now**

### **💬 Chat with AI Legal Experts**
- Ask questions about contracts, laws, regulations
- Get analysis from 45 specialized legal agents
- Upload documents for context-aware responses

### **📄 Compare Legal Documents**
- Side-by-side document comparison (WinMerge-style)
- AI-powered analysis of differences
- Legal implications assessment
- Change tracking and version control

### **📊 Monitor System Performance**
- Real-time agent status and metrics
- Database performance monitoring
- API usage statistics
- System health dashboard

---

## 🎯 **Next Steps**

### **Immediate (5 minutes)**
1. ✅ Start the system with `./start-dev.sh`
2. ✅ Visit http://localhost:3000
3. ✅ Upload a legal document
4. ✅ Ask a legal question

### **Short Term (30 minutes)**
1. 📄 Try document comparison with 2 contracts
2. 🤖 Explore different AI legal agents
3. 📊 Check system monitoring dashboard
4. 🔍 Test advanced search features

### **Medium Term (1-2 hours)**
1. 📚 Read the comprehensive documentation
2. 🛠️ Customize agent configurations
3. 🔧 Set up production deployment
4. 📈 Analyze performance metrics

---

## 🎉 **You're All Set!**

**Your AI Law Firm is ready to revolutionize your legal workflow!** ⚖️🤖

### **🚀 Quick Commands Cheat Sheet**
```bash
# Start everything
./start-dev.sh

# Check status
cd docker && docker-compose ps

# View logs
cd docker && docker-compose logs -f

# Stop everything
cd docker && docker-compose down
```

### **📍 Important URLs**
- **🏠 Main App**: http://localhost:3000
- **📚 API Docs**: http://localhost:8000/docs
- **🐘 PgAdmin**: http://localhost:50500
- **🍃 MongoDB**: http://localhost:8081
- **🔴 Redis**: http://localhost:8082

---

## 💡 **Pro Tips**

1. **🔄 Auto-restart**: The system automatically restarts crashed services
2. **💾 Data persistence**: Your documents and conversations are saved
3. **⚡ Performance**: First load may take a minute, subsequent loads are fast
4. **🔒 Security**: All data is processed locally (unless you configure cloud AI)
5. **📱 Mobile**: The interface works great on tablets and phones

---

## 🆘 **Need Help?**

- **📖 Documentation**: Check `README.md` and `frontend/README.md`
- **🐛 Bug Reports**: Check logs with `docker-compose logs`
- **⚡ Performance**: Monitor with `docker stats`
- **🔧 Configuration**: Edit `src/core/config/settings.py`

**Happy legal AI exploring!** 🎯⚖️🚀