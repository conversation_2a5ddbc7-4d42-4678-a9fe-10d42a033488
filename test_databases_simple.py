#!/usr/bin/env python3
"""
Simple database connection test for AI Law Firm - Basic connectivity test.
"""

import asyncio
import sys
import os

async def test_basic_connectivity():
    """Test basic connectivity to all databases."""
    print("🔍 Testing Basic Database Connectivity")
    print("=" * 60)

    results = {}

    # Test PostgreSQL
    try:
        import asyncpg
        conn = await asyncpg.connect(
            host="localhost", port=54320,
            database="ai_law_firm", user="ai_law_user",
            password="ai_law_password_2024"
        )
        await conn.close()
        results["PostgreSQL"] = "✅ Connected successfully"
        print("✅ PostgreSQL: Connected successfully")
    except Exception as e:
        results["PostgreSQL"] = f"❌ Failed: {str(e)}"
        print(f"❌ PostgreSQL: {str(e)}")

    # Test MongoDB (without auth first)
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
        # Try without authentication first
        client = AsyncIOMotorClient("mongodb://localhost:27019/")
        await client.admin.command('ping')
        client.close()
        results["MongoDB"] = "✅ Connected successfully (no auth)"
        print("✅ MongoDB: Connected successfully (no auth)")
    except Exception as e:
        results["MongoDB"] = f"❌ Failed: {str(e)}"
        print(f"❌ MongoDB: {str(e)}")

    # Test Redis
    try:
        import redis
        r = redis.Redis(
            host="localhost", port=63790,
            password="ai_law_redis_password_2024"
        )
        r.ping()
        r.close()
        results["Redis"] = "✅ Connected successfully"
        print("✅ Redis: Connected successfully")
    except Exception as e:
        results["Redis"] = f"❌ Failed: {str(e)}"
        print(f"❌ Redis: {str(e)}")

    # Test Qdrant (try different endpoints)
    qdrant_endpoints = [
        "http://localhost:63331/health",
        "http://localhost:63331/",
        "http://localhost:63330/health"  # gRPC port might not work with HTTP
    ]

    for endpoint in qdrant_endpoints:
        try:
            import requests
            response = requests.get(endpoint, timeout=5)
            if response.status_code in [200, 404]:  # 404 is OK, means service is responding
                results["Qdrant"] = f"✅ Service responding on {endpoint}"
                print(f"✅ Qdrant: Service responding on {endpoint}")
                break
        except Exception:
            continue
    else:
        results["Qdrant"] = "❌ No response from any endpoint"
        print("❌ Qdrant: No response from any endpoint")

    # Summary
    print("\n" + "=" * 60)
    successful = sum(1 for result in results.values() if "✅" in result)
    total = len(results)

    print(f"📊 Results: {successful}/{total} databases accessible")
    print("\n🔗 Connection Details:")
    print("PostgreSQL: localhost:54320 (ai_law_firm)")
    print("MongoDB:    localhost:27019 (ai_law_firm)")
    print("Redis:      localhost:63790 (password protected)")
    print("Qdrant:     localhost:63330 (gRPC) / 63331 (REST)")

    if successful >= 3:
        print("\n🎉 SUCCESS: Core databases are working!")
        print("📝 Note: MongoDB auth may need additional setup")
        print("🚀 Your AI Law Firm data persistence is ready!")

    return results

if __name__ == "__main__":
    asyncio.run(test_basic_connectivity())