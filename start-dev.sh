#!/bin/bash

# AI Law Firm Development Startup Script
# This script starts both the backend and frontend in development mode

echo "🚀 Starting AI Law Firm Development Environment"
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if Python is installed
if ! command -v python &> /dev/null; then
    echo "❌ Python is not installed. Please install Python first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Start Docker containers
echo ""
echo "🐳 Starting Docker containers..."
cd docker
docker-compose -f docker-compose.databases.yml -f docker-compose.tools.yml --profile tools up -d

# Wait for containers to be healthy
echo "⏳ Waiting for containers to be ready..."
sleep 10

# Check container health
echo "🔍 Checking container health..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ Docker containers are running"
else
    echo "❌ Some containers failed to start"
    docker-compose logs
    exit 1
fi

# Start backend
echo ""
echo "🔧 Starting backend server..."
cd ../src
python main.py &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Start frontend
echo ""
echo "🎨 Starting frontend development server..."
cd ../frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 Development environment started successfully!"
echo ""
echo "📍 Access URLs:"
echo "   • Frontend:     http://localhost:3000"
echo "   • Backend API:  http://localhost:8000"
echo "   • API Docs:     http://localhost:8000/docs"
echo "   • PgAdmin:      http://localhost:50500"
echo "   • Mongo Express: http://localhost:8081"
echo "   • Redis Commander: http://localhost:8082"
echo ""
echo "🛑 To stop all services:"
echo "   docker-compose -f docker/docker-compose.databases.yml -f docker/docker-compose.tools.yml --profile tools down"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo "📝 Backend PID: $BACKEND_PID"
echo "📝 Frontend PID: $FRONTEND_PID"

# Wait for user interrupt
trap "echo '🛑 Shutting down...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; cd docker && docker-compose -f docker-compose.databases.yml -f docker-compose.tools.yml --profile tools down; exit" INT

# Keep script running
wait