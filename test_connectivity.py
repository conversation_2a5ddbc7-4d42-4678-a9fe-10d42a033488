#!/usr/bin/env python3
"""
Basic connectivity test script for AI Law Firm databases.
Tests PostgreSQL, MongoDB, and Redis connections.
"""

import asyncio
import sys
import os

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_database_connections():
    """Test connections to PostgreSQL, MongoDB, and Redis."""
    print("🔍 Testing Database Connections")
    print("=" * 60)

    results = {}

    # Test PostgreSQL
    print("Testing PostgreSQL...")
    try:
        import asyncpg
        conn = await asyncpg.connect(
            host="localhost",
            port=54320,
            database="ai_law_firm",
            user="ai_law_user",
            password="ai_law_password_2024"
        )
        # Test a simple query
        result = await conn.fetchval("SELECT version()")
        await conn.close()
        results["PostgreSQL"] = f"✅ Connected - {result[:50]}..."
        print(f"✅ PostgreSQL: Connected successfully")
    except Exception as e:
        results["PostgreSQL"] = f"❌ Failed: {str(e)}"
        print(f"❌ PostgreSQL: {str(e)}")

    # Test MongoDB
    print("Testing MongoDB...")
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
        client = AsyncIOMotorClient("mongodb://localhost:27019/")
        # Test connection
        await client.admin.command('ping')
        # Test database access
        db = client.ai_law_firm
        collections = await db.list_collection_names()
        client.close()
        results["MongoDB"] = f"✅ Connected - Found {len(collections)} collections"
        print(f"✅ MongoDB: Connected successfully")
    except Exception as e:
        results["MongoDB"] = f"❌ Failed: {str(e)}"
        print(f"❌ MongoDB: {str(e)}")

    # Test Redis
    print("Testing Redis...")
    try:
        import redis
        r = redis.Redis(
            host="localhost",
            port=63790,
            password="ai_law_redis_password_2024",
            decode_responses=True
        )
        # Test connection
        r.ping()
        # Test basic operations
        r.set("test_key", "test_value")
        value = r.get("test_key")
        r.delete("test_key")
        r.close()
        results["Redis"] = f"✅ Connected - Test operations successful"
        print(f"✅ Redis: Connected successfully")
    except Exception as e:
        results["Redis"] = f"❌ Failed: {str(e)}"
        print(f"❌ Redis: {str(e)}")

    # Summary
    print("\n" + "=" * 60)
    successful = sum(1 for result in results.values() if "✅" in result)
    total = len(results)

    print(f"📊 Results: {successful}/{total} databases connected")
    print("\n🔗 Connection Details:")
    print("PostgreSQL: localhost:54320 (ai_law_firm)")
    print("MongoDB:    localhost:27019 (ai_law_firm)")
    print("Redis:      localhost:63790 (password protected)")

    if successful == total:
        print("\n🎉 SUCCESS: All databases are ready for the AI Law Firm!")
        print("📝 Every document, question, and answer will be captured automatically")
    else:
        print(f"\n⚠️  PARTIAL: {successful}/{total} databases connected")
        print("Some databases may need additional configuration")

    return results

if __name__ == "__main__":
    asyncio.run(test_database_connections())