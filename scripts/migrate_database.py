#!/usr/bin/env python3
"""
Database Migration Script for AI Law Firm

This script handles database schema migrations and updates.
Run this when deploying new versions that require database changes.

Usage:
    python scripts/migrate_database.py

Environment Variables:
    DATABASE_URL: PostgreSQL connection string
    MONGODB_URL: MongoDB connection string
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.config.settings import get_config
from infrastructure.databases.postgres_repository import PostgresRepository
from infrastructure.databases.mongodb_repository import MongoDBRepository


class DatabaseMigrator:
    """Handles database migrations and schema updates."""

    def __init__(self):
        self.config = get_config()
        self.postgres = PostgresRepository(self.config)
        self.mongodb = MongoDBRepository(self.config)
        self.migrations_table = "schema_migrations"

    async def migrate_all(self):
        """Run all pending migrations."""
        print("🔄 Starting database migrations...")

        try:
            # Ensure migrations table exists
            await self.ensure_migrations_table()

            # Run PostgreSQL migrations
            await self.migrate_postgresql()
            print("✅ PostgreSQL migrations completed")

            # Run MongoDB migrations
            await self.migrate_mongodb()
            print("✅ MongoDB migrations completed")

            print("🎉 All migrations completed successfully!")

        except Exception as e:
            print(f"❌ Migration failed: {e}")
            raise

    async def ensure_migrations_table(self):
        """Ensure the migrations tracking table exists."""
        await self.postgres.execute(
            f"""
            CREATE TABLE IF NOT EXISTS {self.migrations_table} (
                migration_id VARCHAR(255) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                success BOOLEAN DEFAULT TRUE,
                error_message TEXT
            )
            """
        )

    async def is_migration_executed(self, migration_id: str) -> bool:
        """Check if a migration has already been executed."""
        result = await self.postgres.fetch_one(
            f"SELECT migration_id FROM {self.migrations_table} WHERE migration_id = $1",
            migration_id
        )
        return result is not None

    async def record_migration(self, migration_id: str, name: str, success: bool = True, error: str = None):
        """Record a migration execution."""
        await self.postgres.execute(
            f"""
            INSERT INTO {self.migrations_table} (migration_id, name, success, error_message)
            VALUES ($1, $2, $3, $4)
            """,
            migration_id, name, success, error
        )

    async def migrate_postgresql(self):
        """Run PostgreSQL migrations."""
        migrations = [
            {
                "id": "001_add_user_preferences",
                "name": "Add user preferences table",
                "sql": """
                    CREATE TABLE IF NOT EXISTS user_preferences (
                        user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
                        preference_key VARCHAR(100) NOT NULL,
                        preference_value TEXT,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        PRIMARY KEY (user_id, preference_key)
                    );

                    CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id
                    ON user_preferences(user_id);
                """
            },
            {
                "id": "002_add_document_versions",
                "name": "Add document versioning support",
                "sql": """
                    CREATE TABLE IF NOT EXISTS document_versions (
                        version_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        document_id UUID REFERENCES documents(document_id) ON DELETE CASCADE,
                        version_number INTEGER NOT NULL,
                        file_size_bytes BIGINT NOT NULL,
                        checksum VARCHAR(128) NOT NULL,
                        storage_path TEXT NOT NULL,
                        changes_description TEXT,
                        created_by UUID REFERENCES users(user_id),
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        UNIQUE(document_id, version_number)
                    );

                    CREATE INDEX IF NOT EXISTS idx_document_versions_document_id
                    ON document_versions(document_id);

                    -- Add version column to documents table
                    ALTER TABLE documents
                    ADD COLUMN IF NOT EXISTS current_version INTEGER DEFAULT 1;
                """
            },
            {
                "id": "003_add_analysis_templates",
                "name": "Add analysis templates support",
                "sql": """
                    CREATE TABLE IF NOT EXISTS analysis_templates (
                        template_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        document_type VARCHAR(50) NOT NULL,
                        template_config JSONB NOT NULL,
                        is_default BOOLEAN DEFAULT FALSE,
                        created_by UUID REFERENCES users(user_id),
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    );

                    CREATE INDEX IF NOT EXISTS idx_analysis_templates_type
                    ON analysis_templates(document_type);

                    -- Insert default templates
                    INSERT INTO analysis_templates (name, description, document_type, template_config, is_default)
                    VALUES
                        ('Standard Contract Review', 'Comprehensive contract analysis', 'contract',
                         '{"steps": ["risk_assessment", "compliance_check", "clause_analysis"]}', true),
                        ('NDA Analysis', 'Non-disclosure agreement review', 'nda',
                         '{"steps": ["confidentiality_check", "term_analysis", "jurisdiction_review"]}', true),
                        ('Employment Agreement', 'Employment contract analysis', 'employment',
                         '{"steps": ["compensation_review", "termination_clauses", "benefit_analysis"]}', true)
                    ON CONFLICT DO NOTHING;
                """
            },
            {
                "id": "004_add_usage_tracking_enhancements",
                "name": "Enhance usage tracking with detailed metrics",
                "sql": """
                    -- Add new columns to usage_stats
                    ALTER TABLE usage_stats
                    ADD COLUMN IF NOT EXISTS api_calls_detailed JSONB DEFAULT '{}',
                    ADD COLUMN IF NOT EXISTS model_usage JSONB DEFAULT '{}',
                    ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0;

                    -- Create detailed usage tracking table
                    CREATE TABLE IF NOT EXISTS detailed_usage (
                        usage_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
                        date DATE NOT NULL DEFAULT CURRENT_DATE,
                        hour INTEGER NOT NULL CHECK (hour >= 0 AND hour <= 23),
                        endpoint VARCHAR(255) NOT NULL,
                        method VARCHAR(10) NOT NULL,
                        response_code INTEGER NOT NULL,
                        response_time DECIMAL(5,2),
                        tokens_used INTEGER,
                        cost DECIMAL(8,4),
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    );

                    CREATE INDEX IF NOT EXISTS idx_detailed_usage_user_date
                    ON detailed_usage(user_id, date);

                    CREATE INDEX IF NOT EXISTS idx_detailed_usage_endpoint
                    ON detailed_usage(endpoint);
                """
            }
        ]

        for migration in migrations:
            if not await self.is_migration_executed(migration["id"]):
                print(f"🔄 Running migration: {migration['name']}")

                try:
                    await self.postgres.execute(migration["sql"])
                    await self.record_migration(migration["id"], migration["name"], True)
                    print(f"✅ Migration completed: {migration['name']}")

                except Exception as e:
                    error_msg = f"Migration failed: {str(e)}"
                    print(f"❌ {error_msg}")
                    await self.record_migration(migration["id"], migration["name"], False, error_msg)
                    raise

    async def migrate_mongodb(self):
        """Run MongoDB migrations."""
        migrations = [
            {
                "id": "001_add_document_embeddings",
                "name": "Add embedding support to documents",
                "action": self.add_embedding_support
            },
            {
                "id": "002_add_analysis_metadata",
                "name": "Add comprehensive metadata to analysis results",
                "action": self.add_analysis_metadata
            },
            {
                "id": "003_create_user_profiles",
                "name": "Create user profiles collection",
                "action": self.create_user_profiles
            }
        ]

        for migration in migrations:
            if not await self.is_migration_executed(migration["id"]):
                print(f"🔄 Running MongoDB migration: {migration['name']}")

                try:
                    await migration["action"]()
                    await self.record_migration(migration["id"], migration["name"], True)
                    print(f"✅ MongoDB migration completed: {migration['name']}")

                except Exception as e:
                    error_msg = f"MongoDB migration failed: {str(e)}"
                    print(f"❌ {error_msg}")
                    await self.record_migration(migration["id"], migration["name"], False, error_msg)
                    raise

    async def add_embedding_support(self):
        """Add embedding support to documents collection."""
        # Create indexes for embedding search
        await self.mongodb.create_index("documents", "embeddings", sparse=True)
        await self.mongodb.create_index("documents", [("document_type", 1), ("embeddings", 1)])

        # Add embedding field to existing documents if missing
        await self.mongodb.update_many(
            "documents",
            {"embeddings": {"$exists": False}},
            {"$set": {"embeddings": []}}
        )

    async def add_analysis_metadata(self):
        """Add comprehensive metadata to analysis results."""
        # Create indexes for better query performance
        await self.mongodb.create_index("analysis_results", "metadata.analysis_type")
        await self.mongodb.create_index("analysis_results", "metadata.confidence_threshold")
        await self.mongodb.create_index("analysis_results", [("created_at", -1), ("agent_name", 1)])

        # Add metadata field to existing analysis results if missing
        await self.mongodb.update_many(
            "analysis_results",
            {"metadata": {"$exists": False}},
            {"$set": {"metadata": {}}}
        )

    async def create_user_profiles(self):
        """Create user profiles collection for extended user data."""
        # Create the collection
        await self.mongodb.create_collection("user_profiles")

        # Create indexes
        await self.mongodb.create_index("user_profiles", "user_id", unique=True)
        await self.mongodb.create_index("user_profiles", "preferences.theme")
        await self.mongodb.create_index("user_profiles", "preferences.language")

        # Insert default profiles for existing users
        default_profiles = [
            {
                "user_id": "admin_user",
                "preferences": {
                    "theme": "dark",
                    "language": "en",
                    "timezone": "UTC",
                    "notifications": {
                        "email": True,
                        "analysis_complete": True,
                        "system_alerts": True
                    }
                },
                "statistics": {
                    "total_documents": 0,
                    "total_analyses": 0,
                    "account_created": datetime.utcnow(),
                    "last_active": datetime.utcnow()
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
        ]

        for profile in default_profiles:
            await self.mongodb.insert_one("user_profiles", profile)


async def main():
    """Main migration function."""
    print("🔄 AI Law Firm Database Migrator")
    print("=" * 50)

    # Check environment
    if os.getenv("APP_ENV") not in ["development", "local", "testing"]:
        print("⚠️  Warning: Not running in development/local/testing environment")
        response = input("Continue with migrations? (y/N): ")
        if response.lower() != 'y':
            print("Migration cancelled.")
            return

    # Initialize migrator
    migrator = DatabaseMigrator()

    try:
        await migrator.migrate_all()
        print("\n" + "=" * 50)
        print("✅ Database migrations completed successfully!")
        print("\n📋 Migration Summary:")
        print("   • PostgreSQL: User preferences, document versioning, analysis templates")
        print("   • PostgreSQL: Enhanced usage tracking with detailed metrics")
        print("   • MongoDB: Embedding support for documents")
        print("   • MongoDB: Comprehensive metadata for analysis results")
        print("   • MongoDB: User profiles collection with preferences")

    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())