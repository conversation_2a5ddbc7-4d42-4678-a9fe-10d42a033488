#!/usr/bin/env python3
"""
Database Seeding Script for AI Law Firm

This script populates the databases with sample data for testing and development.
Run this after the application has started and databases are initialized.

Usage:
    python scripts/seed_database.py

Environment Variables:
    DATABASE_URL: PostgreSQL connection string
    MONGODB_URL: MongoDB connection string
    REDIS_URL: Redis connection string
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.config.settings import get_config
from infrastructure.databases.postgres_repository import PostgresRepository
from infrastructure.databases.mongodb_repository import MongoDBRepository
from infrastructure.databases.redis_cache import RedisCache


class DatabaseSeeder:
    """Handles seeding of all databases with sample data."""

    def __init__(self):
        self.config = get_config()
        self.postgres = PostgresRepository(self.config)
        self.mongodb = MongoDBRepository(self.config)
        self.redis = RedisCache(self.config)

    async def seed_all(self):
        """Seed all databases with sample data."""
        print("🌱 Starting database seeding...")

        try:
            # Seed PostgreSQL
            await self.seed_postgresql()
            print("✅ PostgreSQL seeding completed")

            # Seed MongoDB
            await self.seed_mongodb()
            print("✅ MongoDB seeding completed")

            # Seed Redis
            await self.seed_redis()
            print("✅ Redis seeding completed")

            print("🎉 All databases seeded successfully!")

        except Exception as e:
            print(f"❌ Seeding failed: {e}")
            raise

    async def seed_postgresql(self):
        """Seed PostgreSQL with sample data."""
        # Sample users
        users_data = [
            {
                "email": "<EMAIL>",
                "username": "john_lawyer",
                "full_name": "John Lawyer",
                "hashed_password": "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPjYQmHqXaSme",  # password123
                "is_active": True,
                "is_superuser": False
            },
            {
                "email": "<EMAIL>",
                "username": "sarah_paralegal",
                "full_name": "Sarah Paralegal",
                "hashed_password": "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPjYQmHqXaSme",  # password123
                "is_active": True,
                "is_superuser": False
            }
        ]

        for user_data in users_data:
            await self.postgres.execute(
                """
                INSERT INTO users (email, username, full_name, hashed_password, is_active, is_superuser)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (email) DO NOTHING
                """,
                user_data["email"],
                user_data["username"],
                user_data["full_name"],
                user_data["hashed_password"],
                user_data["is_active"],
                user_data["is_superuser"]
            )

        # Sample documents metadata
        documents_data = [
            {
                "filename": "service_agreement_001.pdf",
                "original_filename": "Service Agreement Template.pdf",
                "file_size_bytes": 245760,
                "mime_type": "application/pdf",
                "document_type": "contract",
                "checksum": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
                "storage_path": "/storage/documents/00/0008951a-3986-44f4-96ff-18660f4a013e/service_agreement_001.pdf",
                "processing_status": "completed"
            },
            {
                "filename": "nda_template_001.docx",
                "original_filename": "NDA Template.docx",
                "file_size_bytes": 189440,
                "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "document_type": "nda",
                "checksum": "b775a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae4",
                "storage_path": "/storage/documents/0b/0b2641b8-76b3-4d37-a1bb-c856d1bf2fde/nda_template_001.docx",
                "processing_status": "completed"
            }
        ]

        for doc_data in documents_data:
            await self.postgres.execute(
                """
                INSERT INTO documents (
                    filename, original_filename, file_size_bytes, mime_type,
                    document_type, checksum, storage_path, processing_status
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT DO NOTHING
                """,
                doc_data["filename"],
                doc_data["original_filename"],
                doc_data["file_size_bytes"],
                doc_data["mime_type"],
                doc_data["document_type"],
                doc_data["checksum"],
                doc_data["storage_path"],
                doc_data["processing_status"]
            )

        # Sample API keys
        api_keys_data = [
            {
                "provider": "openai",
                "key_name": "Development Key",
                "encrypted_key": "encrypted_openai_key_placeholder",
                "is_active": True
            },
            {
                "provider": "anthropic",
                "key_name": "Development Key",
                "encrypted_key": "encrypted_anthropic_key_placeholder",
                "is_active": True
            }
        ]

        for key_data in api_keys_data:
            await self.postgres.execute(
                """
                INSERT INTO api_keys (user_id, provider, key_name, encrypted_key, is_active)
                SELECT u.user_id, $2, $3, $4, $5
                FROM users u WHERE u.email = '<EMAIL>'
                ON CONFLICT DO NOTHING
                """,
                key_data["provider"],
                key_data["key_name"],
                key_data["encrypted_key"],
                key_data["is_active"]
            )

    async def seed_mongodb(self):
        """Seed MongoDB with sample data."""
        # Sample documents content
        documents_content = [
            {
                "document_id": "service_agreement_001",
                "user_id": "admin_user",
                "title": "Service Agreement Template",
                "content": """
                SERVICE AGREEMENT

                This Service Agreement (the "Agreement") is entered into as of the Effective Date by and between:

                Provider: AI Law Firm Services, Inc.
                Address: 123 Legal Street, Law City, LC 12345

                Client: [Client Name]
                Address: [Client Address]

                1. SERVICES
                Provider agrees to provide the following services to Client:
                - Legal document analysis and review
                - Contract drafting assistance
                - Compliance checking
                - Legal research support

                2. COMPENSATION
                Client agrees to pay Provider the sum of $500.00 per hour for services rendered.

                3. TERM
                This Agreement shall commence on the Effective Date and continue for a period of one (1) year.

                4. GOVERNING LAW
                This Agreement shall be governed by the laws of the State of [State].

                IN WITNESS WHEREOF, the parties have executed this Agreement as of the Effective Date.
                """,
                "document_type": "contract",
                "tags": ["service", "agreement", "template"],
                "metadata": {
                    "word_count": 245,
                    "language": "en",
                    "processing_status": "completed",
                    "sections": ["services", "compensation", "term", "governing_law"]
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            },
            {
                "document_id": "nda_template_001",
                "user_id": "admin_user",
                "title": "Non-Disclosure Agreement Template",
                "content": """
                NON-DISCLOSURE AGREEMENT

                This Non-Disclosure Agreement (the "Agreement") is entered into as of [Date] by and between:

                Disclosing Party: [Company Name]
                Address: [Company Address]

                Receiving Party: [Recipient Name]
                Address: [Recipient Address]

                1. CONFIDENTIAL INFORMATION
                "Confidential Information" means any information disclosed by Disclosing Party to Receiving Party.

                2. OBLIGATIONS OF RECEIVING PARTY
                Receiving Party agrees to:
                a) Hold the Confidential Information in strict confidence
                b) Not disclose Confidential Information to any third party
                c) Use Confidential Information solely for the purpose of [Purpose]

                3. TERM
                This Agreement shall remain in effect for a period of five (5) years from the date hereof.

                4. RETURN OF MATERIALS
                Upon termination of this Agreement, Receiving Party shall return all Confidential Information.

                IN WITNESS WHEREOF, the parties have executed this Agreement.
                """,
                "document_type": "nda",
                "tags": ["nda", "confidentiality", "template"],
                "metadata": {
                    "word_count": 189,
                    "language": "en",
                    "processing_status": "completed",
                    "sections": ["confidential_information", "obligations", "term", "return"]
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
        ]

        # Insert documents
        for doc in documents_content:
            await self.mongodb.insert_one("documents", doc)

        # Sample analysis results
        analysis_results = [
            {
                "session_id": "analysis_session_001",
                "document_id": "service_agreement_001",
                "agent_name": "contract_analyst",
                "model_used": "gpt-4o-mini",
                "confidence_score": 0.92,
                "processing_time": 2.34,
                "token_count": 1250,
                "cost_estimate": 0.0045,
                "analysis_type": "comprehensive_review",
                "results": {
                    "risk_level": "low",
                    "issues_found": 2,
                    "recommendations": [
                        "Consider adding force majeure clause",
                        "Specify governing law jurisdiction"
                    ],
                    "compliance_score": 0.88
                },
                "created_at": datetime.utcnow()
            },
            {
                "session_id": "analysis_session_002",
                "document_id": "nda_template_001",
                "agent_name": "legal_researcher",
                "model_used": "gpt-4o-mini",
                "confidence_score": 0.89,
                "processing_time": 1.87,
                "token_count": 890,
                "cost_estimate": 0.0032,
                "analysis_type": "legal_research",
                "results": {
                    "relevant_case_law": ["Smith v. Jones (2020)", "ABC Corp v. XYZ Inc (2019)"],
                    "statutory_references": ["17 U.S.C. § 101", "35 U.S.C. § 271"],
                    "risk_assessment": "Standard NDA language appears appropriate"
                },
                "created_at": datetime.utcnow()
            }
        ]

        # Insert analysis results
        for result in analysis_results:
            await self.mongodb.insert_one("analysis_results", result)

        # Sample knowledge base entries
        knowledge_entries = [
            {
                "document_id": "service_agreement_001",
                "chunk_id": "chunk_001",
                "content": "This Service Agreement is entered into as of the Effective Date by and between Provider and Client for legal document analysis services.",
                "embedding": [],  # Would be populated by embedding service
                "metadata": {
                    "chunk_index": 0,
                    "total_chunks": 3,
                    "tags": ["service", "agreement", "introduction"],
                    "section": "preamble"
                },
                "created_at": datetime.utcnow()
            },
            {
                "document_id": "nda_template_001",
                "chunk_id": "chunk_002",
                "content": "Receiving Party agrees to hold Confidential Information in strict confidence and not disclose to any third party without prior written consent.",
                "embedding": [],  # Would be populated by embedding service
                "metadata": {
                    "chunk_index": 0,
                    "total_chunks": 2,
                    "tags": ["nda", "confidentiality", "obligations"],
                    "section": "obligations"
                },
                "created_at": datetime.utcnow()
            }
        ]

        # Insert knowledge base entries
        for entry in knowledge_entries:
            await self.mongodb.insert_one("knowledge_base", entry)

    async def seed_redis(self):
        """Seed Redis with sample cache data."""
        # Sample cache entries
        cache_entries = {
            "user:<EMAIL>:profile": json.dumps({
                "user_id": "admin_user",
                "email": "<EMAIL>",
                "full_name": "System Administrator",
                "role": "admin"
            }),
            "config:ai_provider": "openai",
            "config:max_file_size": "52428800",  # 50MB
            "stats:total_documents": "2",
            "stats:total_analyses": "2",
            "cache:document:service_agreement_001": json.dumps({
                "title": "Service Agreement Template",
                "type": "contract",
                "last_accessed": datetime.utcnow().isoformat()
            })
        }

        # Set cache entries with TTL
        for key, value in cache_entries.items():
            await self.redis.set(key, value, ttl=3600)  # 1 hour TTL

        # Sample rate limiting data
        rate_limit_keys = [
            "ratelimit:api:<EMAIL>",
            "ratelimit:documents:<EMAIL>"
        ]

        for key in rate_limit_keys:
            await self.redis.set(key, "0", ttl=3600)


async def main():
    """Main seeding function."""
    print("🚀 AI Law Firm Database Seeder")
    print("=" * 50)

    # Check environment
    if os.getenv("APP_ENV") not in ["development", "local", "testing"]:
        print("⚠️  Warning: Not running in development/local/testing environment")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Seeding cancelled.")
            return

    # Initialize seeder
    seeder = DatabaseSeeder()

    try:
        await seeder.seed_all()
        print("\n" + "=" * 50)
        print("✅ Database seeding completed successfully!")
        print("\n📊 Sample Data Summary:")
        print("   • 2 Users (admin + regular users)")
        print("   • 2 Sample Documents (contract + NDA)")
        print("   • API Keys for OpenAI and Anthropic")
        print("   • Analysis Results and Knowledge Base entries")
        print("   • Redis cache entries with TTL")

    except Exception as e:
        print(f"\n❌ Seeding failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())