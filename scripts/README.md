# AI Law Firm Database Scripts

This directory contains scripts for database management, seeding, and migrations.

## 📋 Available Scripts

### 1. `seed_database.py`
**Purpose**: Populate databases with sample data for development and testing

**Usage**:
```bash
# Basic seeding
python scripts/seed_database.py

# With environment variables
APP_ENV=development python scripts/seed_database.py
```

**What it creates**:
- **PostgreSQL**: Sample users, documents, API keys, usage stats
- **MongoDB**: Document content, analysis results, knowledge base entries
- **Redis**: Cache entries, rate limiting data, user sessions

**Sample Data**:
- 2 Users (admin + regular users)
- 2 Sample Documents (Service Agreement + NDA Template)
- API Keys for OpenAI and Anthropic
- Analysis Results with confidence scores
- Knowledge Base entries with embeddings placeholder

### 2. `migrate_database.py`
**Purpose**: Handle database schema migrations and updates

**Usage**:
```bash
# Run all migrations
python scripts/migrate_database.py

# With environment variables
APP_ENV=development python scripts/migrate_database.py
```

**Migrations Included**:

#### PostgreSQL Migrations:
1. **User Preferences Table** - Store user-specific settings
2. **Document Versioning** - Track document changes over time
3. **Analysis Templates** - Predefined analysis configurations
4. **Enhanced Usage Tracking** - Detailed API usage metrics

#### MongoDB Migrations:
1. **Embedding Support** - Add vector embeddings to documents
2. **Analysis Metadata** - Comprehensive analysis result metadata
3. **User Profiles** - Extended user information and preferences

## 🔧 Prerequisites

### Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export APP_ENV=development
export DATABASE_URL="postgresql://user:password@localhost:54320/ai_law_firm"
export MONGODB_URL="mongodb://localhost:27019"
export REDIS_URL="redis://localhost:63790"
```

### Database Setup
Ensure all databases are running:
```bash
# Start databases
docker-compose -f docker/docker-compose.databases.yml up -d

# Verify connections
docker-compose -f docker/docker-compose.databases.yml ps
```

## 🚀 Quick Start

### Development Setup
```bash
# 1. Start databases
docker-compose -f docker/docker-compose.databases.yml up -d

# 2. Run migrations (creates schema)
python scripts/migrate_database.py

# 3. Seed with sample data
python scripts/seed_database.py

# 4. Start the application
cd src && python main.py
```

### Production Setup
```bash
# 1. Backup existing data (if any)
# 2. Run migrations
APP_ENV=production python scripts/migrate_database.py

# 3. Seed minimal data (optional)
APP_ENV=production python scripts/seed_database.py
```

## 📊 Data Overview

### PostgreSQL Tables
- `users` - User accounts and authentication
- `documents` - Document metadata and references
- `analysis_sessions` - Analysis job tracking
- `analysis_results` - Analysis outcomes metadata
- `api_keys` - AI provider API keys
- `usage_stats` - Usage tracking and billing
- `user_preferences` - User-specific settings
- `document_versions` - Document change history
- `analysis_templates` - Predefined analysis configs

### MongoDB Collections
- `documents` - Full document content and metadata
- `analysis_results` - Complete analysis results
- `knowledge_base` - Chunked content with embeddings
- `agent_interactions` - Agent conversation logs
- `user_profiles` - Extended user information

### Redis Keys
- `user:{email}:profile` - User profile cache
- `cache:document:{id}` - Document metadata cache
- `ratelimit:*` - Rate limiting counters
- `config:*` - Application configuration cache

## 🔍 Troubleshooting

### Common Issues

#### Permission Denied
```bash
# Fix script permissions
chmod +x scripts/*.py
```

#### Database Connection Failed
```bash
# Check database status
docker-compose -f docker/docker-compose.databases.yml ps

# View database logs
docker-compose -f docker/docker-compose.databases.yml logs postgres
docker-compose -f docker/docker-compose.databases.yml logs mongodb
```

#### Migration Already Executed
```bash
# Check migration status
psql -h localhost -p 54320 -U ai_law_user -d ai_law_firm -c "SELECT * FROM schema_migrations;"

# Manually mark migration as executed (if needed)
psql -h localhost -p 54320 -U ai_law_user -d ai_law_firm -c "INSERT INTO schema_migrations (migration_id, name) VALUES ('001_add_user_preferences', 'Manually marked');"
```

### Environment Variables
```bash
# Required for database connections
export DATABASE_URL="postgresql://ai_law_user:ai_law_password_2024@localhost:54320/ai_law_firm"
export MONGODB_URL="mongodb://localhost:27019"
export REDIS_URL="redis://localhost:63790"

# Optional configuration
export APP_ENV="development"  # development, production, local, testing
export DEBUG="true"           # Enable debug mode
```

## 📈 Monitoring

### Check Seeding Results
```bash
# PostgreSQL
psql -h localhost -p 54320 -U ai_law_user -d ai_law_firm -c "SELECT COUNT(*) FROM users;"
psql -h localhost -p 54320 -U ai_law_user -d ai_law_firm -c "SELECT COUNT(*) FROM documents;"

# MongoDB
mongosh --host localhost --port 27019 ai_law_firm --eval "db.documents.countDocuments()"

# Redis
redis-cli -p 63790 KEYS "*"
```

### Performance Monitoring
```bash
# Database connection pool status
psql -h localhost -p 54320 -U ai_law_user -d ai_law_firm -c "SELECT * FROM pg_stat_activity;"

# MongoDB performance
mongosh --host localhost --port 27019 ai_law_firm --eval "db.serverStatus().connections"
```

## 🔄 Rollback Procedures

### PostgreSQL Rollback
```sql
-- Check migration history
SELECT * FROM schema_migrations ORDER BY executed_at DESC;

-- Remove specific migration (if needed)
DELETE FROM schema_migrations WHERE migration_id = '001_add_user_preferences';

-- Drop tables (if needed)
DROP TABLE IF EXISTS user_preferences;
```

### MongoDB Rollback
```javascript
// Connect to MongoDB
mongosh --host localhost --port 27019 ai_law_firm

// Remove indexes (if needed)
db.documents.dropIndex("embeddings_1");

// Remove collections (if needed)
db.user_profiles.drop();
```

## 📚 Additional Resources

- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [Redis Documentation](https://redis.io/documentation)
- [Docker Compose Documentation](https://docs.docker.com/compose/)

## 🤝 Contributing

When adding new migrations:
1. Follow the naming convention: `{number}_{description}`
2. Test migrations on a copy of production data first
3. Include rollback procedures
4. Update this README with new migration details