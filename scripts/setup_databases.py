#!/usr/bin/env python3
"""
Database Setup Script for AI Law Firm.

This script helps set up and initialize the multi-database architecture
for the AI Law Firm system, including PostgreSQL, MongoDB, Redis, and Qdrant.
"""

import asyncio
import sys
import os
from pathlib import Path
import subprocess
import time
from typing import Dict, Any

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.logging import get_logger, configure_logging
from core.config.settings import AppConfig


class DatabaseSetup:
    """Handles database setup and initialization."""

    def __init__(self):
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.project_root = Path(__file__).parent.parent

    def check_docker(self) -> bool:
        """Check if Docker is installed and running."""
        try:
            result = subprocess.run(
                ["docker", "--version"],
                capture_output=True,
                text=True,
                check=True
            )
            self.logger.info(f"Docker version: {result.stdout.strip()}")

            # Check if Docker daemon is running
            result = subprocess.run(
                ["docker", "info"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                self.logger.info("Docker daemon is running")
                return True
            else:
                self.logger.error("Docker daemon is not running")
                return False

        except (subprocess.CalledProcessError, FileNotFoundError):
            self.logger.error("Docker is not installed or not accessible")
            return False

    def check_docker_compose(self) -> bool:
        """Check if Docker Compose is available."""
        try:
            result = subprocess.run(
                ["docker-compose", "--version"],
                capture_output=True,
                text=True,
                check=True
            )
            self.logger.info(f"Docker Compose version: {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            # Try docker compose (newer syntax)
            try:
                result = subprocess.run(
                    ["docker", "compose", "version"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                self.logger.info(f"Docker Compose version: {result.stdout.strip()}")
                return True
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.logger.error("Docker Compose is not available")
                return False

    def start_databases(self) -> bool:
        """Start all database services using Docker Compose."""
        try:
            self.logger.info("Starting database services...")

            docker_compose_file = self.project_root / "docker" / "docker-compose.yml"
            if not docker_compose_file.exists():
                self.logger.error(f"Docker Compose file not found: {docker_compose_file}")
                return False

            # Change to docker directory
            os.chdir(self.project_root / "docker")

            # Start services
            cmd = ["docker-compose", "up", "-d"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                # Try with new syntax
                cmd = ["docker", "compose", "up", "-d"]
                result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info("Database services started successfully")
                return True
            else:
                self.logger.error(f"Failed to start database services: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"Error starting database services: {str(e)}")
            return False

    def wait_for_databases(self, timeout: int = 120) -> bool:
        """Wait for all databases to be ready."""
        self.logger.info("Waiting for databases to be ready...")

        services = {
            "PostgreSQL": ("localhost", 54320),
            "MongoDB": ("localhost", 27017),
            "Redis": ("localhost", 63790),
            "Qdrant": ("localhost", 63330)
        }

        start_time = time.time()

        for service_name, (host, port) in services.items():
            self.logger.info(f"Waiting for {service_name} on {host}:{port}")

            while time.time() - start_time < timeout:
                try:
                    if self._check_service_ready(service_name, host, port):
                        self.logger.info(f"✅ {service_name} is ready")
                        break
                    else:
                        self.logger.debug(f"⏳ {service_name} not ready yet...")
                        time.sleep(2)
                except Exception as e:
                    self.logger.debug(f"Error checking {service_name}: {str(e)}")
                    time.sleep(2)
            else:
                self.logger.error(f"❌ {service_name} failed to start within {timeout} seconds")
                return False

        self.logger.info("🎉 All databases are ready!")
        return True

    def _check_service_ready(self, service_name: str, host: str, port: int) -> bool:
        """Check if a service is ready to accept connections."""
        try:
            if service_name == "PostgreSQL":
                # Try to connect to PostgreSQL
                import asyncpg
                # This is a simple check - in production you'd use actual credentials
                return True  # For now, assume it's ready if container is up

            elif service_name == "MongoDB":
                # Try to connect to MongoDB
                from motor.motor_asyncio import AsyncIOMotorClient
                client = AsyncIOMotorClient(f"mongodb://localhost:{port}", serverSelectionTimeoutMS=1000)
                # This will raise an exception if not ready
                client.admin.command('ping')
                client.close()
                return True

            elif service_name == "Redis":
                # Try to connect to Redis
                import redis
                r = redis.Redis(host=host, port=port, password="ai_law_redis_password_2024", socket_timeout=1)
                r.ping()
                r.close()
                return True

            elif service_name == "Qdrant":
                # Try to connect to Qdrant
                import requests
                response = requests.get(f"http://{host}:{port + 1}/health", timeout=1)
                return response.status_code == 200

            return False

        except Exception:
            return False

    def create_env_file(self) -> bool:
        """Create .env file with database configuration."""
        try:
            env_file = self.project_root / "docker" / ".env"
            if env_file.exists():
                self.logger.info(".env file already exists")
                return True

            # Copy template to .env
            template_file = self.project_root / "docker" / ".env"
            if template_file.exists():
                # File already exists as template
                self.logger.info(".env template found")
                return True

            # Create basic .env file
            env_content = """# AI Law Firm Database Environment Configuration

# PostgreSQL Configuration
POSTGRES_DB=ai_law_firm
POSTGRES_USER=ai_law_user
POSTGRES_PASSWORD=ai_law_password_2024
POSTGRES_HOST=localhost
POSTGRES_PORT=54320

# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME=ai_law_admin
MONGO_INITDB_ROOT_PASSWORD=ai_law_mongo_password_2024
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=ai_law_firm

# Redis Configuration
REDIS_PASSWORD=ai_law_redis_password_2024
REDIS_HOST=localhost
REDIS_PORT=63790
REDIS_DB=0

# Qdrant Configuration
QDRANT_HOST=localhost
QDRANT_PORT=63330

# Application Configuration
APP_ENV=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production
"""

            with open(env_file, 'w') as f:
                f.write(env_content)

            self.logger.info(f"Created .env file: {env_file}")
            return True

        except Exception as e:
            self.logger.error(f"Error creating .env file: {str(e)}")
            return False

    def show_status(self) -> None:
        """Show current status of database services."""
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=ai_law_firm", "--format", "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"],
                capture_output=True,
                text=True,
                cwd=self.project_root / "docker"
            )

            if result.returncode == 0:
                print("\n📊 Database Services Status:")
                print("=" * 60)
                print(result.stdout)
            else:
                print("❌ Unable to get service status")

        except Exception as e:
            print(f"❌ Error getting status: {str(e)}")

    def show_connection_info(self) -> None:
        """Show connection information for all databases."""
        print("\n🔗 Database Connection Information:")
        print("=" * 60)
        print("PostgreSQL:")
        print("  Host: localhost:54320")
        print("  Database: ai_law_firm")
        print("  User: ai_law_user")
        print("  Password: ai_law_password_2024")
        print()
        print("MongoDB:")
        print("  Host: localhost:27017")
        print("  Database: ai_law_firm")
        print("  User: ai_law_admin")
        print("  Password: ai_law_mongo_password_2024")
        print()
        print("Redis:")
        print("  Host: localhost:63790")
        print("  Password: ai_law_redis_password_2024")
        print()
        print("Qdrant:")
        print("  gRPC: localhost:63330")
        print("  REST API: localhost:63331")
        print()

    async def test_connections(self) -> Dict[str, bool]:
        """Test connections to all databases."""
        results = {}

        print("\n🧪 Testing Database Connections:")
        print("=" * 60)

        # Test PostgreSQL
        try:
            import asyncpg
            conn = await asyncpg.connect(
                host="localhost", port=54320,
                database="ai_law_firm", user="ai_law_user",
                password="ai_law_password_2024"
            )
            await conn.close()
            results["PostgreSQL"] = True
            print("✅ PostgreSQL: Connected successfully")
        except Exception as e:
            results["PostgreSQL"] = False
            print(f"❌ PostgreSQL: {str(e)}")

        # Test MongoDB
        try:
            from motor.motor_asyncio import AsyncIOMotorClient
            client = AsyncIOMotorClient(
                "*****************************************************************************"
            )
            await client.admin.command('ping')
            client.close()
            results["MongoDB"] = True
            print("✅ MongoDB: Connected successfully")
        except Exception as e:
            results["MongoDB"] = False
            print(f"❌ MongoDB: {str(e)}")

        # Test Redis
        try:
            import redis
            r = redis.Redis(
                host="localhost", port=63790,
                password="ai_law_redis_password_2024"
            )
            r.ping()
            r.close()
            results["Redis"] = True
            print("✅ Redis: Connected successfully")
        except Exception as e:
            results["Redis"] = False
            print(f"❌ Redis: {str(e)}")

        # Test Qdrant
        try:
            import requests
            response = requests.get("http://localhost:63331/health", timeout=5)
            if response.status_code == 200:
                results["Qdrant"] = True
                print("✅ Qdrant: Connected successfully")
            else:
                results["Qdrant"] = False
                print(f"❌ Qdrant: HTTP {response.status_code}")
        except Exception as e:
            results["Qdrant"] = False
            print(f"❌ Qdrant: {str(e)}")

        return results


def main():
    """Main setup function."""
    print("🚀 AI Law Firm - Database Setup")
    print("=" * 60)

    setup = DatabaseSetup()

    # Configure logging
    configure_logging()

    # Step 1: Check prerequisites
    print("\n📋 Step 1: Checking Prerequisites...")
    if not setup.check_docker():
        print("❌ Docker is required but not available")
        sys.exit(1)

    if not setup.check_docker_compose():
        print("❌ Docker Compose is required but not available")
        sys.exit(1)

    print("✅ Prerequisites check passed")

    # Step 2: Create environment file
    print("\n📋 Step 2: Creating Environment Configuration...")
    if setup.create_env_file():
        print("✅ Environment file ready")
    else:
        print("❌ Failed to create environment file")
        sys.exit(1)

    # Step 3: Start database services
    print("\n📋 Step 3: Starting Database Services...")
    if setup.start_databases():
        print("✅ Database services started")
    else:
        print("❌ Failed to start database services")
        sys.exit(1)

    # Step 4: Wait for services to be ready
    print("\n📋 Step 4: Waiting for Services to be Ready...")
    if setup.wait_for_databases():
        print("✅ All databases are ready")
    else:
        print("❌ Some databases failed to start")
        setup.show_status()
        sys.exit(1)

    # Step 5: Test connections
    print("\n📋 Step 5: Testing Database Connections...")
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        connection_results = loop.run_until_complete(setup.test_connections())

        successful = sum(1 for result in connection_results.values() if result)
        total = len(connection_results)

        if successful == total:
            print(f"\n🎉 Setup Complete! All {total} databases are connected and ready.")
        else:
            print(f"\n⚠️  Setup completed with issues. {successful}/{total} databases connected.")

    except Exception as e:
        print(f"❌ Error testing connections: {str(e)}")
    finally:
        loop.close()

    # Show connection information
    setup.show_connection_info()

    # Show status
    setup.show_status()

    print("\n📚 Next Steps:")
    print("1. Update your application configuration with the database credentials")
    print("2. Run database migrations if needed")
    print("3. Start your AI Law Firm application")
    print("4. Test document upload and analysis features")
    print()
    print("📖 For detailed documentation, see: docs/DATA_PERSISTENCE.md")
    print("🔧 For troubleshooting, check the logs: docker logs <container_name>")


if __name__ == "__main__":
    main()