#!/usr/bin/env python3
"""
Simple database connection test for AI Law Firm.
"""

import asyncio
import sys
import os

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_databases():
    """Test connections to all databases."""
    print("🧪 Testing Database Connections")
    print("=" * 50)

    results = {}

    # Test PostgreSQL
    try:
        import asyncpg
        conn = await asyncpg.connect(
            host="localhost", port=54320,
            database="ai_law_firm", user="ai_law_user",
            password="ai_law_password_2024"
        )
        await conn.close()
        results["PostgreSQL"] = "✅ Connected successfully"
        print("✅ PostgreSQL: Connected successfully")
    except Exception as e:
        results["PostgreSQL"] = f"❌ Failed: {str(e)}"
        print(f"❌ PostgreSQL: {str(e)}")

    # Test MongoDB
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
        client = AsyncIOMotorClient(
            "*****************************************************************************"
        )
        await client.admin.command('ping')
        client.close()
        results["MongoDB"] = "✅ Connected successfully"
        print("✅ MongoDB: Connected successfully")
    except Exception as e:
        results["MongoDB"] = f"❌ Failed: {str(e)}"
        print(f"❌ MongoDB: {str(e)}")

    # Test Redis
    try:
        import redis
        r = redis.Redis(
            host="localhost", port=63790,
            password="ai_law_redis_password_2024"
        )
        r.ping()
        r.close()
        results["Redis"] = "✅ Connected successfully"
        print("✅ Redis: Connected successfully")
    except Exception as e:
        results["Redis"] = f"❌ Failed: {str(e)}"
        print(f"❌ Redis: {str(e)}")

    # Test Qdrant
    try:
        import requests
        response = requests.get("http://localhost:63331/health", timeout=5)
        if response.status_code == 200:
            results["Qdrant"] = "✅ Connected successfully"
            print("✅ Qdrant: Connected successfully")
        else:
            results["Qdrant"] = f"❌ HTTP {response.status_code}"
            print(f"❌ Qdrant: HTTP {response.status_code}")
    except Exception as e:
        results["Qdrant"] = f"❌ Failed: {str(e)}"
        print(f"❌ Qdrant: {str(e)}")

    # Summary
    print("\n" + "=" * 50)
    successful = sum(1 for result in results.values() if "✅" in result)
    total = len(results)

    if successful == total:
        print(f"🎉 SUCCESS: All {total} databases are connected and ready!")
        print("\n🚀 Your AI Law Firm is ready to use!")
        print("📊 Every document, question, and answer will be captured automatically")
    else:
        print(f"⚠️  PARTIAL: {successful}/{total} databases connected")
        print("Some databases may need additional configuration")

    print("\n🔗 Connection Details:")
    print("PostgreSQL: localhost:54320 (ai_law_firm)")
    print("MongoDB:    localhost:27019 (ai_law_firm)")
    print("Redis:      localhost:63790 (password protected)")
    print("Qdrant:     localhost:63330 (gRPC) / 63331 (REST)")

    return results

if __name__ == "__main__":
    asyncio.run(test_databases())