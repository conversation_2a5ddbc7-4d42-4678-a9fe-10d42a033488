"""
Unit tests for configuration management system.
"""
import pytest
import os
from pathlib import Path
from unittest.mock import patch, mock_open

from core.config.settings import (
    AppConfig, Environment, AIProvider, VectorDatabase,
    OpenAIConfig, OllamaConfig, QdrantCloudConfig, QdrantLocalConfig,
    load_config, get_config
)


class TestAppConfig:
    """Test cases for AppConfig class."""

    def test_default_config_creation(self, monkeypatch):
        """Test creating configuration with default values."""
        # Skip validation for testing
        monkeypatch.setenv("SKIP_CONFIG_VALIDATION", "true")
        config = AppConfig()

        assert config.environment == Environment.DEVELOPMENT
        assert config.debug is False
        assert config.ai_provider == AIProvider.OPENAI
        assert config.vector_database == VectorDatabase.QDRANT_CLOUD

    def test_config_with_openai_provider(self):
        """Test configuration with OpenAI provider."""
        config = AppConfig(
            ai_provider=AIProvider.OPENAI,
            openai=OpenAIConfig(api_key="test-key")
        )

        assert config.ai_provider == AIProvider.OPENAI
        assert config.openai is not None
        assert config.openai.api_key == "test-key"

    def test_config_with_ollama_provider(self):
        """Test configuration with Ollama provider."""
        config = AppConfig(
            ai_provider=AIProvider.OLLAMA,
            ollama=OllamaConfig()
        )

        assert config.ai_provider == AIProvider.OLLAMA
        assert config.ollama is not None
        assert config.ollama.base_url == "http://localhost:11434"

    def test_config_validation_openai_required(self):
        """Test that OpenAI config is required when using OpenAI provider."""
        with pytest.raises(ValueError, match="OpenAI configuration required"):
            AppConfig(ai_provider=AIProvider.OPENAI)

    def test_config_validation_ollama_required(self):
        """Test that Ollama config is required when using Ollama provider."""
        with pytest.raises(ValueError, match="Ollama configuration required"):
            AppConfig(ai_provider=AIProvider.OLLAMA)

    def test_production_config_validation(self):
        """Test production environment validation."""
        with pytest.raises(ValueError, match="Debug mode should not be enabled"):
            AppConfig(
                environment=Environment.PRODUCTION,
                debug=True
            )

    def test_config_to_dict_conversion(self):
        """Test converting configuration to dictionary."""
        config = AppConfig()
        config_dict = config.to_dict()

        assert isinstance(config_dict, dict)
        assert config_dict["environment"] == "development"
        assert config_dict["ai_provider"] == "openai"

    def test_config_from_dict_creation(self):
        """Test creating configuration from dictionary."""
        config_dict = {
            "environment": "production",
            "debug": False,
            "ai_provider": "ollama",
            "ollama": {
                "base_url": "http://localhost:11434",
                "model": "llama3.2:3b"
            }
        }

        config = AppConfig.from_dict(config_dict)

        assert config.environment == Environment.PRODUCTION
        assert config.ai_provider == AIProvider.OLLAMA
        assert config.ollama is not None
        assert config.ollama.base_url == "http://localhost:11434"


class TestOpenAIConfig:
    """Test cases for OpenAI configuration."""

    def test_valid_openai_config(self):
        """Test creating valid OpenAI configuration."""
        config = OpenAIConfig(
            api_key="test-key",
            model="gpt-4o",
            temperature=0.7
        )

        assert config.api_key == "test-key"
        assert config.model == "gpt-4o"
        assert config.temperature == 0.7

    def test_openai_config_validation(self):
        """Test OpenAI configuration validation."""
        # Test missing API key
        with pytest.raises(ValueError, match="OpenAI API key is required"):
            OpenAIConfig(api_key="")

        # Test invalid temperature
        with pytest.raises(ValueError, match="Temperature must be between 0 and 2"):
            OpenAIConfig(api_key="test-key", temperature=2.5)


class TestOllamaConfig:
    """Test cases for Ollama configuration."""

    def test_valid_ollama_config(self):
        """Test creating valid Ollama configuration."""
        config = OllamaConfig(
            base_url="http://localhost:11434",
            model="llama3.2:3b",
            temperature=0.8
        )

        assert config.base_url == "http://localhost:11434"
        assert config.model == "llama3.2:3b"
        assert config.temperature == 0.8

    def test_ollama_config_validation(self):
        """Test Ollama configuration validation."""
        # Test missing base URL
        with pytest.raises(ValueError, match="Ollama base URL is required"):
            OllamaConfig(base_url="")

        # Test invalid temperature
        with pytest.raises(ValueError, match="Temperature must be between 0 and 2"):
            OllamaConfig(base_url="http://localhost:11434", temperature=-0.1)


class TestQdrantCloudConfig:
    """Test cases for Qdrant Cloud configuration."""

    def test_valid_qdrant_cloud_config(self):
        """Test creating valid Qdrant Cloud configuration."""
        config = QdrantCloudConfig(
            url="https://test.cloud.qdrant.io",
            api_key="test-key",
            collection="test-collection"
        )

        assert config.url == "https://test.cloud.qdrant.io"
        assert config.api_key == "test-key"
        assert config.collection == "test-collection"

    def test_qdrant_cloud_config_validation(self):
        """Test Qdrant Cloud configuration validation."""
        # Test missing URL
        with pytest.raises(ValueError, match="Qdrant URL is required"):
            QdrantCloudConfig(url="", api_key="test-key")

        # Test missing API key
        with pytest.raises(ValueError, match="Qdrant API key is required"):
            QdrantCloudConfig(url="https://test.cloud.qdrant.io", api_key="")


class TestQdrantLocalConfig:
    """Test cases for Qdrant Local configuration."""

    def test_valid_qdrant_local_config(self):
        """Test creating valid Qdrant Local configuration."""
        config = QdrantLocalConfig(
            url="http://localhost:6333",
            collection="test-collection"
        )

        assert config.url == "http://localhost:6333"
        assert config.collection == "test-collection"

    def test_qdrant_local_config_validation(self):
        """Test Qdrant Local configuration validation."""
        # Test missing URL
        with pytest.raises(ValueError, match="Qdrant URL is required"):
            QdrantLocalConfig(url="")


class TestLoadConfig:
    """Test cases for configuration loading."""

    @patch.dict(os.environ, {"APP_ENV": "testing"})
    def test_load_config_from_environment(self):
        """Test loading configuration from environment variables."""
        config = load_config()

        assert config.environment == Environment.TESTING

    @patch("builtins.open", new_callable=mock_open, read_data="""
environment: production
debug: false
ai_provider: openai
openai:
  api_key: test-key
  model: gpt-4o
""")
    @patch("pathlib.Path.exists", return_value=True)
    def test_load_config_from_file(self, mock_exists, mock_file):
        """Test loading configuration from YAML file."""
        config = load_config("test_config.yaml")

        assert config.environment == Environment.PRODUCTION
        assert config.debug is False
        assert config.ai_provider == AIProvider.OPENAI
        assert config.openai is not None
        assert config.openai.api_key == "test-key"

    def test_get_config_singleton(self):
        """Test that get_config returns singleton instance."""
        config1 = get_config()
        config2 = get_config()

        assert config1 is config2


class TestEnvironmentDetection:
    """Test cases for environment detection."""

    @patch.dict(os.environ, {"APP_ENV": "production"})
    def test_environment_from_env_var(self):
        """Test environment detection from environment variable."""
        config = load_config()
        assert config.environment == Environment.PRODUCTION

    @patch.dict(os.environ, {}, clear=True)
    def test_default_environment(self):
        """Test default environment when no env var is set."""
        config = load_config()
        assert config.environment == Environment.DEVELOPMENT


if __name__ == "__main__":
    pytest.main([__file__])