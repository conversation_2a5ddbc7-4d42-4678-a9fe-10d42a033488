"""
Shared test configuration, fixtures, and utilities for AI Law Firm test suite.

This module provides:
- Pytest fixtures for common test objects
- Mock objects and factories
- Test data generators
- Configuration helpers
- Cleanup utilities
"""
import pytest
import tempfile
import shutil
import os
import json
from pathlib import Path
from unittest.mock import Mock, MagicMock, AsyncMock
from typing import Dict, Any, Optional, Generator, List
import sys

# Add src to Python path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.config.settings import (
    AppConfig, Environment, AIProvider, VectorDatabase,
    OpenAIConfig, OllamaConfig, QdrantCloudConfig, QdrantLocalConfig,
    DocumentConfig, LoggingConfig, SecurityConfig
)
from core.config.environment import EnvironmentDetector
from core.agents.base import Agent, AgentResponse, AgentContext, AgentRole, AgentCapability
from utils.logging import get_logger, configure_logging
from utils.exceptions import AILawFirmError, ErrorContext


# Test Configuration Fixtures
@pytest.fixture(scope="session")
def test_config_path(tmp_path_factory) -> Path:
    """Create a temporary test configuration file."""
    config_dir = tmp_path_factory.mktemp("config")
    return config_dir / "test.yaml"


@pytest.fixture(scope="session")
def test_config(test_config_path: Path) -> AppConfig:
    """Create test configuration."""
    config = AppConfig(
        environment=Environment.TESTING,
        debug=True,
        ai_provider=AIProvider.OPENAI,
        vector_database=VectorDatabase.QDRANT_LOCAL,
        openai=OpenAIConfig(api_key="test-openai-key"),
        qdrant_local=QdrantLocalConfig(),
        document=DocumentConfig(max_size_mb=10),  # Smaller for testing
        logging=LoggingConfig(level="DEBUG"),
        security=SecurityConfig(secret_key="test-secret-key-that-is-at-least-32-characters-long-for-testing")
    )

    # Save config to file
    import yaml
    with open(test_config_path, "w") as f:
        yaml.dump(config.to_dict(), f)

    return config


@pytest.fixture(scope="session")
def test_env_vars():
    """Set up test environment variables."""
    original_env = dict(os.environ)

    # Set test environment variables
    test_env = {
        "APP_ENV": "testing",
        "DEBUG": "true",
        "OPENAI_API_KEY": "test-openai-key",
        "QDRANT_URL": "http://localhost:6333",
        "LOG_LEVEL": "DEBUG",
        "SECRET_KEY": "test-secret-key-32-chars-long",
        "PYTEST_CURRENT_TEST": "true"  # Mark as test environment
    }

    os.environ.update(test_env)

    yield test_env

    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


# Mock AI Provider Fixtures
@pytest.fixture
def mock_openai_provider():
    """Create mock OpenAI provider."""
    provider = Mock()
    provider.generate_response = Mock(return_value=AgentResponse(
        content="Mock OpenAI response for testing",
        confidence_score=0.85,
        metadata={"model": "gpt-4o", "tokens": 150},
        sources=[],
        processing_time=1.2,
        model_used="gpt-4o"
    ))
    provider.is_available = Mock(return_value=True)
    provider.get_model_info = Mock(return_value={
        "name": "gpt-4o",
        "provider": "openai",
        "context_window": 8192
    })
    return provider


@pytest.fixture
def mock_ollama_provider():
    """Create mock Ollama provider."""
    provider = Mock()
    provider.generate_response = Mock(return_value=AgentResponse(
        content="Mock Ollama response for testing",
        confidence_score=0.78,
        metadata={"model": "llama3.2:3b", "tokens": 120},
        sources=[],
        processing_time=2.1,
        model_used="llama3.2:3b"
    ))
    provider.is_available = Mock(return_value=True)
    provider.get_model_info = Mock(return_value={
        "name": "llama3.2:3b",
        "provider": "ollama",
        "context_window": 4096
    })
    return provider


@pytest.fixture
def mock_ai_provider(mock_openai_provider):
    """Default mock AI provider (OpenAI)."""
    return mock_openai_provider


# Mock Vector Database Fixtures
@pytest.fixture
def mock_qdrant_vector_db():
    """Create mock Qdrant vector database."""
    vector_db = Mock()
    vector_db.search = Mock(return_value=[
        {
            "id": "doc1",
            "content": "Sample legal document content for testing",
            "metadata": {"type": "contract", "filename": "test_contract.pdf"},
            "score": 0.95
        },
        {
            "id": "doc2",
            "content": "Another sample document for comprehensive testing",
            "metadata": {"type": "brief", "filename": "test_brief.pdf"},
            "score": 0.87
        }
    ])
    vector_db.store = Mock(return_value=True)
    vector_db.retrieve = Mock(return_value={
        "id": "doc1",
        "content": "Retrieved document content",
        "metadata": {"type": "contract"}
    })
    vector_db.is_available = Mock(return_value=True)
    return vector_db


@pytest.fixture
def mock_knowledge_base(mock_qdrant_vector_db):
    """Create mock knowledge base."""
    kb = Mock()
    kb.search = mock_qdrant_vector_db.search
    kb.store = mock_qdrant_vector_db.store
    kb.retrieve = mock_qdrant_vector_db.retrieve
    kb.vector_db = mock_qdrant_vector_db
    return kb


# Mock Agent Fixtures
@pytest.fixture
def mock_agent_response():
    """Create mock agent response."""
    return AgentResponse(
        content="This is a comprehensive analysis of the legal document provided.",
        confidence_score=0.92,
        metadata={
            "model": "gpt-4o",
            "tokens_used": 450,
            "processing_time": 2.3
        },
        sources=[
            {"title": "Contract Law Principles", "type": "reference"},
            {"title": "Legal Precedent Database", "type": "database"}
        ],
        processing_time=2.3,
        model_used="gpt-4o"
    )


@pytest.fixture
def mock_agent_context():
    """Create mock agent context."""
    return AgentContext(
        session_id="test-session-123",
        user_id="test-user-456",
        document_ids=["doc1", "doc2"],
        query="Analyze this contract for potential risks",
        metadata={"analysis_type": "risk_assessment"}
    )


@pytest.fixture
def mock_legal_researcher(mock_ai_provider, mock_knowledge_base):
    """Create mock legal researcher agent."""
    from core.agents.base import Agent

    agent = Mock(spec=Agent)
    agent.name = "Legal Researcher"
    agent.role = AgentRole.LEGAL_RESEARCHER
    agent.capabilities = [AgentCapability.LEGAL_RESEARCH, AgentCapability.DOCUMENT_ANALYSIS]
    agent.ai_provider = mock_ai_provider
    agent.knowledge_base = mock_knowledge_base
    agent.is_initialized = True

    # Mock methods
    agent.process_query = AsyncMock(return_value=AgentResponse(
        content="Legal research findings: Based on case law analysis...",
        confidence_score=0.88,
        metadata={"citations_found": 5, "statutes_referenced": 3},
        sources=[{"type": "case_law"}, {"type": "statute"}],
        processing_time=1.8
    ))
    agent.can_handle_query = Mock(return_value=True)
    agent.get_capabilities = Mock(return_value=agent.capabilities)
    agent.health_check = AsyncMock(return_value={
        "healthy": True,
        "response_time": 1.2,
        "last_check": "2024-01-15T10:30:00Z"
    })

    return agent


@pytest.fixture
def mock_contract_analyst(mock_ai_provider, mock_knowledge_base):
    """Create mock contract analyst agent."""
    from core.agents.base import Agent

    agent = Mock(spec=Agent)
    agent.name = "Contract Analyst"
    agent.role = AgentRole.CONTRACT_ANALYST
    agent.capabilities = [AgentCapability.CONTRACT_REVIEW, AgentCapability.RISK_ASSESSMENT]
    agent.ai_provider = mock_ai_provider
    agent.knowledge_base = mock_knowledge_base
    agent.is_initialized = True

    agent.process_query = AsyncMock(return_value=AgentResponse(
        content="Contract analysis: Key terms identified, potential risks assessed...",
        confidence_score=0.91,
        metadata={"clauses_analyzed": 12, "risks_identified": 3},
        sources=[{"type": "contract_clause"}, {"type": "risk_analysis"}],
        processing_time=2.1
    ))
    agent.can_handle_query = Mock(return_value=True)
    agent.get_capabilities = Mock(return_value=agent.capabilities)

    return agent


@pytest.fixture
def mock_legal_strategist(mock_ai_provider, mock_knowledge_base):
    """Create mock legal strategist agent."""
    from core.agents.base import Agent

    agent = Mock(spec=Agent)
    agent.name = "Legal Strategist"
    agent.role = AgentRole.LEGAL_STRATEGIST
    agent.capabilities = [AgentCapability.STRATEGY_DEVELOPMENT, AgentCapability.RISK_ASSESSMENT]
    agent.ai_provider = mock_ai_provider
    agent.knowledge_base = mock_knowledge_base
    agent.is_initialized = True

    agent.process_query = AsyncMock(return_value=AgentResponse(
        content="Strategic recommendations: Based on comprehensive risk analysis...",
        confidence_score=0.89,
        metadata={"strategies_proposed": 4, "risk_mitigation_steps": 6},
        sources=[{"type": "strategy_framework"}, {"type": "risk_mitigation"}],
        processing_time=2.5
    ))
    agent.can_handle_query = Mock(return_value=True)
    agent.get_capabilities = Mock(return_value=agent.capabilities)

    return agent


@pytest.fixture
def mock_agent_team(mock_legal_researcher, mock_contract_analyst, mock_legal_strategist):
    """Create mock agent team."""
    from core.agents.base import TeamCoordinator

    team = Mock(spec=TeamCoordinator)
    team.name = "Legal Analysis Team"
    team.team_members = [mock_legal_researcher, mock_contract_analyst, mock_legal_strategist]
    team.coordination_history = []

    team.process_query = AsyncMock(return_value=AgentResponse(
        content="Coordinated team analysis: Comprehensive legal assessment completed...",
        confidence_score=0.94,
        metadata={
            "agents_contributed": 3,
            "analysis_types": ["research", "contract_review", "strategy"],
            "coordination_time": 1.2
        },
        sources=[
            {"type": "research_findings"},
            {"type": "contract_analysis"},
            {"type": "strategic_recommendations"}
        ],
        processing_time=5.8
    ))

    team.delegate_task = AsyncMock(return_value={
        "assigned_agents": [mock_legal_researcher, mock_contract_analyst],
        "task_breakdown": "Split analysis into research and contract review phases"
    })

    team.synthesize_responses = AsyncMock(return_value=AgentResponse(
        content="Synthesized analysis from all team members...",
        confidence_score=0.94,
        metadata={"synthesis_method": "weighted_consensus"},
        sources=[],
        processing_time=1.0
    ))

    return team


# Document Fixtures
@pytest.fixture
def sample_contract_content():
    """Sample contract content for testing."""
    return """
    EMPLOYMENT AGREEMENT

    This Employment Agreement (the "Agreement") is entered into as of January 1, 2024,
    by and between TechCorp Inc. (the "Company") and John Doe (the "Employee").

    1. POSITION AND DUTIES
    Employee shall serve as Senior Software Engineer and shall perform duties
    commensurate with such position as reasonably assigned by the Company.

    2. COMPENSATION
    Employee shall receive an annual base salary of $120,000, payable bi-weekly.
    Employee shall also be eligible for performance bonuses.

    3. TERMINATION
    Either party may terminate this Agreement with thirty (30) days written notice.
    In the event of termination without cause, Employee shall receive severance pay.

    4. CONFIDENTIALITY
    Employee agrees to maintain the confidentiality of Company proprietary information
    during and after employment.

    5. NON-COMPETE
    During employment and for one year thereafter, Employee shall not engage in
    competing business activities.

    6. GOVERNING LAW
    This Agreement shall be governed by the laws of the State of California.

    IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first above written.

    TechCorp Inc.                                John Doe
    By: /s/ Jane Smith                         /s/ John Doe
    Name: Jane Smith                           Date: January 1, 2024
    Title: CEO                                 Date: January 1, 2024
    """


@pytest.fixture
def sample_legal_brief():
    """Sample legal brief content for testing."""
    return """
    IN THE UNITED STATES DISTRICT COURT
    FOR THE NORTHERN DISTRICT OF CALIFORNIA

    JOHN DOE,                                Case No. 24-CV-01234
                                            Plaintiff,

    v.

    TECHCORP INC.,                          DEFENDANT'S MEMORANDUM IN
                                            SUPPORT OF MOTION TO DISMISS

    Defendant TechCorp Inc., by and through undersigned counsel, hereby submits
    this Memorandum in Support of its Motion to Dismiss Plaintiff's Complaint.

    I. INTRODUCTION

    Plaintiff John Doe brings this action alleging wrongful termination and
    breach of employment contract. Defendant respectfully submits that Plaintiff's
    claims fail as a matter of law and should be dismissed pursuant to Federal
    Rule of Civil Procedure 12(b)(6).

    II. BACKGROUND

    Plaintiff was employed by Defendant as a Senior Software Engineer from
    January 1, 2022, until his termination on December 31, 2023. Plaintiff's
    employment was governed by a written Employment Agreement dated January 1, 2022.

    III. ARGUMENT

    A. Plaintiff's Wrongful Termination Claim Fails

    Under California law, employment is at-will absent a contrary agreement.
    (Guz v. Bechtel National, Inc. (2000) 24 Cal.4th 317, 337). The Employment
    Agreement expressly provides that either party may terminate the agreement
    with 30 days' notice, confirming at-will employment.

    B. Plaintiff's Breach of Contract Claim is Without Merit

    Plaintiff alleges that Defendant breached the Employment Agreement by failing
    to provide severance pay. However, the Agreement's termination provision
    does not require severance pay for termination with notice.

    IV. CONCLUSION

    For the foregoing reasons, Defendant's Motion to Dismiss should be granted.

    Respectfully submitted,

    SMITH & ASSOCIATES
    By: /s/ Robert Smith
    Robert Smith, Esq.
    Attorneys for Defendant TechCorp Inc.
    """


@pytest.fixture
def temp_dir():
    """Create temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def sample_pdf_path(temp_dir, sample_contract_content):
    """Create sample PDF file for testing."""
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    import io

    pdf_path = os.path.join(temp_dir, "sample_contract.pdf")

    # Create PDF with sample content
    buffer = io.BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    width, height = letter

    # Add content to PDF
    y_position = height - 50
    for line in sample_contract_content.split('\n')[:30]:  # Limit content
        if y_position < 50:
            c.showPage()
            y_position = height - 50
        c.drawString(50, y_position, line.strip())
        y_position -= 15

    c.save()

    # Write to file
    with open(pdf_path, "wb") as f:
        f.write(buffer.getvalue())

    return pdf_path


# Error and Exception Fixtures
@pytest.fixture
def sample_error_context():
    """Create sample error context for testing."""
    return ErrorContext(
        operation="document_processing",
        component="document_processor",
        user_id="test-user-123",
        session_id="test-session-456",
        document_id="doc-789",
        additional_data={"file_size": 1024000, "file_type": "pdf"}
    )


@pytest.fixture
def mock_exception():
    """Create mock exception for testing."""
    return AILawFirmError(
        message="Test error for unit testing",
        error_code="TEST_ERROR",
        context=ErrorContext(
            operation="test_operation",
            component="test_component"
        )
    )


# Logging Fixtures
@pytest.fixture
def test_logger():
    """Create test logger."""
    logger = get_logger("test_logger")
    # Clear any existing handlers to avoid conflicts
    logger.handlers.clear()
    return logger


@pytest.fixture(autouse=True)
def setup_test_logging(test_config):
    """Setup logging for tests."""
    configure_logging(test_config.logging)


# Async Test Utilities
@pytest.fixture
def event_loop():
    """Create event loop for async tests."""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Performance Testing Fixtures
@pytest.fixture
def performance_monitor():
    """Create performance monitor for tests."""
    class PerformanceMonitor:
        def __init__(self):
            self.measurements = []

        def measure(self, operation: str):
            import time
            start_time = time.time()
            try:
                yield
            finally:
                end_time = time.time()
                duration = end_time - start_time
                self.measurements.append({
                    "operation": operation,
                    "duration": duration,
                    "timestamp": end_time
                })

        def get_measurements(self):
            return self.measurements

        def get_average_duration(self, operation: str):
            measurements = [m for m in self.measurements if m["operation"] == operation]
            if not measurements:
                return 0
            return sum(m["duration"] for m in measurements) / len(measurements)

    return PerformanceMonitor()


# Test Data Generators
@pytest.fixture
def generate_test_contract():
    """Generate test contract data."""
    def _generate_contract(variations: Optional[Dict[str, Any]] = None) -> str:
        base_contract = """
        EMPLOYMENT AGREEMENT

        This Agreement is between {company} and {employee}.

        1. Position: {position}
        2. Salary: ${salary:,}
        3. Term: {term} months
        4. Location: {location}
        """

        defaults = {
            "company": "TestCorp Inc.",
            "employee": "Test Employee",
            "position": "Software Engineer",
            "salary": 100000,
            "term": 12,
            "location": "San Francisco, CA"
        }

        if variations:
            defaults.update(variations)

        return base_contract.format(**defaults)

    return _generate_contract


@pytest.fixture
def generate_test_query():
    """Generate test queries for different analysis types."""
    def _generate_query(query_type: str, **kwargs) -> str:
        query_templates = {
            "contract_review": "Please review this {document_type} and identify key terms, obligations, and potential issues.",
            "legal_research": "Research relevant {topic} cases and precedents related to this matter.",
            "risk_assessment": "Analyze potential legal risks and liabilities in this {document_type}.",
            "compliance_check": "Check this document for compliance with {regulation} requirements.",
            "custom": "{custom_query}"
        }

        template = query_templates.get(query_type, query_templates["custom"])
        return template.format(**kwargs)

    return _generate_query


# Cleanup Fixtures
@pytest.fixture(autouse=True)
def cleanup_test_artifacts():
    """Clean up test artifacts after each test."""
    yield
    # Add cleanup logic here if needed
    # For example, clean up temporary files, reset global state, etc.


# Test Configuration Helpers
def pytest_configure(config):
    """Configure pytest with custom markers and settings."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "ui: UI tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "e2e: End-to-end tests")
    config.addinivalue_line("markers", "slow: Slow tests")
    config.addinivalue_line("markers", "external: External service tests")
    config.addinivalue_line("markers", "ai: AI provider tests")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "ui" in str(item.fspath):
            item.add_marker(pytest.mark.ui)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)


# Export commonly used test utilities
__all__ = [
    "test_config",
    "test_env_vars",
    "mock_openai_provider",
    "mock_ollama_provider",
    "mock_ai_provider",
    "mock_qdrant_vector_db",
    "mock_knowledge_base",
    "mock_agent_response",
    "mock_agent_context",
    "mock_legal_researcher",
    "mock_contract_analyst",
    "mock_legal_strategist",
    "mock_agent_team",
    "sample_contract_content",
    "sample_legal_brief",
    "temp_dir",
    "sample_pdf_path",
    "sample_error_context",
    "mock_exception",
    "test_logger",
    "performance_monitor",
    "generate_test_contract",
    "generate_test_query",
]