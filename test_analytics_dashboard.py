#!/usr/bin/env python3
"""
Test script for the Real-Time Analytics Dashboard.

This script demonstrates the analytics dashboard functionality
and provides sample data visualization.
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.analytics.dashboard import AnalyticsDashboard, get_dashboard_overview


async def test_analytics_dashboard():
    """Test the analytics dashboard functionality."""
    print("📊 Testing Analytics Dashboard")
    print("=" * 60)

    # Database configuration
    db_config = {
        'postgres_host': 'localhost',
        'postgres_port': 54320,
        'postgres_db': 'ai_law_firm',
        'postgres_user': 'ai_law_user',
        'postgres_password': 'ai_law_password_2024',
        'redis_host': 'localhost',
        'redis_port': 63790,
        'redis_password': 'ai_law_redis_password_2024',
        'mongo_host': 'localhost',
        'mongo_port': 27019
    }

    # Initialize dashboard
    dashboard = AnalyticsDashboard(db_config)

    if not await dashboard.initialize():
        print("❌ Failed to initialize dashboard")
        return

    try:
        # Get comprehensive dashboard overview
        print("Fetching dashboard overview...")
        overview = await dashboard.get_dashboard_overview()

        if "error" in overview:
            print(f"❌ Dashboard error: {overview['error']}")
            return

    except Exception as e:
        print(f"❌ Error during dashboard test: {str(e)}")
        return

        # Display results
        print("\n🎯 DASHBOARD OVERVIEW")
        print("=" * 60)

        # Summary statistics
        summary = overview.get('summary', {})
        print("📈 SUMMARY STATISTICS:")
        print(f"   • Total Documents: {summary.get('total_documents', 0)}")
        print(f"   • Active Users: {summary.get('active_users', 0)}")
        print(f"   • AI Requests Today: {summary.get('ai_requests_today', 0)}")
        print(f"   • System Health Score: {summary.get('system_health_score', 0)}%")
        print(f"   • Overall System Score: {summary.get('overall_system_score', 0)}%")

        # Document metrics
        documents = overview.get('documents', {})
        print("\n📄 DOCUMENT METRICS:")
        print(f"   • Total Documents: {documents.get('total_documents', 0)}")
        print(f"   • Recent Uploads (7d): {documents.get('recent_uploads_7d', 0)}")
        print(f"   • Average File Size: {documents.get('average_file_size_kb', 0)} KB")

        if documents.get('document_types'):
            print("   • Top Document Types:")
            for doc_type in documents['document_types'][:3]:
                print(f"     - {doc_type['type']}: {doc_type['count']}")

        # User activity
        users = overview.get('users', {})
        print("\n👥 USER ACTIVITY:")
        print(f"   • Active Users: {users.get('active_users', 0)}")
        print(f"   • Total Uploads (30d): {users.get('total_uploads_30d', 0)}")
        print(f"   • Unique Documents (30d): {users.get('unique_documents_30d', 0)}")
        print(f"   • User Engagement Score: {users.get('user_engagement_score', 0)}")

        # AI usage
        ai_usage = overview.get('ai_usage', {})
        print("\n🤖 AI USAGE:")
        print(f"   • Total AI Requests: {ai_usage.get('total_ai_requests', 0)}")
        print(f"   • Total Tokens Used: {ai_usage.get('total_tokens_used', 0):,}")
        print(f"   • Average Response Time: {ai_usage.get('average_response_time_ms', 0)} ms")
        print(f"   • Estimated Cost: ${ai_usage.get('cost_estimate_usd', 0)}")
        print(f"   • Requests/Minute: {ai_usage.get('requests_per_minute', 0)}")

        # Database performance
        database = overview.get('database', {})
        print("\n🗄️ DATABASE PERFORMANCE:")
        print(f"   • PostgreSQL Connections: {database.get('postgres_connections', 0)}")
        print(f"   • Redis Memory Used: {database.get('redis_memory_used_mb', 0)} MB")
        print(f"   • Redis Operations/sec: {database.get('redis_operations_per_sec', 0)}")
        print(f"   • MongoDB Size: {database.get('mongodb_size_mb', 0)} MB")

        # System health
        system = overview.get('system', {})
        print("\n🏥 SYSTEM HEALTH:")
        print(f"   • Overall Health Score: {system.get('overall_health_score', 0)}%")
        print("   • Service Status:")
        for service, status in system.get('services', {}).items():
            status_icon = "✅" if status == "healthy" else "❌"
            print(f"     {status_icon} {service}: {status}")

        # Search analytics
        search = overview.get('search', {})
        print("\n🔍 SEARCH ANALYTICS:")
        print(f"   • Total Search Queries: {search.get('total_search_queries', 0)}")
        print(f"   • Average Search Time: {search.get('average_search_time_ms', 0)} ms")

        if search.get('popular_search_terms'):
            print("   • Popular Search Terms:")
            for term in search['popular_search_terms'][:3]:
                print(f"     - '{term['term']}': {term['count']} searches")

        # Save results to file
        output_file = "dashboard_results.json"
        with open(output_file, 'w') as f:
            json.dump(overview, f, indent=2, default=str)

        print(f"\n💾 Results saved to {output_file}")

        print("\n🎉 Analytics Dashboard Test Completed Successfully!")
        print("📊 Your AI Law Firm now has comprehensive real-time analytics!")

    except Exception as e:
        print(f"❌ Error during dashboard test: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        await dashboard.close()


async def test_individual_metrics():
    """Test individual metric functions."""
    print("\n🔧 Testing Individual Metrics")
    print("=" * 40)

    db_config = {
        'postgres_host': 'localhost',
        'postgres_port': 54320,
        'postgres_db': 'ai_law_firm',
        'postgres_user': 'ai_law_user',
        'postgres_password': 'ai_law_password_2024',
        'redis_host': 'localhost',
        'redis_port': 63790,
        'redis_password': 'ai_law_redis_password_2024',
        'mongo_host': 'localhost',
        'mongo_port': 27019
    }

    dashboard = AnalyticsDashboard(db_config)

    if not await dashboard.initialize():
        print("❌ Failed to initialize dashboard")
        return

    try:
        # Test document metrics
        print("Testing document metrics...")
        doc_metrics = await dashboard.get_document_metrics()
        print(f"✅ Document metrics: {len(doc_metrics)} data points")

        # Test user activity metrics
        print("Testing user activity metrics...")
        user_metrics = await dashboard.get_user_activity_metrics()
        print(f"✅ User metrics: {len(user_metrics)} data points")

        # Test AI usage metrics
        print("Testing AI usage metrics...")
        ai_metrics = await dashboard.get_ai_usage_metrics()
        print(f"✅ AI metrics: {len(ai_metrics)} data points")

        # Test database performance
        print("Testing database performance...")
        db_metrics = await dashboard.get_database_performance_metrics()
        print(f"✅ Database metrics: {len(db_metrics)} data points")

        # Test system health
        print("Testing system health...")
        health_metrics = await dashboard.get_system_health_metrics()
        print(f"✅ Health metrics: {len(health_metrics)} data points")

        print("\n✅ All individual metrics tests passed!")

    except Exception as e:
        print(f"❌ Error testing individual metrics: {str(e)}")

    finally:
        await dashboard.close()


async def main():
    """Main test function."""
    print("🚀 AI Law Firm Analytics Dashboard Test")
    print("=" * 60)

    # Test comprehensive dashboard
    await test_analytics_dashboard()

    # Test individual components
    await test_individual_metrics()

    print("\n🎊 All Analytics Dashboard Tests Completed!")
    print("📊 Your dashboard is ready for production use!")


if __name__ == "__main__":
    asyncio.run(main())