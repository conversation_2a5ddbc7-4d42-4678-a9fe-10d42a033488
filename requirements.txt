# Core Dependencies for AI Law Firm System
# These are the essential packages needed for the refactored system

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
streamlit==1.40.2

# AI and Machine Learning
agno>=0.1.0
openai>=1.0.0
ollama>=0.1.0
anthropic>=0.30.0
transformers>=4.30.0
torch>=2.0.0
sentence-transformers>=2.2.0
google-generativeai>=0.8.0
openrouter>=0.1.0

# Vector Databases
qdrant-client==1.12.1
chromadb>=0.4.0

# Document Processing
pypdf>=3.0.0
python-docx>=0.8.11
reportlab>=4.0.0
openpyxl>=3.1.0
xlrd>=2.0.1
beautifulsoup4>=4.12.0
markdown>=3.5.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# HTTP and Networking
requests>=2.31.0
httpx>=0.25.0
aiohttp>=3.9.0

# Configuration and Serialization
pyyaml>=6.0
pydantic>=2.0.0

# Utilities
python-dotenv>=1.0.0
click>=8.1.0

# Database Dependencies
asyncpg>=0.29.0
motor>=3.3.0
redis>=5.0.0
psycopg2-binary>=2.9.0
pymongo>=4.6.0
sqlalchemy>=2.0.0

# Security and Authentication
cryptography>=41.0.0
bcrypt>=4.0.0
passlib>=1.7.0
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6

# Logging and Monitoring
structlog>=23.1.0
rich>=13.0.0

# Development and Testing (also in requirements-dev.txt)
pytest>=7.4.0
pytest-cov>=4.1.0
