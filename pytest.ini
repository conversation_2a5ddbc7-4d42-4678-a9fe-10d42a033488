[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
pythonpath = src
addopts =
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=80
    --cov-report=xml
    -v
    --tb=short
    --disable-warnings
markers =
    unit: Unit tests that test individual components in isolation
    integration: Integration tests that test component interactions
    ui: User interface component tests
    performance: Performance and load testing
    e2e: End-to-end workflow tests
    slow: Slow-running tests that should be run separately
    external: Tests that require external services or APIs
    ai: Tests that interact with AI providers
    database: Tests that interact with databases
    async: Tests that use async/await
    mock: Tests that use extensive mocking
    real: Tests that use real services (use sparingly)
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
    error::Exception
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S