#!/usr/bin/env python3
"""
Test MongoDB authentication and setup.
"""

import asyncio
import sys
import os

async def test_mongodb_auth():
    """Test MongoDB authentication."""
    print("🔍 Testing MongoDB Authentication")
    print("=" * 50)

    try:
        from motor.motor_asyncio import AsyncIOMotorClient

        # Test 1: Connect without authentication
        print("Test 1: Connecting without authentication...")
        client = AsyncIOMotorClient("mongodb://localhost:27019/")
        await client.admin.command('ping')
        print("✅ Basic connection successful")

        # Test 2: Try to authenticate
        print("Test 2: Testing authentication...")
        try:
            client = AsyncIOMotorClient(
                "*****************************************************************************"
            )
            await client.admin.command('ping')
            print("✅ Authentication successful")

            # Test 3: List users
            print("Test 3: Listing users...")
            users = await client.admin.command('usersInfo')
            print(f"✅ Found {len(users.get('users', []))} users")

            # Test 4: Create test collection
            print("Test 4: Creating test collection...")
            db = client.ai_law_firm
            collection = db.test_collection
            result = await collection.insert_one({"test": "document", "timestamp": "2024-01-01"})
            print(f"✅ Document inserted with ID: {result.inserted_id}")

            # Clean up
            await collection.drop()
            print("✅ Test collection cleaned up")

            client.close()
            print("\n🎉 MongoDB authentication is working perfectly!")

        except Exception as e:
            print(f"❌ Authentication failed: {str(e)}")
            print("📝 MongoDB may need additional setup for authentication")
            client.close()

    except Exception as e:
        print(f"❌ Basic connection failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_mongodb_auth())