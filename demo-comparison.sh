#!/bin/bash

# AI Law Firm Document Comparison Demo
# This script demonstrates the WinMerge-style document comparison feature

echo "⚖️ AI Law Firm - Document Comparison Demo"
echo "=========================================="

# Check if backend is running
if ! curl -s http://localhost:8000/api/v1/health > /dev/null; then
    echo "❌ Backend is not running. Please start it first:"
    echo "   cd src && python main.py"
    exit 1
fi

echo "✅ Backend is running"

# Check if frontend is running
if ! curl -s http://localhost:3000 > /dev/null; then
    echo "⚠️  Frontend is not running. Start it with:"
    echo "   cd frontend && npm install && npm start"
    echo ""
fi

echo ""
echo "📄 Document Comparison Feature Overview:"
echo "=========================================="
echo ""
echo "🎯 What is Document Comparison?"
echo "   • Side-by-side document viewing (like WinMerge)"
echo "   • Highlighted differences with color coding"
echo "   • AI-powered analysis of changes"
echo "   • Line-by-line diff navigation"
echo "   • Split and unified view modes"
echo ""
echo "📊 How to Use:"
echo "=============="
echo ""
echo "1. 📤 Upload Two Documents:"
echo "   • Go to http://localhost:3000"
echo "   • Upload two legal documents (contracts, agreements, etc.)"
echo "   • Supported formats: PDF, DOCX, TXT, etc."
echo ""
echo "2. 🔍 Select Documents for Comparison:"
echo "   • In the chat interface, click the document selector"
echo "   • Choose exactly 2 documents"
echo "   • Look for the green 'Ready to compare!' message"
echo ""
echo "3. ⚖️ Start Comparison:"
echo "   • Click the green 'Compare' button in the header"
echo "   • View side-by-side differences"
echo "   • Navigate through changes with arrow buttons"
echo ""
echo "4. 🤖 AI Analysis:"
echo "   • Ask AI questions about the differences"
echo "   • Get legal analysis of changes"
echo "   • Understand implications of modifications"
echo ""
echo "🎨 Interface Features:"
echo "======================"
echo ""
echo "• 🔄 Split View: Side-by-side document comparison"
echo "• 📄 Unified View: Single-pane diff view"
echo "• 🎯 Line Numbers: Toggle line number display"
echo "• 📊 Statistics: Added/removed/modified line counts"
echo "• 🎨 Color Coding: Green (added), Red (removed), Yellow (modified)"
echo "• 🧠 AI Insights: Intelligent analysis of changes"
echo ""
echo "📋 Sample Use Cases:"
echo "===================="
echo ""
echo "1. 📝 Contract Review:"
echo "   • Compare original vs. amended contracts"
echo "   • Identify material changes"
echo "   • Assess legal implications"
echo ""
echo "2. 📋 Version Control:"
echo "   • Track document evolution"
echo "   • Review collaborative edits"
echo "   • Maintain change history"
echo ""
echo "3. 🔍 Due Diligence:"
echo "   • Compare similar agreements"
echo "   • Identify inconsistencies"
echo "   • Spot potential issues"
echo ""
echo "4. 📊 Compliance Checking:"
echo "   • Compare against templates"
echo "   • Verify standard clauses"
echo "   • Ensure regulatory compliance"
echo ""
echo "🚀 Advanced Features:"
echo "===================="
echo ""
echo "• 🎯 Smart Navigation: Jump to next/previous difference"
echo "• 📈 Similarity Scoring: Overall document similarity percentage"
echo "• 🧠 Contextual AI: Ask questions about specific changes"
echo "• 📱 Responsive Design: Works on desktop, tablet, mobile"
echo "• 🎨 Professional UI: Clean, legal-focused interface"
echo ""
echo "🔧 Technical Details:"
echo "====================="
echo ""
echo "• Backend API: POST /api/v1/compare/detailed"
echo "• Real-time diffing with line-by-line analysis"
echo "• AI-powered change analysis and insights"
echo "• Optimized for large legal documents"
echo "• Secure document handling and storage"
echo ""
echo "📞 Getting Help:"
echo "================"
echo ""
echo "• 📚 Documentation: Check frontend/README.md"
echo "• 🐛 Issues: Report bugs in GitHub issues"
echo "• 💬 Discussion: Join community discussions"
echo "• 📧 Support: Contact <EMAIL>"
echo ""
echo "🎉 Ready to Compare Documents!"
echo ""
echo "Visit: http://localhost:3000"
echo "API Docs: http://localhost:8000/docs"
echo ""
echo "Happy comparing! ⚖️📄🔍"