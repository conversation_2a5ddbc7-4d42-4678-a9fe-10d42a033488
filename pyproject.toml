[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-law-firm"
version = "1.0.0"
description = "AI-powered multi-agent legal document analysis system"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AI Law Firm Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "AI Law Firm Team", email = "<EMAIL>"}
]
keywords = [
    "ai", "legal", "document-analysis", "multi-agent",
    "machine-learning", "nlp", "law", "contracts"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Legal Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Office/Business :: Legal",
]
requires-python = ">=3.8"
dependencies = [
    "streamlit>=1.40.0",
    "agno>=0.1.0",
    "openai>=1.0.0",
    "qdrant-client>=1.12.0",
    "pypdf>=3.0.0",
    "pyyaml>=6.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "flake8>=6.0.0",
    "pre-commit>=3.4.0",
]
docs = [
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.1.0",
]
performance = [
    "pytest-benchmark>=4.0.0",
    "memory-profiler>=0.61.0",
    "psutil>=5.9.0",
]

[project.urls]
Homepage = "https://github.com/ai-law-firm/ai-law-firm"
Documentation = "https://ai-law-firm.readthedocs.io/"
Repository = "https://github.com/ai-law-firm/ai-law-firm"
"Bug Reports" = "https://github.com/ai-law-firm/ai-law-firm/issues"
"Source" = "https://github.com/ai-law-firm/ai-law-firm"

[project.scripts]
ai-law-firm = "src.ui.app:main"
configure-ai-law-firm = "src.core.config.settings:create_default_configs"

[tool.setuptools]
zip-safe = false
include-package-data = true

[tool.setuptools.packages.find]
where = ["src"]
exclude = ["tests*", "docs*", "scripts*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.txt"]

# Black code formatting
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | __pycache__
  | \.pytest_cache
  | htmlcov
  | node_modules
  | \.vscode
  | \.idea
)/
'''

# isort import sorting
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = [
    "streamlit",
    "agno",
    "openai",
    "qdrant_client",
    "pydantic",
    "yaml",
    "pytest",
]

# MyPy type checking
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "streamlit.*",
    "agno.*",
    "qdrant_client.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing",
    "--cov-fail-under=80",
    "-v",
    "--tb=short",
    "--disable-warnings",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "ui: UI component tests",
    "performance: Performance tests",
    "e2e: End-to-end tests",
    "slow: Slow-running tests",
    "external: Tests requiring external services",
    "ai: Tests interacting with AI providers",
    "database: Tests interacting with databases",
    "async: Tests using async/await",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning",
    "error::Exception",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/conftest.py",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\bProtocol\):",
    "@(abc\.)?abstractmethod",
]

# Flake8 linting
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
    ".venv",
    "venv",
    ".tox",
    ".pytest_cache",
    "htmlcov",
    "node_modules",
    ".vscode",
    ".idea",
]

# Bandit security linting
[tool.bandit]
exclude_dirs = ["tests", "docs", "scripts"]
skips = ["B101", "B601"]  # Skip assert checks and shell usage

# Pre-commit hooks
[tool.pre-commit]
repos = [
    {repo = "https://github.com/pre-commit/pre-commit-hooks", rev = "v4.4.0", hooks = [
        {id = "trailing-whitespace"},
        {id = "end-of-file-fixer"},
        {id = "check-yaml"},
        {id = "check-added-large-files"},
        {id = "check-merge-conflict"},
        {id = "debug-statements"},
    ]},
    {repo = "https://github.com/psf/black", rev = "23.7.0", hooks = [
        {id = "black"},
    ]},
    {repo = "https://github.com/pycqa/isort", rev = "5.12.0", hooks = [
        {id = "isort"},
    ]},
    {repo = "https://github.com/pycqa/flake8", rev = "6.0.0", hooks = [
        {id = "flake8"},
    ]},
    {repo = "https://github.com/pre-commit/mirrors-mypy", rev = "v1.5.1", hooks = [
        {id = "mypy"},
    ]},
]

# Version configuration
[tool.bumpver]
current_version = "1.0.0"
version_pattern = "MAJOR.MINOR.PATCH"
commit_message = "bump version {old_version} -> {new_version}"
commit = true
tag = true
push = false

[tool.bumpver.file_patterns]
"pyproject.toml" = [
    'version = "{version}"',
]
"src/__init__.py" = [
    '__version__ = "{version}"',
]
"README.md" = [
    "version = \"{version}\"",
]