#!/usr/bin/env python3
"""
Refactored AI Law Firm Application Entry Point.

This is the new, modular entry point for the AI Law Firm application,
replacing the monolithic legal_agent_team.py with a clean, maintainable architecture.

Key Improvements:
- Modular component architecture
- Service layer orchestration
- Comprehensive error handling
- Clean separation of concerns
- Extensible design patterns
"""

import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ui.app import LegalAnalysisApp
from core.config.settings import get_config
from utils.logging import configure_logging, get_logger


def main():
    """Main application entry point."""
    try:
        # Get configuration
        config = get_config()

        # Configure logging
        configure_logging(config.logging)

        # Get logger
        logger = get_logger(__name__)
        logger.info("Starting AI Law Firm Application (Refactored)")

        # Create and run application
        app = LegalAnalysisApp(config)
        app.run()

    except KeyboardInterrupt:
        print("\n👋 Application interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        logger = get_logger(__name__)
        logger.error(f"Fatal application error: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()