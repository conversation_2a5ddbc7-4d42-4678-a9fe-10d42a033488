#!/usr/bin/env python3
"""
<PERSON>ript to fix all relative imports in the project to use absolute imports.
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """Fix imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Replace 'from src.' with nothing (remove src.)
        original_content = content
        content = re.sub(r'from src\.', 'from ', content)

        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

    return False

def main():
    """Main function to fix all imports."""
    src_dir = Path('src')
    if not src_dir.exists():
        print("src directory not found!")
        return

    fixed_count = 0

    # Walk through all Python files in src directory
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                if fix_imports_in_file(file_path):
                    print(f"Fixed imports in: {file_path}")
                    fixed_count += 1

    # Also fix files in root directory that might import from src
    root_files = [
        'legal_agent_team_refactored.py',
        'test_45_legal_agents.py',
        'test_multi_model.py'
    ]

    for file in root_files:
        if Path(file).exists():
            if fix_imports_in_file(file):
                print(f"Fixed imports in: {file}")
                fixed_count += 1

    # Fix test files
    test_dir = Path('tests')
    if test_dir.exists():
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    if fix_imports_in_file(file_path):
                        print(f"Fixed imports in: {file_path}")
                        fixed_count += 1

    # Fix scripts
    scripts_dir = Path('scripts')
    if scripts_dir.exists():
        for root, dirs, files in os.walk(scripts_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    if fix_imports_in_file(file_path):
                        print(f"Fixed imports in: {file_path}")
                        fixed_count += 1

    print(f"\n✅ Fixed imports in {fixed_count} files")

if __name__ == "__main__":
    main()